<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
</head>
<body>
    <h1>API Test</h1>
    <button onclick="testLogin()">Test Login (Fetch)</button>
    <button onclick="testLoginXHR()">Test Login (XHR)</button>
    <button onclick="testCORS()">Test CORS</button>
    <div id="result"></div>

    <script>
        async function testLogin() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                console.log('Starting API test...');
                const response = await fetch('http://localhost:3001/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Origin': 'http://localhost:5177'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'Admin2025!Secure#'
                    })
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', [...response.headers.entries()]);
                
                const data = await response.json();
                console.log('Response data:', data);
                
                resultDiv.innerHTML = `
                    <h3>Success!</h3>
                    <p>Status: ${response.status}</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                console.error('Error:', error);
                resultDiv.innerHTML = `
                    <h3>Error!</h3>
                    <p>${error.message}</p>
                `;
            }
        }

        async function testLoginXHR() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing with XHR...';

            return new Promise((resolve, reject) => {
                const xhr = new XMLHttpRequest();
                xhr.open('POST', 'http://localhost:3001/api/auth/login', true);
                xhr.setRequestHeader('Content-Type', 'application/json');

                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        console.log('XHR Status:', xhr.status);
                        console.log('XHR Response:', xhr.responseText);

                        if (xhr.status === 200) {
                            const data = JSON.parse(xhr.responseText);
                            resultDiv.innerHTML = `
                                <h3>XHR Success!</h3>
                                <p>Status: ${xhr.status}</p>
                                <pre>${JSON.stringify(data, null, 2)}</pre>
                            `;
                        } else {
                            resultDiv.innerHTML = `
                                <h3>XHR Error!</h3>
                                <p>Status: ${xhr.status}</p>
                                <p>Response: ${xhr.responseText}</p>
                            `;
                        }
                    }
                };

                xhr.onerror = function() {
                    console.error('XHR Error');
                    resultDiv.innerHTML = '<h3>XHR Network Error!</h3>';
                };

                xhr.send(JSON.stringify({
                    email: '<EMAIL>',
                    password: 'Admin2025!Secure#'
                }));
            });
        }

        async function testCORS() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing CORS...';

            try {
                const response = await fetch('http://localhost:3001/health', {
                    method: 'GET',
                    headers: {
                        'Origin': 'http://localhost:5177'
                    }
                });

                console.log('CORS test status:', response.status);
                const data = await response.json();

                resultDiv.innerHTML = `
                    <h3>CORS Test Success!</h3>
                    <p>Status: ${response.status}</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                console.error('CORS Error:', error);
                resultDiv.innerHTML = `
                    <h3>CORS Test Error!</h3>
                    <p>${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
