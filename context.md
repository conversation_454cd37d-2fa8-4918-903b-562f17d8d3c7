# gAds Supercharge - Project Context

## Project Overview

gAds Supercharge is a comprehensive Google Ads automation and management platform built with React, TypeScript, and Vite. The application provides a suite of tools for campaign management, budget optimization, performance reporting, and script generation for Google Ads automation.

## Architecture

### Frontend Application
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite 5.4.2
- **Styling**: Tailwind CSS with PostCSS
- **Routing**: React Router DOM v7.6.0
- **UI Components**: Radix UI with custom components
- **Authentication**: CSV-based authentication system
- **State Management**: React Context API and hooks

### Key Technologies
- **React Helmet Async**: SEO and meta tag management
- **React Hook Form**: Form handling and validation
- **Lucide React**: Icon library
- **Canvas**: Chart and visualization support
- **Class Variance Authority**: Component styling utilities

## Core Features

### 1. Authentication System
**Location**: `src/utils/auth.ts`
- CSV-based user authentication (`/root.csv`)
- Role-based access control (admin, user roles)
- Persistent login with localStorage
- Protected routes for dashboard access

### 2. Dashboard and Tools
**Main Dashboard**: `src/components/Dashboard.tsx`
- User profile and account information
- Tool navigation and quick access
- Recent budget adjustments summary
- Role-based feature access

### 3. Google Ads Tools Suite

#### Budget Management Tools
- **Google Ads Budget Updater** (`/dashboard/gads-budget-updater`)
  - Automatic budget increases up to specified maximums
  - Campaign pattern matching
  - Telegram notification integration
  - Historical data tracking

- **Budget Monitor** (`/dashboard/budget-monitor`)
  - Campaign budget monitoring and alerts
  - Threshold-based notifications
  - Spending analysis

#### Performance Analysis Tools
- **Campaign Performance** (`/dashboard/campaign-performance`)
  - Campaign metrics and KPI tracking
  - Performance comparison and analysis

- **Ad Performance** (`/dashboard/ad-performance`)
  - Ad-level performance metrics
  - Creative performance analysis

- **Keyword Performance** (`/dashboard/keyword-performance`)
  - Keyword-level analytics
  - Search term performance

- **Search Query Performance** (`/dashboard/search-query`)
  - Search query analysis and optimization
  - Query performance insights

#### Automation and Optimization Tools
- **Telegram Script Generator** (`/dashboard/telegram-script-generator`)
  - Generate Google Ads scripts with Telegram notifications
  - Custom notification templates
  - Bot integration setup

- **AirTable Script Generator** (`/dashboard/airtable-script`)
  - P&L reporting integration with AirTable
  - Data synchronization scripts
  - Custom reporting workflows

- **Performance Max Asset Analyzer** (`/dashboard/performance-max`)
  - Performance Max campaign analysis
  - Asset performance insights

- **Device Bid Adjuster** (`/dashboard/device-bid`)
  - Device-specific bid adjustments
  - Performance-based optimization

- **Keyword Conflict Detector** (`/dashboard/keyword-conflict`)
  - Identify keyword conflicts across campaigns
  - Optimization recommendations

- **Script Generator** (`/dashboard/script-generator`)
  - General Google Ads script generation
  - Custom automation scripts

## File Structure

```
gads-services-mainpage/
├── src/
│   ├── components/
│   │   ├── ui/                    # Reusable UI components
│   │   ├── tools/                 # Tool-specific components
│   │   ├── dashboard/             # Dashboard components
│   │   ├── Header.tsx             # Main navigation
│   │   ├── Layout.tsx             # Page layout wrapper
│   │   ├── Dashboard.tsx          # Main dashboard
│   │   └── ...
│   ├── pages/
│   │   ├── ToolPage.tsx           # Dynamic tool page router
│   │   ├── DashboardPage.tsx      # Dashboard layout
│   │   ├── LoginPage.tsx          # Authentication page
│   │   └── ...
│   ├── utils/
│   │   ├── auth.ts                # Authentication utilities
│   │   └── ...
│   ├── App.tsx                    # Main application component
│   ├── main.tsx                   # Application entry point
│   └── index.css                  # Global styles
├── public/
│   ├── root.csv                   # User authentication data
│   └── ...
├── package.json                   # Dependencies and scripts
├── vite.config.ts                 # Vite configuration
├── tailwind.config.js             # Tailwind CSS configuration
├── tsconfig.json                  # TypeScript configuration
└── netlify.toml                   # Netlify deployment config
```

## Component Architecture

### Tool Page System
**Location**: `src/pages/ToolPage.tsx`
- Dynamic component loading based on URL path
- Centralized tool routing and management
- Consistent layout and navigation

### UI Component Library
**Location**: `src/components/ui/`
- Button, Input, Card, Textarea components
- Consistent design system
- Accessible and responsive components

### Tool Components
**Location**: `src/components/tools/`
- Modular tool implementations
- Shared utilities and helpers
- Form handling and validation

## Authentication Flow

1. **Login Process**: User enters credentials on `/login`
2. **CSV Validation**: Credentials checked against `/root.csv`
3. **Role Assignment**: User role determined from CSV data
4. **Session Management**: User data stored in localStorage
5. **Protected Access**: Dashboard routes require authentication
6. **Logout**: Clears session and redirects to login

## Routing Structure

```
/ (Homepage)
├── /login (Authentication)
├── /app (Login redirect)
├── /portfolio (Portfolio showcase)
└── /dashboard (Protected area)
    ├── / (Main dashboard)
    ├── /telegram-script-generator
    ├── /airtable-script
    ├── /gads-budget-updater
    ├── /campaign-performance
    ├── /ad-performance
    ├── /keyword-performance
    ├── /budget-monitor
    ├── /device-bid
    ├── /search-query
    ├── /performance-max
    ├── /keyword-conflict
    ├── /script-generator
    └── /settings
```

## Data Management

### User Authentication
- CSV-based user database (`/root.csv`)
- Fields: login, password, role
- Local storage for session persistence

### Tool Configuration
- Form state management with React Hook Form
- Local storage for user preferences
- History tracking for form inputs

### Performance Data
- Seasonal budget summary components
- Campaign performance tracking
- Historical data visualization

## Development Workflow

### Local Development
```bash
cd gads-services-mainpage
npm install
npm run dev
```

### Build Process
```bash
npm run build
```

### Code Quality
```bash
npm run lint
```

## Integration Points

### Google Ads API
- Script generation for campaign automation
- Budget management and optimization
- Performance data retrieval and analysis

### Telegram Integration
- Bot notifications for campaign events
- Custom message templates
- Real-time alerts and updates

### AirTable Integration
- P&L data synchronization
- Custom reporting workflows
- Data export and analysis

## Security Considerations

- CSV-based authentication (development/demo purposes)
- Role-based access control
- Protected routes and components
- Input validation and sanitization
- Secure data handling practices

## Performance Optimizations

- Vite for fast development and building
- Code splitting with React Router
- Lazy loading of tool components
- Optimized bundle size with tree shaking
- Efficient re-rendering with React hooks
