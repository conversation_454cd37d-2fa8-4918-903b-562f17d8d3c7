# gAds Supercharge - Project Context

## Project Overview

gAds Supercharge is a comprehensive suite of Google Ads automation tools and services designed to streamline campaign management, budget optimization, and performance reporting. The project consists of multiple components including standalone tools, a React-based web application, and backend services.

## Architecture

### Frontend Application (`gads-services-mainpage/`)
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **Authentication**: Firebase Auth
- **Hosting**: Netlify (https://gads-supercharge.netlify.app/)
- **State Management**: React Context API

### Backend Services (`gadsServices-backend/`)
- **Runtime**: Node.js with TypeScript
- **Framework**: Express.js (inferred)
- **Purpose**: API services and business logic

### Firebase Integration
- **Authentication**: User management and access control
- **Cloud Functions**: Server-side logic for admin operations
- **Configuration**: Firebase project integration

## Key Components

### 1. Main Web Application
**Location**: `gads-services-mainpage/`

**Core Features**:
- User authentication and dashboard
- Script generation tools for Google Ads automation
- Integration with external services (AirTable, Telegram)
- Responsive design with modern UI components

**Key Pages**:
- Dashboard with tool access
- Google Ads Budget Updater
- AirTable P&L Script Generator
- Telegram Bot Script Generator
- Search Ads Script Generator
- User management (admin)

### 2. Standalone Tools
**Locations**: Various directories with self-contained HTML/CSS/JS tools

**Tools Include**:
- `gAds-BudgUpdater-Windsurf/`: Budget management tool
- `gAds-airtable-PnL Windsurf/`: P&L reporting with AirTable integration
- `gAds_TgBotGroup_windsurf/`: Telegram notification script generator

### 3. Utility Scripts
**Locations**: `Scripts_AirTable/`, `Scripts_TGBot/`

**Purpose**: Direct automation scripts for specific tasks

## Technology Stack

### Frontend Dependencies
```json
{
  "react": "^18.3.1",
  "react-dom": "^18.3.1",
  "react-router-dom": "^7.6.0",
  "firebase": "^11.7.3",
  "lucide-react": "^0.344.0",
  "clsx": "^2.0.0"
}
```

### Development Tools
- **TypeScript**: Type safety and better development experience
- **ESLint**: Code linting and quality assurance
- **Tailwind CSS**: Utility-first CSS framework
- **PostCSS**: CSS processing and optimization
- **Vite**: Fast build tool and development server

## File Structure

```
/
├── gads-services-mainpage/     # Main React application
│   ├── src/
│   │   ├── components/         # Reusable UI components
│   │   ├── pages/             # Route-specific page components
│   │   ├── context/           # React context providers
│   │   ├── data/              # Static data and configurations
│   │   ├── types/             # TypeScript type definitions
│   │   └── firebase.ts        # Firebase configuration
│   ├── functions/             # Firebase Cloud Functions
│   └── public/                # Static assets
├── gadsServices-backend/       # Backend API services
├── 2deploy/                   # Deployment-ready builds
├── Scripts_*/                 # Utility scripts
└── Standalone tools/          # Self-contained HTML tools
```

## Data Flow

1. **User Authentication**: Firebase Auth handles user login/signup
2. **Dashboard Access**: Authenticated users access tool dashboard
3. **Script Generation**: Tools generate customized Google Ads scripts
4. **External Integrations**: Scripts integrate with AirTable, Telegram, etc.
5. **Admin Functions**: Firebase Functions handle admin operations

## Integration Points

### Google Ads API
- Script generation for campaign management
- Budget optimization automation
- Performance reporting

### AirTable Integration
- P&L data synchronization
- Campaign performance tracking
- Custom reporting workflows

### Telegram Bot Integration
- Real-time notifications
- Performance alerts
- Campaign status updates

## Security Considerations

- Firebase Authentication for user access control
- Environment variables for sensitive configurations
- Admin-only functions for user management
- Secure API endpoints for backend services

## Development Workflow

1. **Local Development**: Use `npm run dev` in respective directories
2. **Building**: `npm run build` for production builds
3. **Deployment**: Automated deployment to Netlify for frontend
4. **Testing**: ESLint for code quality, manual testing for functionality

## Deployment Architecture

- **Frontend**: Deployed to Netlify with automatic builds
- **Backend**: Separate deployment (configuration TBD)
- **Firebase Functions**: Deployed via Firebase CLI
- **Static Tools**: Can be deployed independently or as part of main app
