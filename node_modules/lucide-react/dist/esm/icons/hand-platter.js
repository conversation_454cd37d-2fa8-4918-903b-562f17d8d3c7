/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const HandPlatter = createLucideIcon("HandPlatter", [
  ["path", { d: "M12 3V2", key: "ar7q03" }],
  ["path", { d: "M5 10a7.1 7.1 0 0 1 14 0", key: "1t9y3n" }],
  ["path", { d: "M4 10h16", key: "img6z1" }],
  ["path", { d: "M2 14h12a2 2 0 1 1 0 4h-2", key: "loyjft" }],
  [
    "path",
    {
      d: "m15.4 17.4 3.2-2.8a2 2 0 0 1 2.8 2.9l-3.6 3.3c-.7.8-1.7 1.2-2.8 1.2h-4c-1.1 0-2.1-.4-2.8-1.2L5 18",
      key: "1rixiy"
    }
  ],
  ["path", { d: "M5 14v7H2", key: "3mujks" }]
]);

export { HandPlatter as default };
//# sourceMappingURL=hand-platter.js.map
