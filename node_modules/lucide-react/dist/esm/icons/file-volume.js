/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const FileVolume = createLucideIcon("FileVolume", [
  ["path", { d: "M4 22h14a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v3", key: "1vg67v" }],
  ["path", { d: "M14 2v4a2 2 0 0 0 2 2h4", key: "tnqrlb" }],
  ["path", { d: "m7 10-3 2H2v4h2l3 2Z", key: "fiq8l4" }],
  ["path", { d: "M11 11a5 5 0 0 1 0 6", key: "193qb2" }]
]);

export { FileVolume as default };
//# sourceMappingURL=file-volume.js.map
