/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const FilePieChart = createLucideIcon("File<PERSON><PERSON><PERSON><PERSON>", [
  ["path", { d: "M16 22h2a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v3", key: "37hlfg" }],
  ["path", { d: "M14 2v4a2 2 0 0 0 2 2h4", key: "tnqrlb" }],
  ["path", { d: "M4 11.5a6.02 6.02 0 1 0 8.5 8.5", key: "unkkko" }],
  ["path", { d: "M14 16c0-3.3-2.7-6-6-6v6Z", key: "bym002" }]
]);

export { FilePieChart as default };
//# sourceMappingURL=file-pie-chart.js.map
