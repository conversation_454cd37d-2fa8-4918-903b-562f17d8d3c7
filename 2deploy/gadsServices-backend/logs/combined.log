{"level":"info","message":"Firebase initialized successfully","service":"gads-services-api","timestamp":"2025-05-18 04:17:10"}
{"level":"info","message":"Firebase initialized successfully","service":"gads-services-api","timestamp":"2025-05-18 04:17:10"}
{"level":"info","message":"Firebase initialization triggered","service":"gads-services-api","timestamp":"2025-05-18 04:17:10"}
{"level":"info","message":"Firebase initialization triggered","service":"gads-services-api","timestamp":"2025-05-18 04:17:10"}
{"level":"info","message":"Server running on port 5000","service":"gads-services-api","timestamp":"2025-05-18 04:17:10"}
{"level":"info","message":"Firebase initialized successfully","service":"gads-services-api","timestamp":"2025-05-18 04:17:10"}
{"level":"info","message":"Firebase initialization triggered","service":"gads-services-api","timestamp":"2025-05-18 04:17:10"}
{"level":"info","message":"Server running on port 5000","service":"gads-services-api","timestamp":"2025-05-18 04:17:10"}
{"level":"info","message":"Server running on port 5000","service":"gads-services-api","timestamp":"2025-05-18 04:17:10"}
{"level":"error","message":"Error getting user by email: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.","service":"gads-services-api","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:287:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GoogleAuth._GoogleAuth_determineClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:834:32)\n    at async GoogleAuth.getClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:698:20)\n    at async GrpcClient._getCredentials (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:209:20)\n    at async GrpcClient.createStub (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:426:19)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at Query._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:784:32)\n    at Query._get (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:777:35)\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:745:43\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at NoopContextManager.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/context/NoopContextManager.ts:31:15)\n    at ContextAPI.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/api/context.ts:77:42)\n    at NoopTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/NoopTracer.ts:98:27)\n    at ProxyTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/ProxyTracer.ts:51:20)\n    at EnabledTraceUtil.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:102:28)","timestamp":"2025-05-18 04:17:16"}
{"level":"error","message":"Error checking admin account: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.","service":"gads-services-api","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:287:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GoogleAuth._GoogleAuth_determineClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:834:32)\n    at async GoogleAuth.getClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:698:20)\n    at async GrpcClient._getCredentials (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:209:20)\n    at async GrpcClient.createStub (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:426:19)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at Query._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:784:32)\n    at Query._get (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:777:35)\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:745:43\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at NoopContextManager.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/context/NoopContextManager.ts:31:15)\n    at ContextAPI.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/api/context.ts:77:42)\n    at NoopTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/NoopTracer.ts:98:27)\n    at ProxyTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/ProxyTracer.ts:51:20)\n    at EnabledTraceUtil.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:102:28)","timestamp":"2025-05-18 04:17:16"}
{"level":"error","message":"Failed to check admin account: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.","service":"gads-services-api","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:287:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GoogleAuth._GoogleAuth_determineClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:834:32)\n    at async GoogleAuth.getClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:698:20)\n    at async GrpcClient._getCredentials (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:209:20)\n    at async GrpcClient.createStub (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:426:19)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at Query._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:784:32)\n    at Query._get (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:777:35)\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:745:43\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at NoopContextManager.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/context/NoopContextManager.ts:31:15)\n    at ContextAPI.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/api/context.ts:77:42)\n    at NoopTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/NoopTracer.ts:98:27)\n    at ProxyTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/ProxyTracer.ts:51:20)\n    at EnabledTraceUtil.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:102:28)","timestamp":"2025-05-18 04:17:16"}
{"level":"error","message":"Error ensuring admin account exists: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.","service":"gads-services-api","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:287:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GoogleAuth._GoogleAuth_determineClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:834:32)\n    at async GoogleAuth.getClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:698:20)\n    at async GrpcClient._getCredentials (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:209:20)\n    at async GrpcClient.createStub (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:426:19)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at Query._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:784:32)\n    at Query._get (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:777:35)\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:745:43\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at NoopContextManager.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/context/NoopContextManager.ts:31:15)\n    at ContextAPI.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/api/context.ts:77:42)\n    at NoopTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/NoopTracer.ts:98:27)\n    at ProxyTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/ProxyTracer.ts:51:20)\n    at EnabledTraceUtil.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:102:28)","timestamp":"2025-05-18 04:17:16"}
{"level":"error","message":"Error getting user by email: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.","service":"gads-services-api","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:287:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GoogleAuth._GoogleAuth_determineClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:834:32)\n    at async GoogleAuth.getClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:698:20)\n    at async GrpcClient._getCredentials (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:209:20)\n    at async GrpcClient.createStub (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:426:19)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at Query._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:784:32)\n    at Query._get (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:777:35)\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:745:43\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at NoopContextManager.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/context/NoopContextManager.ts:31:15)\n    at ContextAPI.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/api/context.ts:77:42)\n    at NoopTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/NoopTracer.ts:98:27)\n    at ProxyTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/ProxyTracer.ts:51:20)\n    at EnabledTraceUtil.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:102:28)","timestamp":"2025-05-18 04:17:18"}
{"level":"error","message":"Error checking admin account: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.","service":"gads-services-api","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:287:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GoogleAuth._GoogleAuth_determineClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:834:32)\n    at async GoogleAuth.getClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:698:20)\n    at async GrpcClient._getCredentials (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:209:20)\n    at async GrpcClient.createStub (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:426:19)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at Query._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:784:32)\n    at Query._get (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:777:35)\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:745:43\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at NoopContextManager.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/context/NoopContextManager.ts:31:15)\n    at ContextAPI.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/api/context.ts:77:42)\n    at NoopTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/NoopTracer.ts:98:27)\n    at ProxyTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/ProxyTracer.ts:51:20)\n    at EnabledTraceUtil.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:102:28)","timestamp":"2025-05-18 04:17:18"}
{"level":"error","message":"Failed to check admin account: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.","service":"gads-services-api","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:287:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GoogleAuth._GoogleAuth_determineClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:834:32)\n    at async GoogleAuth.getClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:698:20)\n    at async GrpcClient._getCredentials (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:209:20)\n    at async GrpcClient.createStub (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:426:19)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at Query._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:784:32)\n    at Query._get (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:777:35)\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:745:43\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at NoopContextManager.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/context/NoopContextManager.ts:31:15)\n    at ContextAPI.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/api/context.ts:77:42)\n    at NoopTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/NoopTracer.ts:98:27)\n    at ProxyTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/ProxyTracer.ts:51:20)\n    at EnabledTraceUtil.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:102:28)","timestamp":"2025-05-18 04:17:18"}
{"level":"error","message":"Error ensuring admin account exists: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.","service":"gads-services-api","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:287:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GoogleAuth._GoogleAuth_determineClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:834:32)\n    at async GoogleAuth.getClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:698:20)\n    at async GrpcClient._getCredentials (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:209:20)\n    at async GrpcClient.createStub (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:426:19)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at Query._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:784:32)\n    at Query._get (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:777:35)\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:745:43\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at NoopContextManager.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/context/NoopContextManager.ts:31:15)\n    at ContextAPI.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/api/context.ts:77:42)\n    at NoopTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/NoopTracer.ts:98:27)\n    at ProxyTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/ProxyTracer.ts:51:20)\n    at EnabledTraceUtil.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:102:28)","timestamp":"2025-05-18 04:17:18"}
{"level":"error","message":"Error getting user by email: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.","service":"gads-services-api","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:287:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GoogleAuth._GoogleAuth_determineClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:834:32)\n    at async GoogleAuth.getClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:698:20)\n    at async GrpcClient._getCredentials (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:209:20)\n    at async GrpcClient.createStub (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:426:19)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at Query._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:784:32)\n    at Query._get (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:777:35)\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:745:43\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at NoopContextManager.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/context/NoopContextManager.ts:31:15)\n    at ContextAPI.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/api/context.ts:77:42)\n    at NoopTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/NoopTracer.ts:98:27)\n    at ProxyTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/ProxyTracer.ts:51:20)\n    at EnabledTraceUtil.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:102:28)","timestamp":"2025-05-18 04:17:19"}
{"level":"error","message":"Error checking admin account: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.","service":"gads-services-api","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:287:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GoogleAuth._GoogleAuth_determineClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:834:32)\n    at async GoogleAuth.getClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:698:20)\n    at async GrpcClient._getCredentials (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:209:20)\n    at async GrpcClient.createStub (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:426:19)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at Query._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:784:32)\n    at Query._get (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:777:35)\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:745:43\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at NoopContextManager.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/context/NoopContextManager.ts:31:15)\n    at ContextAPI.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/api/context.ts:77:42)\n    at NoopTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/NoopTracer.ts:98:27)\n    at ProxyTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/ProxyTracer.ts:51:20)\n    at EnabledTraceUtil.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:102:28)","timestamp":"2025-05-18 04:17:19"}
{"level":"error","message":"Failed to check admin account: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.","service":"gads-services-api","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:287:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GoogleAuth._GoogleAuth_determineClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:834:32)\n    at async GoogleAuth.getClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:698:20)\n    at async GrpcClient._getCredentials (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:209:20)\n    at async GrpcClient.createStub (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:426:19)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at Query._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:784:32)\n    at Query._get (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:777:35)\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:745:43\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at NoopContextManager.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/context/NoopContextManager.ts:31:15)\n    at ContextAPI.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/api/context.ts:77:42)\n    at NoopTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/NoopTracer.ts:98:27)\n    at ProxyTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/ProxyTracer.ts:51:20)\n    at EnabledTraceUtil.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:102:28)","timestamp":"2025-05-18 04:17:19"}
{"level":"error","message":"Error ensuring admin account exists: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.","service":"gads-services-api","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:287:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GoogleAuth._GoogleAuth_determineClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:834:32)\n    at async GoogleAuth.getClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:698:20)\n    at async GrpcClient._getCredentials (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:209:20)\n    at async GrpcClient.createStub (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:426:19)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at Query._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:784:32)\n    at Query._get (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:777:35)\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:745:43\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at NoopContextManager.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/context/NoopContextManager.ts:31:15)\n    at ContextAPI.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/api/context.ts:77:42)\n    at NoopTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/NoopTracer.ts:98:27)\n    at ProxyTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/ProxyTracer.ts:51:20)\n    at EnabledTraceUtil.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:102:28)","timestamp":"2025-05-18 04:17:19"}
{"level":"info","message":"Firebase initialized successfully","service":"gads-services-api","timestamp":"2025-05-18 04:17:34"}
{"level":"info","message":"Firebase initialization triggered","service":"gads-services-api","timestamp":"2025-05-18 04:17:34"}
{"level":"info","message":"Server running on port 5000","service":"gads-services-api","timestamp":"2025-05-18 04:17:34"}
{"level":"error","message":"Error getting user by email: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.","service":"gads-services-api","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:287:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GoogleAuth._GoogleAuth_determineClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:834:32)\n    at async GoogleAuth.getClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:698:20)\n    at async GrpcClient._getCredentials (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:209:20)\n    at async GrpcClient.createStub (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:426:19)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at Query._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:784:32)\n    at Query._get (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:777:35)\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:745:43\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at NoopContextManager.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/context/NoopContextManager.ts:31:15)\n    at ContextAPI.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/api/context.ts:77:42)\n    at NoopTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/NoopTracer.ts:98:27)\n    at ProxyTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/ProxyTracer.ts:51:20)\n    at EnabledTraceUtil.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:102:28)","timestamp":"2025-05-18 04:17:42"}
{"level":"error","message":"Error checking admin account: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.","service":"gads-services-api","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:287:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GoogleAuth._GoogleAuth_determineClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:834:32)\n    at async GoogleAuth.getClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:698:20)\n    at async GrpcClient._getCredentials (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:209:20)\n    at async GrpcClient.createStub (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:426:19)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at Query._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:784:32)\n    at Query._get (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:777:35)\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:745:43\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at NoopContextManager.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/context/NoopContextManager.ts:31:15)\n    at ContextAPI.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/api/context.ts:77:42)\n    at NoopTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/NoopTracer.ts:98:27)\n    at ProxyTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/ProxyTracer.ts:51:20)\n    at EnabledTraceUtil.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:102:28)","timestamp":"2025-05-18 04:17:42"}
{"level":"error","message":"Failed to check admin account: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.","service":"gads-services-api","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:287:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GoogleAuth._GoogleAuth_determineClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:834:32)\n    at async GoogleAuth.getClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:698:20)\n    at async GrpcClient._getCredentials (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:209:20)\n    at async GrpcClient.createStub (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:426:19)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at Query._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:784:32)\n    at Query._get (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:777:35)\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:745:43\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at NoopContextManager.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/context/NoopContextManager.ts:31:15)\n    at ContextAPI.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/api/context.ts:77:42)\n    at NoopTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/NoopTracer.ts:98:27)\n    at ProxyTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/ProxyTracer.ts:51:20)\n    at EnabledTraceUtil.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:102:28)","timestamp":"2025-05-18 04:17:42"}
{"level":"error","message":"Error ensuring admin account exists: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.","service":"gads-services-api","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:287:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GoogleAuth._GoogleAuth_determineClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:834:32)\n    at async GoogleAuth.getClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:698:20)\n    at async GrpcClient._getCredentials (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:209:20)\n    at async GrpcClient.createStub (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:426:19)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at Query._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:784:32)\n    at Query._get (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:777:35)\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:745:43\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at NoopContextManager.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/context/NoopContextManager.ts:31:15)\n    at ContextAPI.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/api/context.ts:77:42)\n    at NoopTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/NoopTracer.ts:98:27)\n    at ProxyTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/ProxyTracer.ts:51:20)\n    at EnabledTraceUtil.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:102:28)","timestamp":"2025-05-18 04:17:42"}
{"level":"info","message":"Firebase initialized successfully","service":"gads-services-api","timestamp":"2025-05-18 04:18:27"}
{"level":"info","message":"Firebase initialization triggered","service":"gads-services-api","timestamp":"2025-05-18 04:18:27"}
{"level":"info","message":"Firebase initialized successfully","service":"gads-services-api","timestamp":"2025-05-18 04:18:27"}
{"level":"info","message":"Firebase initialization triggered","service":"gads-services-api","timestamp":"2025-05-18 04:18:27"}
{"level":"info","message":"Server running on port 5000","service":"gads-services-api","timestamp":"2025-05-18 04:18:27"}
{"level":"info","message":"Server running on port 5000","service":"gads-services-api","timestamp":"2025-05-18 04:18:27"}
{"level":"info","message":"Firebase initialized successfully","service":"gads-services-api","timestamp":"2025-05-18 04:18:27"}
{"level":"info","message":"Firebase initialization triggered","service":"gads-services-api","timestamp":"2025-05-18 04:18:27"}
{"level":"info","message":"Server running on port 5000","service":"gads-services-api","timestamp":"2025-05-18 04:18:27"}
{"level":"info","message":"Firebase initialized successfully","service":"gads-services-api","timestamp":"2025-05-18 04:18:27"}
{"level":"info","message":"Firebase initialization triggered","service":"gads-services-api","timestamp":"2025-05-18 04:18:27"}
{"level":"info","message":"Server running on port 5000","service":"gads-services-api","timestamp":"2025-05-18 04:18:27"}
{"level":"info","message":"Firebase initialized successfully","service":"gads-services-api","timestamp":"2025-05-18 04:18:30"}
{"level":"info","message":"Firebase initialization triggered","service":"gads-services-api","timestamp":"2025-05-18 04:18:30"}
{"level":"info","message":"Server running on port 5000","service":"gads-services-api","timestamp":"2025-05-18 04:18:30"}
{"level":"info","message":"Firebase initialized successfully","service":"gads-services-api","timestamp":"2025-05-18 04:18:30"}
{"level":"info","message":"Firebase initialized successfully","service":"gads-services-api","timestamp":"2025-05-18 04:18:30"}
{"level":"info","message":"Firebase initialization triggered","service":"gads-services-api","timestamp":"2025-05-18 04:18:30"}
{"level":"info","message":"Firebase initialization triggered","service":"gads-services-api","timestamp":"2025-05-18 04:18:30"}
{"level":"info","message":"Server running on port 5000","service":"gads-services-api","timestamp":"2025-05-18 04:18:30"}
{"level":"info","message":"Server running on port 5000","service":"gads-services-api","timestamp":"2025-05-18 04:18:30"}
{"level":"info","message":"Firebase initialized successfully","service":"gads-services-api","timestamp":"2025-05-18 04:18:30"}
{"level":"info","message":"Firebase initialization triggered","service":"gads-services-api","timestamp":"2025-05-18 04:18:30"}
{"level":"info","message":"Server running on port 5000","service":"gads-services-api","timestamp":"2025-05-18 04:18:30"}
{"level":"error","message":"Error checking admin account: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.","service":"gads-services-api","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:287:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GoogleAuth._GoogleAuth_determineClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:834:32)\n    at async GoogleAuth.getClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:698:20)\n    at async GrpcClient._getCredentials (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:209:20)\n    at async GrpcClient.createStub (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:426:19)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at Query._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:784:32)\n    at Query._get (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:777:35)\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:745:43\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at NoopContextManager.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/context/NoopContextManager.ts:31:15)\n    at ContextAPI.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/api/context.ts:77:42)\n    at NoopTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/NoopTracer.ts:98:27)\n    at ProxyTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/ProxyTracer.ts:51:20)\n    at EnabledTraceUtil.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:102:28)","timestamp":"2025-05-18 04:18:36"}
{"level":"error","message":"Error getting user by email: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.","service":"gads-services-api","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:287:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GoogleAuth._GoogleAuth_determineClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:834:32)\n    at async GoogleAuth.getClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:698:20)\n    at async GrpcClient._getCredentials (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:209:20)\n    at async GrpcClient.createStub (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:426:19)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at Query._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:784:32)\n    at Query._get (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:777:35)\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:745:43\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at NoopContextManager.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/context/NoopContextManager.ts:31:15)\n    at ContextAPI.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/api/context.ts:77:42)\n    at NoopTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/NoopTracer.ts:98:27)\n    at ProxyTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/ProxyTracer.ts:51:20)\n    at EnabledTraceUtil.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:102:28)","timestamp":"2025-05-18 04:18:36"}
{"level":"error","message":"Failed to check admin account: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.","service":"gads-services-api","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:287:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GoogleAuth._GoogleAuth_determineClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:834:32)\n    at async GoogleAuth.getClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:698:20)\n    at async GrpcClient._getCredentials (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:209:20)\n    at async GrpcClient.createStub (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:426:19)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at Query._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:784:32)\n    at Query._get (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:777:35)\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:745:43\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at NoopContextManager.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/context/NoopContextManager.ts:31:15)\n    at ContextAPI.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/api/context.ts:77:42)\n    at NoopTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/NoopTracer.ts:98:27)\n    at ProxyTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/ProxyTracer.ts:51:20)\n    at EnabledTraceUtil.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:102:28)","timestamp":"2025-05-18 04:18:36"}
{"level":"error","message":"Error ensuring admin account exists: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.","service":"gads-services-api","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:287:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GoogleAuth._GoogleAuth_determineClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:834:32)\n    at async GoogleAuth.getClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:698:20)\n    at async GrpcClient._getCredentials (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:209:20)\n    at async GrpcClient.createStub (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:426:19)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at Query._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:784:32)\n    at Query._get (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:777:35)\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:745:43\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at NoopContextManager.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/context/NoopContextManager.ts:31:15)\n    at ContextAPI.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/api/context.ts:77:42)\n    at NoopTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/NoopTracer.ts:98:27)\n    at ProxyTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/ProxyTracer.ts:51:20)\n    at EnabledTraceUtil.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:102:28)","timestamp":"2025-05-18 04:18:36"}
{"level":"error","message":"Error getting user by email: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.","service":"gads-services-api","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:287:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GoogleAuth._GoogleAuth_determineClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:834:32)\n    at async GoogleAuth.getClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:698:20)\n    at async GrpcClient._getCredentials (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:209:20)\n    at async GrpcClient.createStub (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:426:19)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at Query._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:784:32)\n    at Query._get (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:777:35)\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:745:43\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at NoopContextManager.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/context/NoopContextManager.ts:31:15)\n    at ContextAPI.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/api/context.ts:77:42)\n    at NoopTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/NoopTracer.ts:98:27)\n    at ProxyTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/ProxyTracer.ts:51:20)\n    at EnabledTraceUtil.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:102:28)","timestamp":"2025-05-18 04:18:38"}
{"level":"error","message":"Error checking admin account: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.","service":"gads-services-api","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:287:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GoogleAuth._GoogleAuth_determineClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:834:32)\n    at async GoogleAuth.getClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:698:20)\n    at async GrpcClient._getCredentials (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:209:20)\n    at async GrpcClient.createStub (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:426:19)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at Query._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:784:32)\n    at Query._get (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:777:35)\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:745:43\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at NoopContextManager.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/context/NoopContextManager.ts:31:15)\n    at ContextAPI.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/api/context.ts:77:42)\n    at NoopTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/NoopTracer.ts:98:27)\n    at ProxyTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/ProxyTracer.ts:51:20)\n    at EnabledTraceUtil.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:102:28)","timestamp":"2025-05-18 04:18:38"}
{"level":"error","message":"Failed to check admin account: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.","service":"gads-services-api","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:287:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GoogleAuth._GoogleAuth_determineClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:834:32)\n    at async GoogleAuth.getClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:698:20)\n    at async GrpcClient._getCredentials (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:209:20)\n    at async GrpcClient.createStub (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:426:19)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at Query._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:784:32)\n    at Query._get (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:777:35)\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:745:43\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at NoopContextManager.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/context/NoopContextManager.ts:31:15)\n    at ContextAPI.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/api/context.ts:77:42)\n    at NoopTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/NoopTracer.ts:98:27)\n    at ProxyTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/ProxyTracer.ts:51:20)\n    at EnabledTraceUtil.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:102:28)","timestamp":"2025-05-18 04:18:38"}
{"level":"error","message":"Error ensuring admin account exists: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.","service":"gads-services-api","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:287:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GoogleAuth._GoogleAuth_determineClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:834:32)\n    at async GoogleAuth.getClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:698:20)\n    at async GrpcClient._getCredentials (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:209:20)\n    at async GrpcClient.createStub (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:426:19)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at Query._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:784:32)\n    at Query._get (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:777:35)\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:745:43\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at NoopContextManager.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/context/NoopContextManager.ts:31:15)\n    at ContextAPI.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/api/context.ts:77:42)\n    at NoopTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/NoopTracer.ts:98:27)\n    at ProxyTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/ProxyTracer.ts:51:20)\n    at EnabledTraceUtil.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:102:28)","timestamp":"2025-05-18 04:18:38"}
{"level":"error","message":"Error getting user by email: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.","service":"gads-services-api","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:287:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GoogleAuth._GoogleAuth_determineClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:834:32)\n    at async GoogleAuth.getClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:698:20)\n    at async GrpcClient._getCredentials (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:209:20)\n    at async GrpcClient.createStub (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:426:19)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at Query._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:784:32)\n    at Query._get (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:777:35)\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:745:43\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at NoopContextManager.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/context/NoopContextManager.ts:31:15)\n    at ContextAPI.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/api/context.ts:77:42)\n    at NoopTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/NoopTracer.ts:98:27)\n    at ProxyTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/ProxyTracer.ts:51:20)\n    at EnabledTraceUtil.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:102:28)","timestamp":"2025-05-18 04:18:39"}
{"level":"error","message":"Error checking admin account: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.","service":"gads-services-api","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:287:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GoogleAuth._GoogleAuth_determineClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:834:32)\n    at async GoogleAuth.getClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:698:20)\n    at async GrpcClient._getCredentials (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:209:20)\n    at async GrpcClient.createStub (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:426:19)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at Query._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:784:32)\n    at Query._get (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:777:35)\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:745:43\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at NoopContextManager.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/context/NoopContextManager.ts:31:15)\n    at ContextAPI.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/api/context.ts:77:42)\n    at NoopTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/NoopTracer.ts:98:27)\n    at ProxyTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/ProxyTracer.ts:51:20)\n    at EnabledTraceUtil.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:102:28)","timestamp":"2025-05-18 04:18:39"}
{"level":"error","message":"Failed to check admin account: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.","service":"gads-services-api","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:287:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GoogleAuth._GoogleAuth_determineClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:834:32)\n    at async GoogleAuth.getClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:698:20)\n    at async GrpcClient._getCredentials (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:209:20)\n    at async GrpcClient.createStub (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:426:19)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at Query._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:784:32)\n    at Query._get (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:777:35)\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:745:43\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at NoopContextManager.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/context/NoopContextManager.ts:31:15)\n    at ContextAPI.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/api/context.ts:77:42)\n    at NoopTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/NoopTracer.ts:98:27)\n    at ProxyTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/ProxyTracer.ts:51:20)\n    at EnabledTraceUtil.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:102:28)","timestamp":"2025-05-18 04:18:39"}
{"level":"error","message":"Error ensuring admin account exists: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.","service":"gads-services-api","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:287:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GoogleAuth._GoogleAuth_determineClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:834:32)\n    at async GoogleAuth.getClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:698:20)\n    at async GrpcClient._getCredentials (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:209:20)\n    at async GrpcClient.createStub (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:426:19)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at Query._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:784:32)\n    at Query._get (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:777:35)\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:745:43\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at NoopContextManager.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/context/NoopContextManager.ts:31:15)\n    at ContextAPI.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/api/context.ts:77:42)\n    at NoopTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/NoopTracer.ts:98:27)\n    at ProxyTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/ProxyTracer.ts:51:20)\n    at EnabledTraceUtil.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:102:28)","timestamp":"2025-05-18 04:18:39"}
{"level":"error","message":"Error getting user by email: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.","service":"gads-services-api","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:287:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GoogleAuth._GoogleAuth_determineClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:834:32)\n    at async GoogleAuth.getClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:698:20)\n    at async GrpcClient._getCredentials (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:209:20)\n    at async GrpcClient.createStub (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:426:19)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at Query._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:784:32)\n    at Query._get (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:777:35)\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:745:43\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at NoopContextManager.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/context/NoopContextManager.ts:31:15)\n    at ContextAPI.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/api/context.ts:77:42)\n    at NoopTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/NoopTracer.ts:98:27)\n    at ProxyTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/ProxyTracer.ts:51:20)\n    at EnabledTraceUtil.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:102:28)","timestamp":"2025-05-18 04:18:39"}
{"level":"error","message":"Error checking admin account: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.","service":"gads-services-api","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:287:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GoogleAuth._GoogleAuth_determineClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:834:32)\n    at async GoogleAuth.getClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:698:20)\n    at async GrpcClient._getCredentials (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:209:20)\n    at async GrpcClient.createStub (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:426:19)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at Query._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:784:32)\n    at Query._get (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:777:35)\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:745:43\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at NoopContextManager.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/context/NoopContextManager.ts:31:15)\n    at ContextAPI.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/api/context.ts:77:42)\n    at NoopTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/NoopTracer.ts:98:27)\n    at ProxyTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/ProxyTracer.ts:51:20)\n    at EnabledTraceUtil.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:102:28)","timestamp":"2025-05-18 04:18:39"}
{"level":"error","message":"Failed to check admin account: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.","service":"gads-services-api","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:287:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GoogleAuth._GoogleAuth_determineClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:834:32)\n    at async GoogleAuth.getClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:698:20)\n    at async GrpcClient._getCredentials (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:209:20)\n    at async GrpcClient.createStub (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:426:19)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at Query._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:784:32)\n    at Query._get (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:777:35)\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:745:43\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at NoopContextManager.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/context/NoopContextManager.ts:31:15)\n    at ContextAPI.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/api/context.ts:77:42)\n    at NoopTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/NoopTracer.ts:98:27)\n    at ProxyTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/ProxyTracer.ts:51:20)\n    at EnabledTraceUtil.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:102:28)","timestamp":"2025-05-18 04:18:39"}
{"level":"error","message":"Error ensuring admin account exists: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.","service":"gads-services-api","stack":"Error: Could not load the default credentials. Browse to https://cloud.google.com/docs/authentication/getting-started for more information.\n    at GoogleAuth.getApplicationDefaultAsync (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:287:15)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async GoogleAuth._GoogleAuth_determineClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:834:32)\n    at async GoogleAuth.getClient (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-auth-library/build/src/auth/googleauth.js:698:20)\n    at async GrpcClient._getCredentials (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:209:20)\n    at async GrpcClient.createStub (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/google-gax/src/grpc.ts:426:19)\nCaused by: Error\n    at QueryUtil._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query-util.js:44:23)\n    at Query._getResponse (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:784:32)\n    at Query._get (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:777:35)\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/reference/query.js:745:43\n    at /Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:110:30\n    at NoopContextManager.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/context/NoopContextManager.ts:31:15)\n    at ContextAPI.with (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/api/context.ts:77:42)\n    at NoopTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/NoopTracer.ts:98:27)\n    at ProxyTracer.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@opentelemetry/api/src/trace/ProxyTracer.ts:51:20)\n    at EnabledTraceUtil.startActiveSpan (/Users/<USER>/Documents/GitHub/gAdsScripts/gadsServices-backend/node_modules/@google-cloud/firestore/build/src/telemetry/enabled-trace-util.js:102:28)","timestamp":"2025-05-18 04:18:39"}
