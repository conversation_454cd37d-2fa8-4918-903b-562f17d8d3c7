/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * Port from auth proactiverefresh.js
 *
 */
export declare class Refresher {
    private readonly operation;
    private readonly retryPolicy;
    private readonly getWaitDuration;
    private readonly lowerBound;
    private readonly upperBound;
    private pending;
    private nextErrorWaitInterval;
    constructor(operation: () => Promise<unknown>, retryPolicy: (error: unknown) => boolean, getWaitDuration: () => number, lowerBound: number, upperBound: number);
    start(): void;
    stop(): void;
    isRunning(): boolean;
    private process;
    private getNextRun;
}
