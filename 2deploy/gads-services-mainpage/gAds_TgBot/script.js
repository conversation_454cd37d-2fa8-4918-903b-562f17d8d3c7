document.addEventListener('DOMContentLoaded', function() {
    // Load saved values from localStorage
    loadSavedValues();
    const scriptForm = document.getElementById('scriptForm');
    const resultContainer = document.getElementById('resultContainer');
    const generatedScriptElement = document.getElementById('generatedScript');
    const copyButton = document.getElementById('copyButton');
    const backButton = document.getElementById('backButton');

    // Handle form submission
    scriptForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Get form values
        const telegramToken = document.getElementById('telegramToken').value.trim();
        const telegramChatId = document.getElementById('telegramChatId').value.trim();
        const accountTag = document.getElementById('accountTag').value.trim();
        
        // Save form values to localStorage
        saveFormValue('telegramTokens', telegramToken);
        saveFormValue('telegramChatIds', telegramChatId);
        saveFormValue('accountTags', accountTag);
        
        // Generate the script with randomized variable names
        const generatedScript = generateUniqueScript(telegramToken, telegramChatId, accountTag);
        
        // Display the generated script
        generatedScriptElement.textContent = generatedScript;
        
        // Show the result container and hide the form and instruction
        scriptForm.closest('.card').classList.add('d-none');
        // Find and hide the instruction card that comes after the form card
        const instructionCard = document.querySelector('.card.mb-4:nth-of-type(2)');
        if (instructionCard) {
            instructionCard.classList.add('d-none');
        }
        resultContainer.classList.remove('d-none');
    });

    // Handle copy button click
    copyButton.addEventListener('click', function() {
        const scriptText = generatedScriptElement.textContent;
        navigator.clipboard.writeText(scriptText).then(function() {
            // Visual feedback for successful copy
            copyButton.innerHTML = 'Скопійовано ✓';
            copyButton.classList.add('btn-success');
            copyButton.classList.remove('btn-primary');
            
            // Reset button after 2 seconds
            setTimeout(function() {
                copyButton.innerHTML = 'Скопіювати скрипт';
                copyButton.classList.add('btn-primary');
                copyButton.classList.remove('btn-success');
            }, 2000);
        });
    });

    // Handle back button click
    backButton.addEventListener('click', function() {
        resultContainer.classList.add('d-none');
        scriptForm.closest('.card').classList.remove('d-none');
        
        // Show the instruction card again
        const instructionCard = document.querySelector('.card.mb-4:nth-of-type(2)');
        if (instructionCard) {
            instructionCard.classList.remove('d-none');
        }
    });
    
    // Function to save form value to localStorage
    function saveFormValue(key, value) {
        if (!value) return;
        
        // Get existing values or initialize empty array
        let values = JSON.parse(localStorage.getItem(key) || '[]');
        
        // Remove the value if it already exists (to avoid duplicates)
        values = values.filter(v => v !== value);
        
        // Add the new value to the beginning
        values.unshift(value);
        
        // Keep only the last 3 values
        values = values.slice(0, 3);
        
        // Save back to localStorage
        localStorage.setItem(key, JSON.stringify(values));
        
        // Update the datalist
        updateDatalist(key.replace(/s$/, ''), values);
    }
    
    // Function to load saved values and create datalists
    function loadSavedValues() {
        // Create datalists for each input field
        createDatalist('telegramToken', 'telegramTokens');
        createDatalist('telegramChatId', 'telegramChatIds');
        createDatalist('accountTag', 'accountTags');
        
        // Load saved values into datalists
        loadValuesIntoDatalist('telegramToken', 'telegramTokens');
        loadValuesIntoDatalist('telegramChatId', 'telegramChatIds');
        loadValuesIntoDatalist('accountTag', 'accountTags');
    }
    
    // Function to create a datalist for an input field
    function createDatalist(inputId, storageKey) {
        const input = document.getElementById(inputId);
        const datalistId = inputId + 'List';
        
        // Create datalist if it doesn't exist
        if (!document.getElementById(datalistId)) {
            const datalist = document.createElement('datalist');
            datalist.id = datalistId;
            input.parentNode.appendChild(datalist);
            input.setAttribute('list', datalistId);
        }
    }
    
    // Function to load values into a datalist
    function loadValuesIntoDatalist(inputId, storageKey) {
        const values = JSON.parse(localStorage.getItem(storageKey) || '[]');
        updateDatalist(inputId, values);
    }
    
    // Function to update a datalist with values
    function updateDatalist(inputId, values) {
        const datalistId = inputId + 'List';
        const datalist = document.getElementById(datalistId);
        
        if (datalist) {
            // Clear existing options
            datalist.innerHTML = '';
            
            // Add options for each value
            values.forEach(value => {
                const option = document.createElement('option');
                option.value = value;
                datalist.appendChild(option);
            });
        }
    }

    // Function to generate random variable names
    function generateRandomVarName(prefix = '') {
        const adjectives = ['active', 'brave', 'calm', 'dynamic', 'eager', 'fast', 'good', 'happy'];
        const nouns = ['apple', 'bear', 'cat', 'dog', 'eagle', 'fox', 'goat', 'horse'];
        const randomAdjective = adjectives[Math.floor(Math.random() * adjectives.length)];
        const randomNoun = nouns[Math.floor(Math.random() * nouns.length)];
        const randomNum = Math.floor(Math.random() * 1000);
        return prefix + randomAdjective.charAt(0).toUpperCase() + randomAdjective.slice(1) + 
               randomNoun.charAt(0).toUpperCase() + randomNoun.slice(1) + randomNum;
    }

    // Function to generate the uniqueized script
    function generateUniqueScript(token, chatId, accountTag) {
        // Generate random variable names
        const sendMsgFuncName = generateRandomVarName('send');
        const accountVarName = generateRandomVarName('account');
        const todayCostVarName = generateRandomVarName('cost');
        const todayClicksVarName = generateRandomVarName('clicks');
        const tagAccountsVarName = generateRandomVarName('tag');
        const configVarName = generateRandomVarName('config');
        const telegramUrlVarName = generateRandomVarName('url');
        const messageVarName = generateRandomVarName('msg');
        const sendMessageUrlVarName = generateRandomVarName('sendUrl');
        const optionsVarName = generateRandomVarName('options');
        const formattedChatIdVarName = generateRandomVarName('formattedChatId');
        const channelIdVarName = generateRandomVarName('channelId');

        // Create the script with the randomized variable names and a fixed "main" function
        return `function main() {
    var ${accountVarName} = AdsApp.currentAccount();
    var ${todayCostVarName} = AdsApp.currentAccount().getStatsFor("TODAY").getCost();
    var ${todayClicksVarName} = ${accountVarName}.getStatsFor("TODAY").getClicks();
    var ${tagAccountsVarName} = '${accountTag}';

    ${sendMsgFuncName}(
        '\\nАкаунт ID: ' + ${accountVarName}.getCustomerId() +
        '\\nКліків сьогодні: ' + ${todayClicksVarName} +
        '\\nВитрачено сьогодні: ' + ${todayCostVarName} + ' ' + ${accountVarName}.getCurrencyCode() +
        '\\n#' + ${tagAccountsVarName});
}

function ${sendMsgFuncName}(text) {
    var ${configVarName} = {
        TOKEN: '${token}',
        CHAT_ID: '${chatId}'
    };
    
    var ${formattedChatIdVarName} = ${configVarName}.CHAT_ID;
    if (${formattedChatIdVarName}.startsWith('-') && !${formattedChatIdVarName}.startsWith('-100')) {
        var ${channelIdVarName} = ${formattedChatIdVarName}.substring(1);
        ${formattedChatIdVarName} = '-100' + ${channelIdVarName};
    }
    
    var ${telegramUrlVarName} = 'https://api.telegram.org/bot' + ${configVarName}.TOKEN + '/sendMessage';
    var ${messageVarName} = encodeURIComponent(text);
    var ${optionsVarName} = {
        method: 'POST',
        contentType: 'application/json',
        payload: JSON.stringify({
            chat_id: ${formattedChatIdVarName},
            text: text,
            parse_mode: 'HTML'
        })
    };
    
    try {
        UrlFetchApp.fetch(${telegramUrlVarName}, ${optionsVarName});
    } catch(e) {
        Logger.log('Error sending message: ' + e.toString());
        
        if (${formattedChatIdVarName} !== ${configVarName}.CHAT_ID) {
            ${optionsVarName}.payload = JSON.stringify({
                chat_id: ${configVarName}.CHAT_ID,
                text: text + '\\\\n\\\\nNote: There was an issue with the channel ID format. Using original format.',
                parse_mode: 'HTML'
            });
            try {
                UrlFetchApp.fetch(${telegramUrlVarName}, ${optionsVarName});
            } catch(e2) {
                Logger.log('Error sending message with original chat ID: ' + e2.toString());
            }
        }
    }
}`;
    }
});
