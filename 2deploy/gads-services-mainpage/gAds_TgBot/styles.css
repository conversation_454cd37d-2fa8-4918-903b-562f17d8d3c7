body {
    background-color: #f8f9fa;
}

.card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
}

.code-container {
    background-color: #f5f5f5;
    border-radius: 5px;
    padding: 15px;
    margin-top: 10px;
    border: 1px solid #ddd;
    max-height: 400px;
    overflow-y: auto;
}

pre {
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
}

code {
    font-family: 'Courier New', Courier, monospace;
    font-size: 14px;
}

.copy-success {
    color: #198754;
    margin-left: 10px;
    font-weight: bold;
    display: none;
}

/* Animation for copy success message */
@keyframes fadeOut {
    0% { opacity: 1; }
    70% { opacity: 1; }
    100% { opacity: 0; }
}

.fade-out {
    animation: fadeOut 2s forwards;
}
