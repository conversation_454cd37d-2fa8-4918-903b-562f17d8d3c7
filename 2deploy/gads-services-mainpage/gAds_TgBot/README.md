# Google Ads to Telegram Script Generator

This web application allows you to generate a customized Google Ads script that sends account spending information to a Telegram chat. The application creates a unique script with randomized variable names each time you generate it.

## Features

- Simple form to input your Telegram token, chat ID, and account tag
- Automatic variable name randomization for each script generation
- Copy button for easy script copying
- Responsive design using Bootstrap

## How to Use

1. Open `index.html` in your web browser
2. Fill in the form with your information:
   - **Telegram Token**: Your bot token from BotFather (e.g., `**********:AAHEsM-ExampleTokenHere123`)
   - **Telegram Chat ID**: Your chat or group ID (e.g., `-100*********` or `*********`)
   - **Account Tag**: A tag to identify this account in your statistics
3. Click "Generate Script" to create your customized Google Ads script
4. Copy the generated script and paste it into your Google Ads account

## Implementation Details

- Built with HTML, CSS, and JavaScript
- Uses Bootstrap 5 for responsive design
- No server-side processing required - everything runs in your browser

## Files

- `index.html` - The main HTML file containing the form and result display
- `styles.css` - Custom CSS styles for the application
- `script.js` - JavaScript code that handles form submission and script generation
