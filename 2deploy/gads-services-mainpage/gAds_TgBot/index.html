<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Ads скрипт для відправки інфи в Telegram</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h2 class="mb-0">Google Ads скрипт для відправки інфи в Telegram</h2>
                    </div>
                    <div class="card-body">
                        <form id="scriptForm">
                            <div class="mb-3">
                                <label for="telegramToken" class="form-label">Telegram Token:</label>
                                <input type="text" class="form-control" id="telegramToken" 
                                    placeholder="Наприклад: 5734567890:AAHEsM-ExampleTokenHere123" required>
                            </div>
                            <div class="mb-3">
                                <label for="telegramChatId" class="form-label">Telegram Chat ID:</label>
                                <input type="text" class="form-control" id="telegramChatId" 
                                    placeholder="Наприклад: -100********* або *********" required>
                            </div>
                            <div class="mb-3">
                                <label for="accountTag" class="form-label">Тег аккаунта:</label>
                                <input type="text" class="form-control" id="accountTag" 
                                    placeholder="Введіть назву аккаунта в антіку" required>
                            </div>
                            <button type="submit" class="btn btn-success">Генерувати унікалізований скрипт</button>
                        </form>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h3 class="mb-0">Інструкція</h3>
                    </div>
                    <div class="card-body">
                        <div class="mb-4">
                            <h4>Генератор скриптів Google Ads для Telegram</h4>
                            <p>Цей веб-додаток дозволяє створювати налаштований скрипт Google Ads, який надсилає інформацію про витрати акаунта в чат Telegram. Додаток створює унікальний скрипт з випадковими назвами змінних щоразу, коли ви його генеруєте.</p>
                            
                            <h5>Особливості</h5>
                            <ul>
                                <li>Проста форма для введення вашого токена Telegram, ID чату та тегу акаунта</li>
                                <li>Автоматична рандомізація назв змінних при кожній генерації скрипта</li>
                                <li>Кнопка копіювання для легкого копіювання скрипта</li>
                                <li>Адаптивний дизайн з використанням Bootstrap</li>
                            </ul>
                            
                            <h5>Як користуватися:</h5>
                            <ol>
                                <li>Заповніть форму своєю інформацією:</li>
                                <ul>
                                    <li><strong>Telegram Token</strong>: Ваш токен бота від BotFather (наприклад, <code>5734567890:AAHEsM-ExampleTokenHere123</code>)</li>
                                    <li><strong>Telegram Chat ID</strong>: ID вашого чату або групи (наприклад, <code>-100*********</code> або <code>*********</code>)</li>
                                    <li><strong>Тег акаунта</strong>: Тег для ідентифікації цього акаунта у вашій статистиці</li>
                                </ul>
                                <li>Натисніть "Генерувати унікалізований скрипт" для створення вашого налаштованого скрипта Google Ads</li>
                                <li>Скопіюйте згенерований скрипт і вставте його у свій акаунт Google Ads</li>
                            </ol>
                            
                            <h5>Як отримати Telegram Chat ID:</h5>
                            <div class="alert alert-secondary">
                                <h6>Для особистого чату:</h6>
                                <ol>
                                    <li>Запустіть вашого бота в Telegram та напишіть йому будь-яке повідомлення</li>
                                    <li>Відвідайте <code>https://api.telegram.org/bot<span class="text-danger">ВАШ_ТОКЕН</span>/getUpdates</code></li>
                                    <li>Знайдіть в JSON-відповіді поле <code>"id"</code> в секції <code>"from"</code> - це і є ваш Chat ID</li>
                                </ol>
                                
                                <h6>Для групи:</h6>
                                <ol>
                                    <li>Додайте вашого бота до групи</li>
                                    <li>Напишіть будь-яке повідомлення в групі</li>
                                    <li>Відвідайте <code>https://api.telegram.org/bot<span class="text-danger">ВАШ_ТОКЕН</span>/getUpdates</code></li>
                                    <li>Знайдіть в JSON-відповіді поле <code>"id"</code> в секції <code>"chat"</code> - це буде негативне число</li>
                                </ol>
                                
                                <h6>Для каналу:</h6>
                                <ol>
                                    <li>Додайте вашого бота як адміністратора до каналу</li>
                                    <li>Опублікуйте будь-яке повідомлення в каналі</li>
                                    <li>Відвідайте <code>https://api.telegram.org/bot<span class="text-danger">ВАШ_ТОКЕН</span>/getUpdates</code></li>
                                    <li>Знайдіть в JSON-відповіді поле <code>"id"</code> в секції <code>"chat"</code> - для каналів це буде починатися з <code>-100</code></li>
                                </ol>
                                
                                <p class="mb-0"><strong>Примітка:</strong> Якщо ви не бачите оновлень в JSON-відповіді, спробуйте додати параметр <code>?offset=-1</code> до URL.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="resultContainer" class="card d-none">
                    <div class="card-header bg-success text-white">
                        <h3 class="mb-0">Ваш унікалізований скрипт готовий</h3>
                    </div>
                    <div class="card-body">
                        <p>Скопіюйте цей скрипт для використання в Google Ads:</p>
                        <div class="code-container">
                            <pre><code id="generatedScript" class="language-javascript"></code></pre>
                        </div>
                        <div class="d-flex mt-3">
                            <button id="copyButton" class="btn btn-primary me-2">Скопіювати скрипт</button>
                            <button id="backButton" class="btn btn-secondary">← Повернутися до форми</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
