<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Ads Budget Script Generator with Telegram</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container py-4">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card shadow">
                    <div class="card-body p-4">
                        <h1 class="text-center mb-4">Google Ads Budget Script Generator</h1>
                        
                        <div class="alert alert-primary">
                            <p class="mb-0">Цей інструмент генерує унікалізований скрипт для Google Ads, який автоматично збільшує бюджет вказаної кампанії та надсилає сповіщення в Telegram.</p>
                        </div>
                        
                        <div class="alert alert-warning">
                            <h5 class="mb-2">Підтримувані типи кампаній для зміни бюджету:</h5>
                            <ul class="mb-2">
                                <li>Performance Max (включаючи PMax для роздрібної торгівлі)</li>
                                <li>Video (Відеокампанії)</li>
                                <li>Shopping (Торгові кампанії, стандартні та PMax для роздрібної торгівлі)</li>
                                <li>Display (Медійні кампанії, включаючи Розумні медійні)</li>
                                <li>Search (Пошукові кампанії)</li>
                                <li>Smart Campaigns (Розумні кампанії)</li>
                                <li>Hotel (Готельні кампанії)</li>
                            </ul>
                            <p class="small mb-0">Примітка: Кампанії для додатків (App Campaigns) можуть бути знайдені, якщо їх назва унікальна і вони доступні через загальний селектор `AdsApp.campaigns()`, але спеціального селектора для них у цьому скрипті немає. Скрипт шукає за назвою серед перелічених типів, пробуючи більш специфічні селектори першими.</p>
                        </div>
                        
                        <form id="scriptForm">
                            <div class="card mb-4">
                                <div class="card-header bg-light">
                                    <h3 class="mb-0">Налаштування Google Ads</h3>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="campaignName" class="form-label">Назва рекламної кампанії (точно як в Google Ads):</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" name="campaignName" id="campaignName" 
                                                placeholder="Наприклад: Website traffic-Search-1" value="Website traffic-Search-1" required>
                                            <button class="btn btn-outline-secondary dropdown-toggle history-toggle" type="button" 
                                                data-bs-toggle="dropdown" aria-expanded="false" title="Історія значень">
                                                <i class="fas fa-history"></i>
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-end history-menu" id="campaignNameHistory"></ul>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="maxBudget" class="form-label">Максимальний денний бюджет (у валюті акаунту):</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" name="maxBudget" id="maxBudget" 
                                                placeholder="Наприклад: 1300" value="1300" min="1" step="any" required>
                                            <button class="btn btn-outline-secondary dropdown-toggle history-toggle" type="button" 
                                                data-bs-toggle="dropdown" aria-expanded="false" title="Історія значень">
                                                <i class="fas fa-history"></i>
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-end history-menu" id="maxBudgetHistory"></ul>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="maxTimes" class="form-label">Максимальна кількість збільшень бюджету за один запуск скрипту:</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" name="maxTimes" id="maxTimes" 
                                                placeholder="Наприклад: 10" value="10" min="1" step="1" required>
                                            <button class="btn btn-outline-secondary dropdown-toggle history-toggle" type="button" 
                                                data-bs-toggle="dropdown" aria-expanded="false" title="Історія значень">
                                                <i class="fas fa-history"></i>
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-end history-menu" id="maxTimesHistory"></ul>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="upPercent" class="form-label">Відсоток збільшення бюджету за крок (наприклад, 0.1 для 10%):</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" name="upPercent" id="upPercent" 
                                                placeholder="Наприклад: 0.1" value="0.1" min="0.01" max="2" step="0.01" required>
                                            <button class="btn btn-outline-secondary dropdown-toggle history-toggle" type="button" 
                                                data-bs-toggle="dropdown" aria-expanded="false" title="Історія значень">
                                                <i class="fas fa-history"></i>
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-end history-menu" id="upPercentHistory"></ul>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="card mb-4">
                                <div class="card-header bg-light">
                                    <h3 class="mb-0">Налаштування Telegram (опціонально)</h3>
                                </div>
                                <div class="card-body">
                                    <p>Залиште поля порожніми, якщо не бажаєте отримувати сповіщення.</p>
                                    <div class="mb-3">
                                        <label for="telegramBotToken" class="form-label">Telegram Bot Token:</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="telegramBotToken" 
                                                placeholder="Наприклад: 1234567890:AAH-aBcDeFgHiJkLmNoPqRsTuVwXyZ">
                                            <button class="btn btn-outline-secondary dropdown-toggle history-toggle" type="button" 
                                                data-bs-toggle="dropdown" aria-expanded="false" title="Історія значень">
                                                <i class="fas fa-history"></i>
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-end history-menu" id="telegramBotTokenHistory"></ul>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="telegramChatId" class="form-label">Telegram Chat ID:</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="telegramChatId" 
                                                placeholder="Наприклад: -100123456789 або 987654321">
                                            <button class="btn btn-outline-secondary dropdown-toggle history-toggle" type="button" 
                                                data-bs-toggle="dropdown" aria-expanded="false" title="Історія значень">
                                                <i class="fas fa-history"></i>
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-end history-menu" id="telegramChatIdHistory"></ul>
                                        </div>
                                    </div>

                                    <div class="accordion mt-4" id="telegramHelpAccordion">
                                        <div class="accordion-item">
                                            <h2 class="accordion-header" id="telegramHelpHeading">
                                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                                    data-bs-target="#telegramHelpContent" aria-expanded="false" aria-controls="telegramHelpContent">
                                                    Як отримати Telegram Bot Token i Chat ID
                                                </button>
                                            </h2>
                                            <div id="telegramHelpContent" class="accordion-collapse collapse" aria-labelledby="telegramHelpHeading">
                                                <div class="accordion-body">
                                                    <h5>Для отримання Bot Token:</h5>
                                                    <ol>
                                                        <li>У Telegram знайдіть бота @BotFather.</li>
                                                        <li>Відправте йому команду <code>/newbot</code>.</li>
                                                        <li>Слідуйте інструкціям, щоб створити нового бота.</li>
                                                        <li>Після створення ви отримаєте повідомлення з токеном вашого бота.</li>
                                                    </ol>
                                                    <h5>Для отримання Chat ID:</h5>
                                                    <ol>
                                                        <li><strong>Для особистого чату:</strong>
                                                            <ul>
                                                                <li>Знайдіть бота @userinfobot (або @myidbot, @getmyid_bot).</li>
                                                                <li>Напишіть йому будь-яке повідомлення (наприклад, <code>/start</code>).</li>
                                                                <li>Бот відповість вашим ID.</li>
                                                            </ul>
                                                        </li>
                                                        <li><strong>Для групового чату:</strong>
                                                            <ul>
                                                                <li>Додайте вашого щойно створеного бота (з п.1) до потрібної групи.</li>
                                                                <li>Надішліть будь-яке повідомлення в цю групу.</li>
                                                                <li>Відкрийте у браузері URL (замініть <code>&lt;YourBotToken&gt;</code> на токен вашого бота):<br>
                                                                    <code>https://api.telegram.org/bot&lt;YourBotToken&gt;/getUpdates</code></li>
                                                                <li>У відповіді JSON знайдіть об'єкт, що відповідає вашому повідомленню в групі. Ваш Chat ID буде значенням поля <code>chat</code> -> <code>id</code> (зазвичай починається з мінуса для груп).</li>
                                                            </ul>
                                                        </li>
                                                    </ol>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="button" id="generateBtn" class="btn btn-success btn-lg">
                                    <i class="fas fa-code me-2"></i>Генерувати унікалізований скрипт
                                </button>
                            </div>
                        </form>

                        <div class="mt-4" id="resultSection" style="display:none;">
                            <div class="d-grid gap-2 mb-3">
                                <button id="copyBtn" class="btn btn-primary">
                                    <i class="fas fa-copy me-2"></i>Скопіювати скрипт
                                </button>
                            </div>
                            <div class="code-container">
                                <pre id="scriptContent" class="p-3 bg-dark text-light rounded"></pre>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="text-center mt-3">
                    <small class="text-muted">© 2025 Google Ads Budget Script Generator</small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="script.js"></script>
</body>
</html>
