// Google Ads Budget Script Generator with Telegram

// Form fields to track for history
const formFields = [
    'campaignName',
    'maxBudget',
    'maxTimes',
    'upPercent',
    'telegramBotToken',
    'telegramChatId'
];

// Maximum number of history items to store per field
const MAX_HISTORY_ITEMS = 3;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    // Load saved values from localStorage
    loadSavedValues();
    
    // Set up event listeners
    document.getElementById('generateBtn').addEventListener('click', generateScript);
    document.getElementById('copyBtn').addEventListener('click', copyScript);
    
    // Set up history dropdown listeners
    formFields.forEach(field => {
        const input = document.getElementById(field);
        if (input) {
            // Save value when input changes
            input.addEventListener('change', () => saveFieldValue(field, input.value));
            
            // Set up history dropdown items
            populateHistoryDropdown(field);
        }
    });
});

// Load saved values from localStorage
function loadSavedValues() {
    formFields.forEach(field => {
        const savedValues = getSavedValues(field);
        if (savedValues.length > 0) {
            // Set the most recent value in the input field
            const input = document.getElementById(field);
            if (input && savedValues[0]) {
                input.value = savedValues[0];
            }
        }
    });
}

// Get saved values for a field from localStorage
function getSavedValues(field) {
    const savedData = localStorage.getItem(`gads_${field}_history`);
    return savedData ? JSON.parse(savedData) : [];
}

// Save a value to the field's history
function saveFieldValue(field, value) {
    if (!value.trim()) return; // Don't save empty values
    
    let values = getSavedValues(field);
    
    // Remove the value if it already exists (to avoid duplicates)
    values = values.filter(v => v !== value);
    
    // Add the new value at the beginning
    values.unshift(value);
    
    // Limit to MAX_HISTORY_ITEMS
    values = values.slice(0, MAX_HISTORY_ITEMS);
    
    // Save to localStorage
    localStorage.setItem(`gads_${field}_history`, JSON.stringify(values));
    
    // Update the dropdown
    populateHistoryDropdown(field);
}

// Populate history dropdown for a field
function populateHistoryDropdown(field) {
    const historyMenu = document.getElementById(`${field}History`);
    if (!historyMenu) return;
    
    const values = getSavedValues(field);
    
    // Clear existing items
    historyMenu.innerHTML = '';
    
    if (values.length === 0) {
        const emptyItem = document.createElement('li');
        emptyItem.innerHTML = '<span class="dropdown-item disabled">Немає збережених значень</span>';
        historyMenu.appendChild(emptyItem);
        return;
    }
    
    // Add history items
    values.forEach(value => {
        const item = document.createElement('li');
        const link = document.createElement('a');
        link.className = 'dropdown-item history-item';
        link.href = '#';
        link.textContent = value;
        link.addEventListener('click', (e) => {
            e.preventDefault();
            document.getElementById(field).value = value;
        });
        item.appendChild(link);
        historyMenu.appendChild(item);
    });
}

// Generate a random variable name
function generateRandomName(prefix = 'customVar') {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let result = prefix + '_';
    for (let i = 0; i < 8; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

// Generate the Google Ads script
function generateScript() {
    // Validate form
    const campaignName = document.getElementById('campaignName').value;
    const maxBudget = document.getElementById('maxBudget').value;
    const maxTimes = document.getElementById('maxTimes').value;
    const upPercent = document.getElementById('upPercent').value;
    const telegramBotToken = document.getElementById('telegramBotToken').value.trim();
    const telegramChatId = document.getElementById('telegramChatId').value.trim();

    // Basic validation
    if (!campaignName || !maxBudget || !maxTimes || !upPercent) {
        showAlert('Будь ласка, заповніть всі обов\'язкові поля форми.', 'danger');
        if (!campaignName) document.getElementById('campaignName').focus();
        else if (!maxBudget) document.getElementById('maxBudget').focus();
        else if (!maxTimes) document.getElementById('maxTimes').focus();
        else if (!upPercent) document.getElementById('upPercent').focus();
        return;
    }
    
    if (parseFloat(maxBudget) <= 0 || parseInt(maxTimes, 10) <= 0 || parseFloat(upPercent) <= 0) {
        showAlert('Значення бюджету, кількості збільшень та відсотку мають бути позитивними.', 'danger');
        return;
    }
    
    // Save values to history
    saveFieldValue('campaignName', campaignName);
    saveFieldValue('maxBudget', maxBudget);
    saveFieldValue('maxTimes', maxTimes);
    saveFieldValue('upPercent', upPercent);
    if (telegramBotToken) saveFieldValue('telegramBotToken', telegramBotToken);
    if (telegramChatId) saveFieldValue('telegramChatId', telegramChatId);
    
    // Generate random variable names
    const campaignNameVar = generateRandomName('targetCampaignNameCfg');
    const maxBudgetVar = generateRandomName('maxBudgetCfg');
    const maxTimesVar = generateRandomName('maxIncrementsCfg');
    const upPercentVar = generateRandomName('incrementPercentCfg');
    const telegramBotTokenVar = generateRandomName('telegramTokenCfg');
    const telegramChatIdVar = generateRandomName('telegramChatIdCfg');
    const sendTelegramFunc = generateRandomName('sendTelegramNotificationUtil');
    const findCampaignFunc = generateRandomName('findCampaignUtil');
    const campaignVar = generateRandomName('campaignObj');
    const initialBudgetVar = generateRandomName('initialBudgetVal');
    const incrementCounterVar = generateRandomName('incrementCounter');
    const currentBudgetVar = generateRandomName('currentBudgetVal');
    const newBudgetVar = generateRandomName('newBudgetTarget');
    const finalBudgetVar = generateRandomName('finalBudgetVal');
    const messageVar = generateRandomName('statusMessage');
    
    // Create the script
    const script = `var ${campaignNameVar} = '${campaignName.replace(/'/g, "\\'")}';
var ${maxBudgetVar} = ${maxBudget};
var ${maxTimesVar} = ${maxTimes};
var ${upPercentVar} = ${upPercent};
var ${telegramBotTokenVar} = '${telegramBotToken.replace(/'/g, "\\'")}';
var ${telegramChatIdVar} = '${telegramChatId.replace(/'/g, "\\'")}';

function ${sendTelegramFunc}(${messageVar}) {
  if (!${telegramBotTokenVar} || !${telegramChatIdVar}) {
    Logger.log("Telegram Bot Token або Chat ID не вказані. Сповіщення пропущено.");
    return;
  }
  var url = 'https://api.telegram.org/bot' + ${telegramBotTokenVar} + '/sendMessage';
  var payload = {
    'chat_id': ${telegramChatIdVar},
    'text': ${messageVar},
    'parse_mode': 'HTML'
  };
  var options = {
    'method': 'post',
    'contentType': 'application/json',
    'payload': JSON.stringify(payload),
    'muteHttpExceptions': true
  };
  try {
    var response = UrlFetchApp.fetch(url, options);
    Logger.log("Спроба надіслати сповіщення в Telegram. Код відповіді: " + response.getResponseCode() + ". Відповідь: " + response.getContentText());
  } catch (e) {
    Logger.log("Помилка надсилання сповіщення в Telegram: " + e.toString());
  }
}

function ${findCampaignFunc}(name) {
  var selectors = [];
  
  // Додаємо всі підтримувані типи кампаній
  try { selectors.push({ selector: AdsApp.performanceMaxCampaigns(), type: 'Performance Max' }); } catch(e){ Logger.log('Немає доступу до Performance Max кампаній: ' + e); }
  try { selectors.push({ selector: AdsApp.videoCampaigns(), type: 'Video' }); } catch(e){ Logger.log('Немає доступу до Video кампаній: ' + e); }
  try { selectors.push({ selector: AdsApp.shoppingCampaigns(), type: 'Shopping' }); } catch(e){ Logger.log('Немає доступу до Shopping кампаній: ' + e); }
  
  // Для Display, Search, Smart і Hotel кампаній використовуємо загальний селектор з фільтрацією
  try { 
    selectors.push({ 
      selector: AdsApp.campaigns().withCondition('CampaignType = DISPLAY'), 
      type: 'Display' 
    }); 
  } catch(e){ Logger.log('Немає доступу до Display кампаній: ' + e); }
  
  try { 
    selectors.push({ 
      selector: AdsApp.campaigns().withCondition('CampaignType = SEARCH'), 
      type: 'Search' 
    }); 
  } catch(e){ Logger.log('Немає доступу до Search кампаній: ' + e); }
  
  try { 
    selectors.push({ 
      selector: AdsApp.campaigns().withCondition('CampaignType = SMART'), 
      type: 'Smart' 
    }); 
  } catch(e){ Logger.log('Немає доступу до Smart кампаній: ' + e); }
  
  try { 
    selectors.push({ 
      selector: AdsApp.campaigns().withCondition('CampaignType = HOTEL'), 
      type: 'Hotel' 
    }); 
  } catch(e){ Logger.log('Немає доступу до Hotel кампаній: ' + e); }
  
  // Загальний селектор для всіх інших типів кампаній
  try { selectors.push({ selector: AdsApp.campaigns(), type: 'Other' }); } catch(e){ Logger.log('Немає доступу до інших кампаній: ' + e); }
  
  // Create variations of the name to try
  var nameVariations = [
    name,  // Original name
    name.replace(/-/g, ' '),  // Spaces instead of dashes
    name.replace(/"/g, ''),  // Remove quotes
    name.replace(/"/g, '\\"')  // Escape quotes
  ];
  
  // Log the variations we'll try
  Logger.log('Пошук кампанії з різними варіантами назви:');
  for (var v = 0; v < nameVariations.length; v++) {
    Logger.log('- Варіант ' + (v+1) + ': "' + nameVariations[v] + '"');
  }
  
  // Try with each name variation
  for (var v = 0; v < nameVariations.length; v++) {
    var currentName = nameVariations[v];
    Logger.log('Спроба пошуку з варіантом назви: "' + currentName + '"');
    
    for (var i = 0; i < selectors.length; i++) {
      try {
        var campaignIterator = selectors[i].selector
          .withCondition('CampaignName = "' + currentName + '"')
          .withLimit(1)
          .get();
        if (campaignIterator.hasNext()) {
          Logger.log('Кампанію "' + currentName + '" знайдено в типі: ' + selectors[i].type);
          return campaignIterator.next();
        }
      } catch (e) {
        Logger.log('Помилка при пошуку в типі ' + selectors[i].type + ': ' + e.toString());
        continue;
      }
    }
  }
  
  // If exact match failed, try case-insensitive search for Cyrillic and Latin characters
  Logger.log('Спроба пошуку без урахування регістру...');
  try {
    var allCampaigns = AdsApp.campaigns().get();
    while (allCampaigns.hasNext()) {
      var campaign = allCampaigns.next();
      var campaignName = campaign.getName();
      
      // Try case-insensitive comparison with all variations
      for (var v = 0; v < nameVariations.length; v++) {
        if (campaignName.toLowerCase() === nameVariations[v].toLowerCase()) {
          Logger.log('Знайдено кампанію з подібною назвою (без урахування регістру): "' + campaignName + '"');
          return campaign;
        }
      }
      
      // Try to see if campaign name contains our search term or vice versa
      for (var v = 0; v < nameVariations.length; v++) {
        if (campaignName.toLowerCase().indexOf(nameVariations[v].toLowerCase()) !== -1 ||
            nameVariations[v].toLowerCase().indexOf(campaignName.toLowerCase()) !== -1) {
          Logger.log('Знайдено кампанію, яка містить пошуковий запит або міститься в ньому: "' + campaignName + '"');
          return campaign;
        }
      }
    }
  } catch (e) {
    Logger.log('Помилка при пошуку кампаній без урахування регістру: ' + e.toString());
  }
  
  // List all campaigns to help with debugging
  try {
    var allCampaigns = AdsApp.campaigns().get();
    var campaignList = 'Доступні кампанії: ';
    var count = 0;
    while (allCampaigns.hasNext() && count < 10) {
      var camp = allCampaigns.next();
      campaignList += '\\n- "' + camp.getName() + '" (ID: ' + camp.getId() + ')';
      count++;
    }
    if (allCampaigns.hasNext()) {
      campaignList += '\\n- ... та ще інші кампанії';
    }
    Logger.log(campaignList);
    throw new Error('Кампанію не знайдено: "' + name + '". ' + campaignList);
  } catch (e) {
    throw new Error('Кампанію не знайдено: "' + name + '". Перевірте назву та тип кампанії.');
  }
}

function main() {
  var ${campaignVar};
  try {
    ${campaignVar} = ${findCampaignFunc}(${campaignNameVar});
  } catch (e) {
    Logger.log(e.toString());
    ${sendTelegramFunc}("❌ <b>Помилка Google Ads Script:</b>\\n" + e.toString());
    return;
  }
  var ${initialBudgetVar} = ${campaignVar}.getBudget().getAmount();
  var ${incrementCounterVar} = 0;
  Logger.log("Кампанія: '" + ${campaignNameVar} + "'. Початковий бюджет: " + ${initialBudgetVar}.toFixed(2));
  while (${incrementCounterVar} < ${maxTimesVar} && ${campaignVar}.getBudget().getAmount() < ${maxBudgetVar}) {
    var ${currentBudgetVar} = ${campaignVar}.getBudget().getAmount();
    var ${newBudgetVar} = ${currentBudgetVar} * (1 + ${upPercentVar});
    if (${newBudgetVar} > ${maxBudgetVar}) {
      ${newBudgetVar} = ${maxBudgetVar};
    }
    if (${newBudgetVar}.toFixed(2) <= ${currentBudgetVar}.toFixed(2)) {
        Logger.log("Новий розрахований бюджет (" + ${newBudgetVar}.toFixed(2) + ") не більший за поточний (" + ${currentBudgetVar}.toFixed(2) + "). Зупинка збільшення.");
        break;
    }
    ${campaignVar}.getBudget().setAmount(${newBudgetVar});
    ${incrementCounterVar}++;
    Logger.log("Ітерація " + ${incrementCounterVar} + ": Бюджет встановлено на " + ${campaignVar}.getBudget().getAmount().toFixed(2));
    if (${campaignVar}.getBudget().getAmount() >= ${maxBudgetVar}) {
      Logger.log("Досягнуто або перевищено максимальний бюджет.");
      break;
    }
  }
  var ${finalBudgetVar} = ${campaignVar}.getBudget().getAmount();
  var ${messageVar};
  if (${incrementCounterVar} > 0) {
    ${messageVar} = "✅ <b>Google Ads Script: Бюджет оновлено</b>\\n" +
               "Кампанія: <b>'" + ${campaignNameVar} + "'</b>\\n" +
               "Старий бюджет: " + ${initialBudgetVar}.toFixed(2) + "\\n" +
               "Новий бюджет: " + ${finalBudgetVar}.toFixed(2) + "\\n" +
               "Кількість збільшень: " + ${incrementCounterVar};
    if (${incrementCounterVar} >= ${maxTimesVar} && ${finalBudgetVar} < ${maxBudgetVar}) {
      ${messageVar} += "\\nДосягнуто ліміту кількості збільшень (" + ${maxTimesVar} + ").";
    }
    if (${finalBudgetVar} >= ${maxBudgetVar}) {
      ${messageVar} += "\\nДосягнуто максимального бюджету (" + ${maxBudgetVar}.toFixed(2) + ").";
    }
  } else if (${initialBudgetVar} >= ${maxBudgetVar}) {
     ${messageVar} = "ℹ️ <b>Google Ads Script: Бюджет не змінено</b>\\n" +
               "Кампанія: <b>'" + ${campaignNameVar} + "'</b>\\n" +
               "Поточний бюджет: " + ${finalBudgetVar}.toFixed(2) + "\\n" +
               "Причина: Бюджет вже на рівні або вище максимального (" + ${maxBudgetVar}.toFixed(2) + ").";
  } else {
     ${messageVar} = "ℹ️ <b>Google Ads Script: Бюджет не змінено</b>\\n" +
               "Кампанія: <b>'" + ${campaignNameVar} + "'</b>\\n" +
               "Поточний бюджет: " + ${finalBudgetVar}.toFixed(2) + "\\n" +
               "Причина: Збільшення не відбулися (можливо, досягнуто ліміт збільшень, поточний бюджет вже максимальний, або новий розрахований бюджет не більший за поточний).";
  }
  Logger.log("Підсумкове повідомлення: " + ${messageVar}.replace(/<b>|<\\/b>/g, "").replace(/\\\\n/g, '\\n'));
  ${sendTelegramFunc}(${messageVar});
}`;
    
    // Display the script
    const scriptContentEl = document.getElementById('scriptContent');
    scriptContentEl.textContent = script;
    
    // Show result section
    document.getElementById('resultSection').style.display = 'block';
    
    // Scroll to result
    document.getElementById('resultSection').scrollIntoView({ behavior: 'smooth' });
    
    showAlert('Скрипт успішно згенеровано!', 'success');
}

// Copy script to clipboard
function copyScript() {
    const scriptText = document.getElementById('scriptContent').textContent;
    const copyBtn = document.getElementById('copyBtn');
    
    navigator.clipboard.writeText(scriptText)
        .then(() => {
            // Visual feedback
            copyBtn.innerHTML = '<i class="fas fa-check me-2"></i>Скопійовано!';
            copyBtn.classList.add('flash-animation');
            
            setTimeout(() => {
                copyBtn.innerHTML = '<i class="fas fa-copy me-2"></i>Скопіювати скрипт';
                copyBtn.classList.remove('flash-animation');
            }, 2000);
        })
        .catch(err => {
            console.error('Помилка копіювання: ', err);
            copyBtn.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Помилка копіювання';
            
            setTimeout(() => {
                copyBtn.innerHTML = '<i class="fas fa-copy me-2"></i>Скопіювати скрипт';
            }, 2000);
            
            // Fallback method
            fallbackCopyTextToClipboard(scriptText);
        });
}

// Fallback copy method for older browsers
function fallbackCopyTextToClipboard(text) {
    try {
        const textArea = document.createElement("textarea");
        textArea.value = text;
        textArea.style.position = "fixed";
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.width = "2em";
        textArea.style.height = "2em";
        textArea.style.padding = "0";
        textArea.style.border = "none";
        textArea.style.outline = "none";
        textArea.style.boxShadow = "none";
        textArea.style.background = "transparent";
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        const successful = document.execCommand('copy');
        const copyBtn = document.getElementById('copyBtn');
        
        if (successful) {
            copyBtn.innerHTML = '<i class="fas fa-check me-2"></i>Скопійовано!';
        } else {
            copyBtn.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Помилка копіювання';
        }
        
        document.body.removeChild(textArea);
        
        setTimeout(() => {
            copyBtn.innerHTML = '<i class="fas fa-copy me-2"></i>Скопіювати скрипт';
        }, 2000);
    } catch (err) {
        console.error('Помилка копіювання (fallback): ', err);
        alert('Не вдалося скопіювати. Спробуйте виділити текст та скопіювати вручну (Ctrl+C або Cmd+C).');
    }
}

// Show alert message
function showAlert(message, type) {
    // Check if alert container exists, if not create it
    let alertContainer = document.getElementById('alertContainer');
    if (!alertContainer) {
        alertContainer = document.createElement('div');
        alertContainer.id = 'alertContainer';
        alertContainer.className = 'position-fixed top-0 start-50 translate-middle-x p-3';
        alertContainer.style.zIndex = '1050';
        document.body.appendChild(alertContainer);
    }
    
    // Create alert element
    const alertId = 'alert-' + Date.now();
    const alertEl = document.createElement('div');
    alertEl.className = `alert alert-${type} alert-dismissible fade show`;
    alertEl.id = alertId;
    alertEl.role = 'alert';
    alertEl.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    // Add to container
    alertContainer.appendChild(alertEl);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        const alert = document.getElementById(alertId);
        if (alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }
    }, 5000);
}
