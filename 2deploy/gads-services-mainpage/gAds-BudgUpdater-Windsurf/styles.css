/* Custom styles for Google Ads Budget Script Generator */

body {
    background-color: #f8f9fa;
    color: #333;
    line-height: 1.6;
}

.card {
    border: none;
    border-radius: 10px;
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
}

h1, h2, h3, h4, h5 {
    color: #2c3e50;
}

.form-label {
    font-weight: 600;
    color: #495057;
}

.form-control:focus {
    border-color: #4CAF50;
    box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25);
}

.btn-success {
    background-color: #4CAF50;
    border-color: #4CAF50;
}

.btn-success:hover, .btn-success:focus {
    background-color: #45a049;
    border-color: #45a049;
}

.btn-primary {
    background-color: #2196F3;
    border-color: #2196F3;
}

.btn-primary:hover, .btn-primary:focus {
    background-color: #0b7dda;
    border-color: #0b7dda;
}

.alert-primary {
    background-color: #e7f3fe;
    border-left: 5px solid #2196F3;
    border-top: 1px solid #d1e7fc;
    border-right: 1px solid #d1e7fc;
    border-bottom: 1px solid #d1e7fc;
}

.alert-warning {
    background-color: #fff3e0;
    border-left: 5px solid #FF9800;
    border-top: 1px solid #ffe0b2;
    border-right: 1px solid #ffe0b2;
    border-bottom: 1px solid #ffe0b2;
}

pre {
    background-color: #282c34;
    color: #abb2bf;
    border-radius: 6px;
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
    border: 1px solid #3d424d;
    font-family: Consolas, Monaco, 'Andale Mono', monospace;
    font-size: 14px;
}

.code-container {
    position: relative;
    max-height: 500px;
    overflow-y: auto;
}

.accordion-button:not(.collapsed) {
    background-color: #f0fff4;
    color: #155724;
}

.accordion-button:focus {
    box-shadow: 0 0 0 0.25rem rgba(76, 175, 80, 0.25);
}

.history-menu {
    max-height: 200px;
    overflow-y: auto;
}

.history-item {
    cursor: pointer;
    padding: 8px 16px;
}

.history-item:hover {
    background-color: #f8f9fa;
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .card-body {
        padding: 15px;
    }
    
    h1 {
        font-size: 1.8rem;
    }
    
    h3 {
        font-size: 1.3rem;
    }
    
    .btn {
        padding: 8px 16px;
    }
}

/* Dark mode toggle styles */
.form-check-input:checked {
    background-color: #4CAF50;
    border-color: #4CAF50;
}

/* Animation for copy button */
@keyframes flash {
    0% { background-color: #2196F3; }
    50% { background-color: #4CAF50; }
    100% { background-color: #2196F3; }
}

.flash-animation {
    animation: flash 1s;
}
