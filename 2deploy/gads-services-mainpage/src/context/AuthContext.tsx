import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User, onAuthStateChanged } from 'firebase/auth';
import { auth } from '../firebase';

// Define UserStatus type (can be moved to a shared types file later)
export type UserStatus = 'new' | 'alive' | 'admin' | 'banned';

// --- IMPORTANT DEVELOPMENT NOTE ---
// The isAdmin and userStatus checks below are temporary client-side checks.
// For a production application, admin status MUST be managed using Firebase Custom Claims
// and user status (new, alive, banned) via Firestore/backend logic.
// Client-side checks like this are not secure for access control.
const ADMIN_EMAIL_PRIMARY = '<EMAIL>'; // From memory
const ADMIN_EMAIL_FALLBACK = '<EMAIL>'; // From memory (special handling)

interface AuthContextType {
  currentUser: User | null;
  isAdmin: boolean;
  userStatus: UserStatus | null;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [isAdmin, setIsAdmin] = useState(false);
  const [userStatus, setUserStatus] = useState<UserStatus | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      setCurrentUser(user);
      if (user) {
        if (user.email === ADMIN_EMAIL_PRIMARY || user.email === ADMIN_EMAIL_FALLBACK) {
          setIsAdmin(true);
          setUserStatus('admin');
        } else {
          // For now, default other authenticated users to 'new'
          // In a real app, this would be fetched from Firestore/backend
          setIsAdmin(false);
          setUserStatus('new'); 
        }
      } else {
        setIsAdmin(false);
        setUserStatus(null);
      }
      setLoading(false);
    });

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, []);

  const value = {
    currentUser,
    isAdmin,
    userStatus,
    loading,
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};
