import React from 'react';
import { Routes, Route } from 'react-router-dom';
import Header from './components/Header';
import Hero from './components/Hero';
import Services from './components/Services';
import Footer from './components/Footer';
import LoginPage from './pages/LoginPage';
import SignupPage from './pages/SignupPage';
import ForgotPasswordPage from './pages/ForgotPasswordPage';
import ThankYouPage from './pages/ThankYouPage';
import DashboardPage from './pages/DashboardPage';
import UserManagementPage from './pages/UserManagementPage';
import TelegramScriptGeneratorPage from './pages/TelegramScriptGeneratorPage';
import AirtablePnlScriptGeneratorPage from './pages/AirtablePnlScriptGeneratorPage';
import SearchAdsScriptGeneratorPage from './pages/SearchAdsScriptGeneratorPage';
import GAdsBudgetUpdaterPage from './pages/GAdsBudgetUpdaterPage';

const HomePageContent: React.FC = () => {
  return (
    <>
      <Header />
      <main className="flex-grow pt-40">
        <Hero />
        <Services /> 
      </main>
      <Footer />
    </>
  );
};

const App: React.FC = () => {
  return (
    <div className="font-sans text-neutral-800 bg-gradient-to-br from-background-start to-background-end min-h-screen flex flex-col">
      <Routes>
        <Route path="/tools/telegram-script-generator" element={<TelegramScriptGeneratorPage />} />
        <Route path="/tools/airtable-pnl-script-generator" element={<AirtablePnlScriptGeneratorPage />} />
        <Route path="/tools/search-ads-script-generator" element={<SearchAdsScriptGeneratorPage />} />
        <Route path="/tools/gads-budget-updater" element={<GAdsBudgetUpdaterPage />} />
        <Route path="/login" element={<LoginPage />} />
        <Route path="/signup" element={<SignupPage />} />
        <Route path="/forgot-password" element={<ForgotPasswordPage />} />
        <Route path="/thank-you" element={<ThankYouPage />} />
        <Route path="/dashboard" element={<DashboardPage />} /> 
        <Route path="/admin/user-management" element={<UserManagementPage />} /> 
        <Route path="/" element={<HomePageContent />} /> 
      </Routes>
    </div>
  );
};

export default App;