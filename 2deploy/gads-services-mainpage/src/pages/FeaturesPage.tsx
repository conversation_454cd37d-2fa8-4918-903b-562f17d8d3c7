import React from 'react';
import Header from '../components/Header';
import Footer from '../components/Footer';

const FeaturesSection: React.FC = () => {
  return (
    <section id="features" className="py-16 lg:py-24 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-neutral-900">Our Features</h2>
          <p className="mt-4 text-lg text-neutral-700 max-w-2xl mx-auto">
            Discover the powerful features that make gAds the ultimate advertising solution for your business.
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* Feature 1 */}
          <div className="bg-neutral-50 p-6 rounded-lg shadow-sm">
            <h3 className="text-xl font-semibold text-primary-600 mb-3">Advanced Targeting</h3>
            <p className="text-neutral-700">
              Reach your ideal customers with precision using our advanced demographic, interest, and behavioral targeting options.
            </p>
          </div>
          {/* Feature 2 */}
          <div className="bg-neutral-50 p-6 rounded-lg shadow-sm">
            <h3 className="text-xl font-semibold text-primary-600 mb-3">Real-time Analytics</h3>
            <p className="text-neutral-700">
              Monitor your campaign performance with up-to-the-minute data and comprehensive analytics dashboards.
            </p>
          </div>
          {/* Feature 3 */}
          <div className="bg-neutral-50 p-6 rounded-lg shadow-sm">
            <h3 className="text-xl font-semibold text-primary-600 mb-3">Automated Optimization</h3>
            <p className="text-neutral-700">
              Let our AI-powered tools automatically optimize your bids and budgets for maximum ROI.
            </p>
          </div>
          {/* Add more features as needed */}
        </div>
      </div>
    </section>
  );
};

const FeaturesPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-neutral-100 flex flex-col">
      <Header />
      <main className="flex-grow pt-40 pb-12">
        <FeaturesSection />
      </main>
      <Footer />
    </div>
  );
};

export default FeaturesPage;
