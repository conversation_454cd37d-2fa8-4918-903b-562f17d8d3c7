import React, { useState } from 'react';
import Header from '../components/Header';
import Footer from '../components/Footer';
import { ArrowLeft, PlusCircle, Trash2, ClipboardCopy, CheckCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface CampaignParameters {
  finalUrl: string;
  campaignName: string;
  adGroupName: string;
  budget: string;
}

interface Headline {
  id: string;
  text: string;
}

interface Description {
  id: string;
  text: string;
}

interface Ad {
  id: string;
  headlines: Headline[];
  descriptions: Description[];
}

const MAX_HEADLINES = 15;
const MAX_DESCRIPTIONS = 4;
const HEADLINE_CHAR_LIMIT = 30;
const DESCRIPTION_CHAR_LIMIT = 90;

const generateId = () => `_${Math.random().toString(36).substr(2, 9)}`;

// Ported from src3.html
const generateVarName = (length: number = 8): string => {
  let result = '';
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
  const charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
};

// Ported and adapted from src3.html
const minifyScript = (scriptString: string): string => {
  let minified = scriptString;
  // Remove block comments
  minified = minified.replace(/\/\*[\s\S]*?\*\//g, '');
  // Remove line comments (ensure not to remove http:// or https://)
  minified = minified.replace(/([^:\/])\/\/.*/g, '$1');
  // Trim whitespace from each line and filter out empty lines
  minified = minified.split('\n').map(line => line.trim()).filter(line => line.length > 0).join('\n');
  // Replace multiple spaces with a single space
  minified = minified.replace(/\s\s+/g, ' ');
  return minified;
};

const SearchAdsScriptGeneratorPage: React.FC = () => {
  const navigate = useNavigate();

  const [campaignParams, setCampaignParams] = useState<CampaignParameters>({
    finalUrl: 'https://example.com',
    campaignName: 'My Search Campaign',
    adGroupName: 'My Ad Group',
    budget: '50',
  });

  const [ads, setAds] = useState<Ad[]>([
    { 
      id: generateId(), 
      headlines: [{ id: generateId(), text: '' }], 
      descriptions: [{ id: generateId(), text: '' }] 
    }
  ]);

  const [keywordsInput, setKeywordsInput] = useState<string>('keyword 1\nkeyword 2\nkeyword 3');

  const [generatedScript, setGeneratedScript] = useState<string>('');
  const [showResult, setShowResult] = useState<boolean>(false);
  const [isCopied, setIsCopied] = useState<boolean>(false);

  const handleCampaignParamChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCampaignParams(prev => ({ ...prev, [name]: value }));
  };

  const handleKeywordsChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setKeywordsInput(e.target.value);
  };

  const addAd = () => {
    setAds(prevAds => [
      ...prevAds,
      { 
        id: generateId(), 
        headlines: [{ id: generateId(), text: '' }], 
        descriptions: [{ id: generateId(), text: '' }] 
      }
    ]);
  };

  const removeAd = (adId: string) => {
    setAds(prevAds => prevAds.filter(ad => ad.id !== adId));
  };

  const handleAdTextChange = (adId: string, fieldType: 'headlines' | 'descriptions', fieldId: string, value: string) => {
    setAds(prevAds => 
      prevAds.map(ad => 
        ad.id === adId 
          ? { 
              ...ad, 
              [fieldType]: ad[fieldType].map(field => 
                field.id === fieldId ? { ...field, text: value } : field
              )
            }
          : ad
      )
    );
  };

  const addAdField = (adId: string, fieldType: 'headlines' | 'descriptions') => {
    setAds(prevAds => 
      prevAds.map(ad => {
        if (ad.id === adId) {
          if ((fieldType === 'headlines' && ad.headlines.length < MAX_HEADLINES) || 
              (fieldType === 'descriptions' && ad.descriptions.length < MAX_DESCRIPTIONS)) {
            return {
              ...ad,
              [fieldType]: [...ad[fieldType], { id: generateId(), text: '' }]
            };
          }
        }
        return ad;
      })
    );
  };

  const removeAdField = (adId: string, fieldType: 'headlines' | 'descriptions', fieldId: string) => {
    setAds(prevAds => 
      prevAds.map(ad => 
        ad.id === adId 
          ? { 
              ...ad, 
              [fieldType]: ad[fieldType].filter(field => field.id !== fieldId) 
            }
          : ad
      )
    );
  };

  const handleGenerateScript = () => {
    const finalUrlVar = generateVarName();
    const campaignNameVar = generateVarName();
    const adGroupNameVar = generateVarName();
    const budgetVar = generateVarName();

    let rawScript = '';
    rawScript += `// Generated by GAds Script Generator - ${new Date().toISOString()}\n`;
    rawScript += `// Performance Analytics Tool Integration\n\n`;

    rawScript += `var FINAL_URL_${finalUrlVar} = '${campaignParams.finalUrl.replace(/'/g, "\\'")}';\n`;
    rawScript += `var CAMPAIGN_NAME_${campaignNameVar} = '${campaignParams.campaignName.replace(/'/g, "\\'")}';\n`;
    rawScript += `var AD_GROUP_NAME_${adGroupNameVar} = '${campaignParams.adGroupName.replace(/'/g, "\\'")}';\n`;
    rawScript += `var BUDGET_${budgetVar} = ${parseFloat(campaignParams.budget) || 0};\n\n`;

    rawScript += `function main() {\n`;
    rawScript += `  Logger.log('Starting script: Create Search Campaign');\n\n`;
    rawScript += `  // Create Campaign\n`;
    rawScript += `  var campaignBuilder = AdsApp.campaigns()\n`;
    rawScript += `    .newCampaign()\n`;
    rawScript += `    .withName(CAMPAIGN_NAME_${campaignNameVar})\n`;
    rawScript += `    .withBudget(BUDGET_${budgetVar})\n`;
    rawScript += `    .withAdvertisingChannelType(AdsApp.AdvertisingChannelType.SEARCH);\n`;
    rawScript += `    // .withBiddingStrategyConfiguration(AdsApp.biddingStrategy().maximizeConversions()); // Optional: Add bidding strategy if needed
`;
    rawScript += `  var campaignOperation = campaignBuilder.build();\n`;
    rawScript += `  if (!campaignOperation.isSuccessful()) {\n`;
    rawScript += `    Logger.log('Failed to create campaign: ' + campaignOperation.getErrors().join(', '));\n`;
    rawScript += `    return;\n`;
    rawScript += `  }\n`;
    rawScript += `  var campaign = campaignOperation.getResult();\n`;
    rawScript += `  Logger.log('Campaign created: ' + campaign.getName());\n\n`;

    rawScript += `  // Create Ad Group\n`;
    rawScript += `  var adGroupBuilder = campaign.newAdGroup()\n`;
    rawScript += `    .withName(AD_GROUP_NAME_${adGroupNameVar});\n`;
    rawScript += `    // .withCpc(1.0); // Optional: Set a CPC bid if using Manual CPC
`;
    rawScript += `  var adGroupOperation = adGroupBuilder.build();\n`;
    rawScript += `  if (!adGroupOperation.isSuccessful()) {\n`;
    rawScript += `    Logger.log('Failed to create ad group: ' + adGroupOperation.getErrors().join(', '));\n`;
    rawScript += `    return;\n`;
    rawScript += `  }\n`;
    rawScript += `  var adGroup = adGroupOperation.getResult();\n`;
    rawScript += `  Logger.log('Ad Group created: ' + adGroup.getName());\n\n`;

    // Create Ads (Expanded Text Ads)
    ads.forEach((ad, adIndex) => {
      const adVarSuffix = generateVarName(4);
      rawScript += `  // Creating Ad ${adIndex + 1}\n`;
      let adBuilderChain = `adGroup.newAd().expandedTextAdBuilder()\n`;
      adBuilderChain += `    .withHeadlinePart1('${(ad.headlines[0]?.text || '').replace(/'/g, "\\'")}')\n`;
      adBuilderChain += `    .withHeadlinePart2('${(ad.headlines[1]?.text || '').replace(/'/g, "\\'")}')\n`;
      adBuilderChain += `    .withHeadlinePart3('${(ad.headlines[2]?.text || '').replace(/'/g, "\\'")}')\n`;
      adBuilderChain += `    .withDescription1('${(ad.descriptions[0]?.text || '').replace(/'/g, "\\'")}')\n`;
      adBuilderChain += `    .withDescription2('${(ad.descriptions[1]?.text || '').replace(/'/g, "\\'")}')\n`; // In ETAs, Description2 is the standard second description.
      adBuilderChain += `    .withFinalUrl(FINAL_URL_${finalUrlVar})\n`;
      adBuilderChain += `    .build();\n`;
      rawScript += adBuilderChain;
      rawScript += `  var adOperation_${adVarSuffix} = ${adBuilderChain.substring(0, adBuilderChain.lastIndexOf('.build();') + '.build()'.length)};\n`;
      rawScript += `  if (!adOperation_${adVarSuffix}.isSuccessful()) {\n`;
      rawScript += `    Logger.log('Failed to create Ad ${adIndex + 1}: ' + adOperation_${adVarSuffix}.getErrors().join(', '));\n`;
      rawScript += `  } else {\n`;
      rawScript += `    Logger.log('Ad ${adIndex + 1} created.');\n`;
      rawScript += `  }\n\n`;
    });

    // Create Keywords
    const keywordsArray = keywordsInput.split('\n').map(k => k.trim()).filter(k => k.length > 0);
    if (keywordsArray.length > 0) {
      rawScript += `  // Adding Keywords\n`;
      keywordsArray.forEach((keyword) => {
        const kwVarSuffix = generateVarName(4);
        rawScript += `  var keywordOperation_${kwVarSuffix} = adGroup.newKeywordBuilder()\n`;
        rawScript += `    .withText('${keyword.replace(/'/g, "\\'")}')\n`;
        rawScript += `    .build();\n`;
        rawScript += `  if (!keywordOperation_${kwVarSuffix}.isSuccessful()) {\n`;
        rawScript += `    Logger.log('Failed to create keyword \"${keyword.replace(/'/g, "\\'")}\": ' + keywordOperation_${kwVarSuffix}.getErrors().join(', '));\n`;
        rawScript += `  } else {\n`;
        rawScript += `    Logger.log('Keyword \"${keyword.replace(/'/g, "\\'")}\" created.');\n`;
        rawScript += `  }\n\n`;
      });
    }

    rawScript += `  Logger.log('Script finished.');\n`;
    rawScript += `}\n`; // End of main function

    const minified = minifyScript(rawScript);
    setGeneratedScript(minified);
    setShowResult(true);
    setIsCopied(false); // Reset copied status when new script is generated
  };

  const handleCopyScript = () => {
    if (generatedScript) {
      navigator.clipboard.writeText(generatedScript).then(() => {
        setIsCopied(true);
        setTimeout(() => setIsCopied(false), 2000); 
      }).catch(err => {
        console.error('Failed to copy script: ', err);
      });
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-neutral-50 text-neutral-800">
      <Header />
      <main className="flex-grow container mx-auto px-4 sm:px-6 lg:px-8 pt-36 pb-32">
        <div className="max-w-4xl mx-auto">
          <button
            onClick={() => navigate(-1)}
            className="mb-6 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <ArrowLeft className="mr-2 h-5 w-5" />
            Back to Tools
          </button>
          <div className="bg-white p-6 sm:p-8 rounded-xl shadow-xl">
            <h1 className="text-3xl font-bold text-neutral-800 mb-2 text-center">Search Ads Script Generator</h1>
            <p className="text-neutral-600 text-center mb-8">This tool will help you generate Google Ads scripts for your search campaigns.</p>
            
            {/* --- Campaign Parameters Section --- */}
            <div className="mb-10 p-6 bg-neutral-50 rounded-lg border border-neutral-200">
              <h2 className="text-xl font-semibold text-neutral-700 mb-6 border-b border-neutral-300 pb-3">Campaign Parameters</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Final URL */}
                <div className="form-group">
                  <label htmlFor="finalUrl" className="block text-sm font-medium text-neutral-700 mb-1">Campaign URL:</label>
                  <input 
                    type="text" 
                    id="finalUrl" 
                    name="finalUrl"
                    value={campaignParams.finalUrl}
                    onChange={handleCampaignParamChange}
                    className="w-full px-3 py-2 border border-neutral-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Enter target URL"
                  />
                </div>

                {/* Campaign Name */}
                <div className="form-group">
                  <label htmlFor="campaignName" className="block text-sm font-medium text-neutral-700 mb-1">Campaign Name:</label>
                  <input 
                    type="text" 
                    id="campaignName" 
                    name="campaignName"
                    value={campaignParams.campaignName}
                    onChange={handleCampaignParamChange}
                    className="w-full px-3 py-2 border border-neutral-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Enter campaign name"
                  />
                </div>

                {/* Ad Group Name */}
                <div className="form-group">
                  <label htmlFor="adGroupName" className="block text-sm font-medium text-neutral-700 mb-1">Ad Group Name:</label>
                  <input 
                    type="text" 
                    id="adGroupName" 
                    name="adGroupName"
                    value={campaignParams.adGroupName}
                    onChange={handleCampaignParamChange}
                    className="w-full px-3 py-2 border border-neutral-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Enter ad group name"
                  />
                </div>

                {/* Budget */}
                <div className="form-group">
                  <label htmlFor="budget" className="block text-sm font-medium text-neutral-700 mb-1">Daily Budget (in account currency):</label>
                  <input 
                    type="number" 
                    id="budget" 
                    name="budget"
                    value={campaignParams.budget}
                    onChange={handleCampaignParamChange}
                    className="w-full px-3 py-2 border border-neutral-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Enter budget"
                    min="0" 
                    step="0.01"
                  />
                </div>
              </div>
            </div>

            {/* --- Ads Section --- */}
            <div className="mb-10 p-6 bg-neutral-50 rounded-lg border border-neutral-200">
              <div className="flex justify-between items-center mb-6 border-b border-neutral-300 pb-3">
                <h2 className="text-xl font-semibold text-neutral-700">Ads</h2>
                <button 
                  onClick={addAd}
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
                >
                  <PlusCircle size={18} className="mr-2" />
                  Add Ad
                </button>
              </div>

              {ads.map((ad, adIndex) => (
                <div key={ad.id} className={`p-4 rounded-md border border-neutral-300 ${adIndex < ads.length - 1 ? 'mb-6' : ''}`}>
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-medium text-neutral-700">Ad {adIndex + 1}</h3>
                    {ads.length > 1 && (
                      <button 
                        onClick={() => removeAd(ad.id)}
                        className="inline-flex items-center text-red-500 hover:text-red-700 transition-colors"
                      >
                        <Trash2 size={18} className="mr-1" /> Remove Ad
                      </button>
                    )}
                  </div>

                  {/* Headlines */}
                  <div className="mb-4">
                    <div className="flex justify-between items-center mb-2">
                      <label className="block text-sm font-medium text-neutral-700">Headlines (up to {MAX_HEADLINES}, {HEADLINE_CHAR_LIMIT} chars each)</label>
                      {ad.headlines.length < MAX_HEADLINES && (
                        <button 
                          onClick={() => addAdField(ad.id, 'headlines')}
                          className="inline-flex items-center text-xs font-medium text-primary-600 hover:text-primary-700 transition-colors"
                        >
                          <PlusCircle size={16} className="mr-1" /> Add Headline
                        </button>
                      )}
                    </div>
                    {ad.headlines.map((headline, headlineIndex) => (
                      <div key={headline.id} className="flex items-center mb-2">
                        <input 
                          type="text" 
                          value={headline.text}
                          onChange={(e) => handleAdTextChange(ad.id, 'headlines', headline.id, e.target.value)}
                          maxLength={HEADLINE_CHAR_LIMIT}
                          className="flex-grow px-3 py-2 border border-neutral-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 text-sm"
                          placeholder={`Headline ${headlineIndex + 1}`}
                        />
                        <span className="ml-2 text-xs text-neutral-500 w-12 text-right">
                          {headline.text.length}/{HEADLINE_CHAR_LIMIT}
                        </span>
                        {ad.headlines.length > 1 && (
                          <button 
                            onClick={() => removeAdField(ad.id, 'headlines', headline.id)}
                            className="ml-2 text-red-500 hover:text-red-700 transition-colors p-1 rounded-full hover:bg-red-100"
                          >
                            <Trash2 size={16} />
                          </button>
                        )}
                      </div>
                    ))}
                  </div>

                  {/* Descriptions */}
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <label className="block text-sm font-medium text-neutral-700">Descriptions (up to {MAX_DESCRIPTIONS}, {DESCRIPTION_CHAR_LIMIT} chars each)</label>
                      {ad.descriptions.length < MAX_DESCRIPTIONS && (
                        <button 
                          onClick={() => addAdField(ad.id, 'descriptions')}
                          className="inline-flex items-center text-xs font-medium text-primary-600 hover:text-primary-700 transition-colors"
                        >
                          <PlusCircle size={16} className="mr-1" /> Add Description
                        </button>
                      )}
                    </div>
                    {ad.descriptions.map((description, descriptionIndex) => (
                      <div key={description.id} className="flex items-center mb-2">
                        <input 
                          type="text" 
                          value={description.text}
                          onChange={(e) => handleAdTextChange(ad.id, 'descriptions', description.id, e.target.value)}
                          maxLength={DESCRIPTION_CHAR_LIMIT}
                          className="flex-grow px-3 py-2 border border-neutral-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 text-sm"
                          placeholder={`Description ${descriptionIndex + 1}`}
                        />
                        <span className="ml-2 text-xs text-neutral-500 w-12 text-right">
                          {description.text.length}/{DESCRIPTION_CHAR_LIMIT}
                        </span>
                        {ad.descriptions.length > 1 && (
                          <button 
                            onClick={() => removeAdField(ad.id, 'descriptions', description.id)}
                            className="ml-2 text-red-500 hover:text-red-700 transition-colors p-1 rounded-full hover:bg-red-100"
                          >
                            <Trash2 size={16} />
                          </button>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>

            {/* --- Keywords Section --- */}
            <div className="mb-10 p-6 bg-neutral-50 rounded-lg border border-neutral-200">
              <h2 className="text-xl font-semibold text-neutral-700 mb-6 border-b border-neutral-300 pb-3">Keywords</h2>
              <div className="form-group">
                <label htmlFor="keywords" className="block text-sm font-medium text-neutral-700 mb-1">
                  Enter keywords (one per line):
                </label>
                <textarea 
                  id="keywords" 
                  name="keywords"
                  rows={6}
                  value={keywordsInput}
                  onChange={handleKeywordsChange}
                  className="w-full px-3 py-2 border border-neutral-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 text-sm"
                  placeholder="e.g.\n[exact match keyword]\nbroad match keyword\n+modified +broad +match"
                />
                <p className="mt-1 text-xs text-neutral-500">
                  Use brackets for exact match (e.g., [my keyword]) and plus signs for broad match modifier (e.g., +my +keyword).
                </p>
              </div>
            </div>

            {/* --- Generate Script Button --- */}
            <div className="mt-8 mb-6 text-center">
              <button 
                onClick={handleGenerateScript}
                className="w-full md:w-auto inline-flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
              >
                Generate Script
              </button>
            </div>

            {/* --- Result Display Section --- */}
            {showResult && (
              <div className="mt-10 p-6 bg-neutral-50 rounded-lg border border-neutral-200">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold text-neutral-700">Generated Script</h2>
                  <button
                    onClick={handleCopyScript}
                    disabled={!generatedScript}
                    className={`inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md shadow-sm transition-colors 
                                ${isCopied 
                                  ? 'bg-green-100 border-green-400 text-green-700 hover:bg-green-200'
                                  : 'bg-white border-primary-600 text-primary-600 hover:bg-primary-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'
                                } ${!generatedScript ? 'opacity-50 cursor-not-allowed' : ''}`}
                  >
                    {isCopied ? <CheckCircle size={18} className="mr-2" /> : <ClipboardCopy size={18} className="mr-2" />}
                    {isCopied ? 'Copied!' : 'Copy Script'}
                  </button>
                  <button
                    onClick={() => {
                      setShowResult(false);
                      setIsCopied(false);
                    }}
                    className="mt-4 sm:mt-0 sm:ml-4 w-full sm:w-auto inline-flex items-center justify-center px-6 py-3 border border-neutral-300 text-sm font-medium rounded-md text-neutral-700 bg-white hover:bg-neutral-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
                  >
                    Create Another Script
                  </button>
                </div>
                <textarea 
                  readOnly
                  value={generatedScript}
                  className="w-full h-96 p-3 border border-neutral-300 rounded-md shadow-sm bg-neutral-100 text-sm font-mono focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Your generated Google Ads script will appear here..."
                />
              </div>
            )}
            <div className="mt-8 text-center">
              <button 
                onClick={() => navigate(-1)}
                className="w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <ArrowLeft className="mr-2 h-5 w-5" />
                Back to Tools
              </button>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default SearchAdsScriptGeneratorPage;
