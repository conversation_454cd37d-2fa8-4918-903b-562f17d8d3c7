import React from 'react';
import Header from '../components/Header';
import Services from '../components/Services';
import Footer from '../components/Footer';
import { useAuth } from '../context/AuthContext';
import { Navigate } from 'react-router-dom';

const DashboardPage: React.FC = () => {
  const { currentUser, userStatus, loading } = useAuth();

  if (loading) {
    return <div className="flex justify-center items-center min-h-screen">Loading...</div>;
  }

  if (!currentUser) {
    return <Navigate to="/login" replace />;
  }

  if (userStatus === 'banned') {
    alert('Your account access has been restricted. Please contact support.');
    return <Navigate to="/login" replace />;
  }

  if (userStatus === 'new') {
    return <Navigate to="/" replace />;
  }

  return (
    <div className="flex flex-col min-h-screen bg-gradient-to-br from-background-start to-background-end">
      <Header />
      <main className="flex-grow pt-24 pb-12 px-4 sm:px-6 lg:px-8">
        {/* You can add a dashboard-specific welcome message here */}
        <div className="container mx-auto">
          <h1 className="text-3xl font-bold text-neutral-900 mb-8 text-center">Welcome to your Client Area, {currentUser.email}!</h1>
          {/* For now, we'll just show the Services component on the dashboard */}
          <Services />
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default DashboardPage;
