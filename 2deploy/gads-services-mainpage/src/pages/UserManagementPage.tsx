import React, { useState, useMemo, useEffect } from 'react';
import Header from '../components/Header';
import Footer from '../components/Footer';
import Button from '../components/ui/Button';
import { useAuth } from '../context/AuthContext';
import { Navigate, useNavigate } from 'react-router-dom';
import { Mail, Edit3, Search, UserPlus, RefreshCw, MoreVertical, ChevronLeft, ChevronRight, X, ArrowLeft } from 'lucide-react'; 
import { getAuth, createUserWithEmailAndPassword } from 'firebase/auth'; 
import { app } from '../firebase'; 

// Define UserStatus type
export type UserStatus = 'new' | 'alive' | 'admin' | 'banned';

// Update AppUser interface
interface AppUser {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL?: string | null; 
  creationTime?: string;    
  lastSignInTime?: string;  
  status: UserStatus;
  providerId?: string; 
}

// Helper to format dates (simplified)
const formatDate = (isoString?: string) => {
  if (!isoString) return 'N/A';
  try {
    return new Date(isoString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  } catch (e) {
    return 'Invalid Date';
  }
};

const UserManagementPage: React.FC = () => {
  const { currentUser, isAdmin, loading } = useAuth();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [isAddUserModalOpen, setIsAddUserModalOpen] = useState(false);
  const [newUserEmail, setNewUserEmail] = useState('');
  const [newUserPassword, setNewUserPassword] = useState('');
  const [newUserRole, setNewUserRole] = useState<UserStatus>('new'); 
  const [addUserError, setAddUserError] = useState<string | null>(null);

  useEffect(() => {
    // Redirect if user is not logged in or is not an admin
    if (!currentUser || !isAdmin) {
      console.warn('Access Denied: User is not an admin or not logged in. Redirecting...');
      alert('Access Denied: You do not have permission to view this page.'); // Optional: user-friendly alert
      navigate('/'); // Redirect to home page or login page
    }
  }, [currentUser, isAdmin, navigate]);

  // Placeholder user data - IN A REAL APP, FETCH THIS SECURELY FROM BACKEND
  const [users, setUsers] = React.useState<AppUser[]>([
    {
      uid: 'hi-gio-pw-uid',
      email: '<EMAIL>',
      displayName: 'Admin User Gio',
      creationTime: new Date(2023, 10, 5).toISOString(), 
      lastSignInTime: new Date().toISOString(), 
      status: 'admin',
      providerId: 'password',
    },
  ]);

  // Filter users based on search term
  const filteredUsers = useMemo(() => {
    if (!searchTerm) {
      return users;
    }
    return users.filter(user => 
      (user.email && user.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (user.displayName && user.displayName.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (user.uid.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  }, [users, searchTerm]);

  const handleStatusChange = (uid: string, newStatus: UserStatus) => {
    // In a real app: call a Firebase Function to update Firestore and/or Custom Claims.
    // Then, re-fetch users or update local state optimistically.
    const updatedUsers = users.map(user => user.uid === uid ? { ...user, status: newStatus } : user);
    setUsers(updatedUsers);
  };

  const handleAddNewUser = async (e: React.FormEvent) => {
    e.preventDefault();
    setAddUserError(null);
    if (!newUserEmail || !newUserPassword) {
      setAddUserError('Email and password are required.');
      return;
    }
    try {
      const auth = getAuth(app);
      const userCredential = await createUserWithEmailAndPassword(auth, newUserEmail, newUserPassword);
      const firebaseUser = userCredential.user;

      const newAppUser: AppUser = {
        uid: firebaseUser.uid,
        email: firebaseUser.email,
        displayName: firebaseUser.displayName || firebaseUser.email, 
        creationTime: firebaseUser.metadata.creationTime || new Date().toISOString(),
        lastSignInTime: firebaseUser.metadata.lastSignInTime || new Date().toISOString(),
        status: newUserRole, 
        providerId: 'password', 
      };

      setUsers(prevUsers => [newAppUser, ...prevUsers]);
      alert(`User ${firebaseUser.email} created with role '${newUserRole}'.\n\nIMPORTANT:\n1. AUTO-LOGIN: The Firebase client SDK automatically signed in as this new user. You may need to sign out and sign back in as admin. To prevent this, user creation should be handled by a backend Firebase Function.\n2. SECURE ROLE ASSIGNMENT: Setting roles (especially 'admin') securely requires Firebase Custom Claims, managed by a backend Firebase Function. The role selected here is a local placeholder.`);
      setIsAddUserModalOpen(false);
      setNewUserEmail('');
      setNewUserPassword('');
      setNewUserRole('new'); 
    } catch (error: any) {
      console.error("Error creating new user:", error);
      setAddUserError(error.message || 'Failed to create user.');
    }
  };

  const handleRefreshUsers = async () => {
    // TODO: Implement actual Firebase Function call here to fetch all users
    // This requires Admin SDK and a backend function.
    alert('Placeholder: Refreshing users...\nIn a real app, this would fetch the latest user list from Firebase (requires backend/Firebase Functions).');
    // Example: const firebaseUsers = await callYourFirebaseFunction('listAllUsers');
    // setUsers(firebaseUsers.map(fbUser => ({ /* map to AppUser type */ })));
  };

  if (loading) {
    return <div className="flex justify-center items-center min-h-screen">Loading...</div>;
  }

  if (!currentUser) {
    return <Navigate to="/login" replace />;
  }

  if (!isAdmin) {
    return <Navigate to="/dashboard" replace />;
  }

  const getStatusColor = (status: UserStatus) => {
    switch (status) {
      case 'admin': return 'text-purple-600 bg-purple-100';
      case 'alive': return 'text-green-600 bg-green-100';
      case 'new': return 'text-blue-600 bg-blue-100';
      case 'banned': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-neutral-100">
      <Header />
      <main className="flex-grow pt-36 pb-32 px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto">
          <button
            onClick={() => navigate(-1)}
            className="mb-6 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <ArrowLeft className="mr-2 h-5 w-5" />
            Back to Tools
          </button>
          {/* Top Controls - Mimicking Firebase UI */}
          <div className="mb-4 pt-4 flex flex-col sm:flex-row justify-between items-center gap-4 p-4 bg-white shadow-sm rounded-t-lg border-b border-neutral-200">
            <div className="relative flex-grow w-full sm:w-auto">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-neutral-400" />
              </div>
              <input 
                type="text" 
                placeholder="Search by email address, phone number, or user UID" 
                className="block w-full pl-10 pr-3 py-2 border border-neutral-300 rounded-md leading-5 bg-white placeholder-neutral-500 focus:outline-none focus:placeholder-neutral-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex items-center space-x-2 flex-shrink-0">
              <Button 
                variant="primary" 
                onClick={() => setIsAddUserModalOpen(true)} 
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                <UserPlus size={18} className="mr-2" /> Add user
              </Button>
              <Button variant="ghost" size="icon" className="text-neutral-600 hover:bg-neutral-200" title="Refresh user list" onClick={handleRefreshUsers}>
                <RefreshCw size={20} />
              </Button>
              <Button variant="ghost" size="icon" className="text-neutral-600 hover:bg-neutral-200" title="More options">
                <MoreVertical size={20} />
              </Button>
            </div>
          </div>
          
          <div className="bg-white shadow-md rounded-b-lg overflow-x-auto">
            <table className="min-w-full divide-y divide-neutral-200">
              <thead className="bg-neutral-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Identifier</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Provider(s)</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider whitespace-nowrap">Created <span className="text-lg">↓</span></th> 
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider whitespace-nowrap">Signed In</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Status</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">User UID</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-neutral-200">
                {filteredUsers.length === 0 && (
                  <tr>
                    <td colSpan={7} className="px-6 py-12 text-center text-neutral-500">
                      {searchTerm ? 'No users match your search.' : 'No users found. (Placeholder data is being used).'}
                    </td>
                  </tr>
                )}
                {filteredUsers.map((user) => (
                  <tr key={user.uid} className="hover:bg-neutral-50/50 transition-colors duration-150">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-neutral-900">{user.displayName || 'N/A'}</div>
                      <div className="text-xs text-neutral-500">{user.email}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-500">
                      {user.providerId === 'password' && 
                        <span title="Email/Password Provider">
                          <Mail size={18} className="text-neutral-400" />
                        </span>
                      }
                      {/* Add other provider icons here if needed */}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-600">{formatDate(user.creationTime)}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-600">{formatDate(user.lastSignInTime)}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <select 
                        value={user.status}
                        onChange={(e) => handleStatusChange(user.uid, e.target.value as UserStatus)}
                        className={`text-xs font-semibold py-1 px-2 border rounded-full focus:outline-none focus:ring-2 focus:ring-offset-1 ${getStatusColor(user.status)} border-transparent appearance-none`}
                        aria-label={`Status for ${user.email}`}
                      >
                        <option value="new">New</option>
                        <option value="alive">Alive</option>
                        <option value="admin">Admin</option>
                        <option value="banned">Banned</option>
                      </select>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-xs text-neutral-500 font-mono" title={user.uid}>{user.uid.substring(0, 15)}...</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button 
                        onClick={() => alert(`Edit user: ${user.displayName || user.email}.\nThis needs implementation!`)} 
                        className="text-primary-600 hover:text-primary-700 transition-colors p-1 rounded hover:bg-primary-50"
                        title="Edit User"
                      >
                        <Edit3 size={16}/>
                      </button>
                      {/* Potential Delete Button - HIGHLY SENSITIVE ACTION */}
                      {/* <button 
                        onClick={() => alert(`Delete user: ${user.displayName || user.email}.\nThis needs secure backend implementation!`)} 
                        className="text-error-600 hover:text-error-700 transition-colors ml-2 p-1 rounded hover:bg-error-50"
                        title="Delete User"
                      >
                        <Trash2 size={16}/>
                      </button> */}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            {/* Table Footer with Pagination - Mimicking Firebase UI */}
            {filteredUsers.length > 0 && (
              <div className="px-6 py-3 flex items-center justify-between border-t border-neutral-200 bg-neutral-50">
                <div className="flex items-center text-sm text-neutral-600">
                  <span>Rows per page:</span>
                  <select className="ml-2 border-neutral-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 sm:text-sm p-1 bg-white">
                    <option>10</option>
                    <option>25</option>
                    <option selected>50</option>
                    <option>100</option>
                  </select>
                </div>
                <div className="flex items-center text-sm text-neutral-600">
                  <span className="mr-4">1 – {Math.min(filteredUsers.length, 50)} of {filteredUsers.length}</span>
                  <Button variant="ghost" size="icon" className="text-neutral-500 hover:bg-neutral-200 p-1" disabled={true} title="Previous page">
                    <ChevronLeft size={20} />
                  </Button>
                  <Button variant="ghost" size="icon" className="text-neutral-500 hover:bg-neutral-200 p-1 ml-1" disabled={filteredUsers.length <= 50} title="Next page">
                    <ChevronRight size={20} />
                  </Button>
                </div>
              </div>
            )}
          </div>
          <div className="mt-8 flex justify-center">
            <Button 
              variant="outline"
              onClick={() => navigate(-1)} 
              className="border-neutral-300 text-neutral-700 hover:bg-neutral-50"
            >
              Go Back
            </Button>
          </div>

        </div>
      </main>
      <Footer />

      {/* Add User Modal */}
      {isAddUserModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
          <div className="bg-white p-6 rounded-lg shadow-xl w-full max-w-md m-4">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-neutral-800">Add New User</h2>
              <Button variant="ghost" size="icon" onClick={() => setIsAddUserModalOpen(false)} className="p-1 text-neutral-500 hover:text-neutral-700">
                <X size={24} />
              </Button>
            </div>
            <form onSubmit={handleAddNewUser}>
              <div className="mb-4">
                <label htmlFor="newUserEmail" className="block text-sm font-medium text-neutral-700 mb-1">Email Address</label>
                <input 
                  type="email" 
                  id="newUserEmail" 
                  value={newUserEmail} 
                  onChange={(e) => setNewUserEmail(e.target.value)} 
                  required 
                  className="w-full px-3 py-2 border border-neutral-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
              <div className="mb-4">
                <label htmlFor="newUserPassword" className="block text-sm font-medium text-neutral-700 mb-1">Password</label>
                <input 
                  type="password" 
                  id="newUserPassword" 
                  value={newUserPassword} 
                  onChange={(e) => setNewUserPassword(e.target.value)} 
                  required 
                  minLength={6} 
                  className="w-full px-3 py-2 border border-neutral-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                  placeholder="At least 6 characters"
                />
              </div>
              <div className="mb-6">
                <label htmlFor="newUserRole" className="block text-sm font-medium text-neutral-700 mb-1">Assign Role/Status</label>
                <select
                  id="newUserRole"
                  value={newUserRole}
                  onChange={(e) => setNewUserRole(e.target.value as UserStatus)}
                  className="w-full px-3 py-2 border border-neutral-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 bg-white"
                >
                  <option value="new">New</option>
                  <option value="alive">Alive</option>
                  <option value="admin">Admin (Requires Backend Setup)</option>
                  <option value="banned">Banned</option>
                </select>
                {newUserRole === 'admin' && (
                  <p className="mt-1 text-xs text-orange-600">Note: Setting 'admin' role here is a placeholder. True admin privileges require backend setup (Firebase Custom Claims).</p>
                )}
              </div>
              {addUserError && (
                <p className="mb-4 text-sm text-red-600 bg-red-100 p-2 rounded-md">{addUserError}</p>
              )}
              <div className="flex justify-end space-x-3">
                <Button type="button" variant="outline" onClick={() => setIsAddUserModalOpen(false)} className="border-neutral-300 text-neutral-700 hover:bg-neutral-100">
                  Cancel
                </Button>
                <Button type="submit" variant="primary" className="bg-blue-600 hover:bg-blue-700">
                  Add User
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserManagementPage;
