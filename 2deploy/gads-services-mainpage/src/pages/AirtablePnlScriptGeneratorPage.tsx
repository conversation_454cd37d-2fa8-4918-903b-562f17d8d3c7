import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import Header from '../components/Header';
import Footer from '../components/Footer';

const LOCAL_STORAGE_PREFIX = 'airtablePnlScriptGenerator_';
const MAX_HISTORY_LENGTH = 3;

interface FormData {
  accountName: string;
  buyerId: string;
  niche: string;
  airtableUrl: string;
  apiKey: string;
  banDays: string; // Keep as string to match input type, convert to number when used
  freezeDays: string; // Keep as string to match input type
}

const AirtablePnlScriptGeneratorPage: React.FC = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState<FormData>({
    accountName: '',
    buyerId: '',
    niche: '',
    airtableUrl: '',
    apiKey: '',
    banDays: '30',
    freezeDays: '15',
  });

  const [formHistory, setFormHistory] = useState<Record<keyof FormData, string[]>>({
    accountName: [],
    buyerId: [],
    niche: [],
    airtableUrl: [],
    apiKey: [],
    banDays: [],
    freezeDays: [],
  });

  const [generatedScript, setGeneratedScript] = useState('');
  const [showResult, setShowResult] = useState(false);
  const [copyButtonText, setCopyButtonText] = useState('Copy Script');

  useEffect(() => {
    const loadAllHistory = () => {
      const newFormHistory: Record<keyof FormData, string[]> = { ...formHistory };
      (Object.keys(formData) as Array<keyof FormData>).forEach(key => {
        const storedHistory = localStorage.getItem(`${LOCAL_STORAGE_PREFIX}${key}`);
        if (storedHistory) {
          newFormHistory[key] = JSON.parse(storedHistory);
        }
      });
      setFormHistory(newFormHistory);
    };
    loadAllHistory();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Load once on mount

  const updateFieldHistory = (field: keyof FormData, value: string) => {
    if (!value.trim()) return;
    const currentHistory = formHistory[field];
    const newHistory = [value, ...currentHistory.filter(item => item !== value)].slice(0, MAX_HISTORY_LENGTH);
    
    const updatedFormHistory = { ...formHistory, [field]: newHistory };
    setFormHistory(updatedFormHistory);
    localStorage.setItem(`${LOCAL_STORAGE_PREFIX}${field}`, JSON.stringify(newHistory));
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const generateRandomString = (length = 0): string => {
    if (!length) length = Math.floor(Math.random() * 6) + 5; // 5-10 characters
    const characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
  };

  const scriptTemplate = `function main() {
  var {{airName}} = '{{ACC_NAME_OR_MACHINE_ID}}'; 
  var {{airGroup}} = '{{BUYER_ID_FROM_AIRTABLE}}'; 
  var {{airTheme}} = '{{NICHE_FROM_AIRTABLE}}';  
  
  var {{airUrl}} = '{{URL_OF_AIRTABLE}}'; 
  var {{airKey}} = '{{API_OF_AIRTABLE}}'; 
  
  var {{statusBannedTerm}} = {{DAYS_TO_BAN}};
  var {{statusFrozenTerm}} = {{DAYS_TO_FREEZE}}; 
  
  var {{CTTT}} = AdsApp.currentAccount();
  
  var {{body}} = {
       'records':[    
        {
         'id': '',
         'fields':{
          'Status': 'Works',
          'Clicks (T)': {{CTTT}}.getStatsFor('TODAY').getClicks(),
          'Clicks (A)': {{CTTT}}.getStatsFor('ALL_TIME').getClicks(),
          'Impr. (T)': {{CTTT}}.getStatsFor('TODAY').getImpressions(),
          'Impr. (A)': {{CTTT}}.getStatsFor('ALL_TIME').getImpressions(),
          'CTR (T)': {{CTTT}}.getStatsFor('TODAY').getCtr(),
          'CTR (A)': {{CTTT}}.getStatsFor('ALL_TIME').getCtr(),
          'CPC (T)': {{CTTT}}.getStatsFor('TODAY').getAverageCpc(),
          'CPC (A)': {{CTTT}}.getStatsFor('ALL_TIME').getAverageCpc(),
          'Cost (T)': {{CTTT}}.getStatsFor('TODAY').getCost(),
          'Cost (A)': {{CTTT}}.getStatsFor('ALL_TIME').getCost(),
          'CPM (T)': {{CTTT}}.getStatsFor('TODAY').getAverageCpm(),
          'CPM (A)': {{CTTT}}.getStatsFor('ALL_TIME').getAverageCpm(),
          'Conv (A)': {{CTTT}}.getStatsFor('ALL_TIME').getConversions(),
          'Conv (T)': {{CTTT}}.getStatsFor('TODAY').getConversions()
         }
        }
       ]
    };
  
  var {{airSearchPattern}} = "AND({Account}='"+{{airName}}+"',{Group} ='"+{{airGroup}}+"')";
  
  var {{response}} = JSON.parse({{reqGet}}('GET', {{airUrl}}+"?filterByFormula="+encodeURI({{airSearchPattern}}), {{airKey}}).getContentText()); 

  if({{response}}['records'].length > 0){
    {{body}}['records'][0]['id'] = {{response}}['records'][0]['id']; 
    
    var {{fields}} = {{response}}['records'][0]['fields'];
    var {{dataDiff}} = {{dateComparison}}(({{fields}}['Last update'] || new Date().toISOString()), new Date().toISOString()); // Handle missing 'Last update'
    var {{statusCurrent}} = {{fields}}['Status'];

    if({{body}}['records'][0]['fields']['Impr. (A)'] != ({{fields}}['Impr. (A)'] || 0) ){
      {{reqCustom}}('PATCH', {{airUrl}}, {{body}}, {{airKey}})
    } else 
      if({{dataDiff}} > {{statusBannedTerm}} && {{statusCurrent}} != 'Banned'){
        {{body}}['records'][0]['fields'] = 
        { 
          'Status': 'Banned',
          'Ad status': AdsApp.ads().get().hasNext() ? AdsApp.ads().get().next().getPolicyApprovalStatus() : 'N/A',
          'Disapproved reason': AdsApp.ads().get().hasNext() ? AdsApp.ads().get().next().getDisapprovalReasons().toString() : 'N/A'
        };
        
        {{reqCustom}}('PATCH', {{airUrl}}, {{body}}, {{airKey}});
      } else 
        if({{dataDiff}} > {{statusFrozenTerm}} && {{statusCurrent}} != 'Frozen'){
          {{body}}['records'][0]['fields'] = 
          { 
            'Status': 'Frozen',
            'Ad status': AdsApp.ads().get().hasNext() ? AdsApp.ads().get().next().getPolicyApprovalStatus() : 'N/A',
            'Disapproved reason': AdsApp.ads().get().hasNext() ? AdsApp.ads().get().next().getDisapprovalReasons().toString() : 'N/A'
          };
          {{reqCustom}}('PATCH', {{airUrl}}, {{body}}, {{airKey}});
        }
  } else {
    var {{createBody}} = {
     'records':[ 
      {
       'fields':{
        'Account': {{airName}},
        'Group': {{airGroup}},
        'Theme': {{airTheme}},
        'Status': 'Works'
       }
      }
     ]
    };
  
  {{response}} = JSON.parse({{reqCustom}}('POST', {{airUrl}}, {{createBody}}, {{airKey}}).getContentText());
  {{body}}['records'][0]['id'] = {{response}}['records'][0]['id']; 
  
  {{reqCustom}}('PATCH', {{airUrl}}, {{body}}, {{airKey}});
  }
}

function {{reqGet}}(method, url, key){
    var {{header}} = {
      authorization: 'Bearer ' + key
    };
  
    const {{options}} = {
        method: method,
        headers: {{header}}
    };

    return UrlFetchApp.fetch(url, {{options}});
}

function {{reqCustom}}(method, url, body, key){
  var {{header}} = {
    authorization: 'Bearer ' + key
  }
  
  var {{options}} = {
    method : method,
    headers: {{header}},
    contentType: 'application/json',
    payload: JSON.stringify(body)
  };
  
  return UrlFetchApp.fetch(url, {{options}});
}

function {{dateComparison}}(date1, date2){
  var d1 = new Date(date1);
  var d2 = new Date(date2);
  var timeDiff = Math.abs(d2.getTime() - d1.getTime());
  var {{diffDays}} = Math.ceil(timeDiff / (1000 * 3600 * 24)); 
  
  return {{diffDays}};
}`;

  const generateUniqueAirtableScript = (currentFormData: FormData): string => {
    let uniqueScript = scriptTemplate;
    const varMap: Record<string, string> = {};

    // Generate random names for placeholders
    const placeholders = ['airName', 'airGroup', 'airTheme', 'airUrl', 'airKey', 'CTTT', 'body', 
                        'dateComparison', 'response', 'diffDays', 'statusFrozenTerm', 'statusBannedTerm', 
                        'reqGet', 'reqCustom', 'airSearchPattern', 'fields', 'dataDiff', 'statusCurrent', 
                        'createBody', 'header', 'options'];
                        
    placeholders.forEach(ph => {
      varMap[ph] = generateRandomString(ph.length > 8 ? Math.floor(Math.random()*4)+8 : Math.floor(Math.random()*3)+5 ); // shorter for some
      uniqueScript = uniqueScript.replace(new RegExp('{{' + ph + '}}', 'g'), varMap[ph]);
    });

    // Replace user-provided values
    uniqueScript = uniqueScript.replace('{{ACC_NAME_OR_MACHINE_ID}}', currentFormData.accountName.replace(/'/g, "\\'"));
    uniqueScript = uniqueScript.replace('{{BUYER_ID_FROM_AIRTABLE}}', currentFormData.buyerId.replace(/'/g, "\\'"));
    uniqueScript = uniqueScript.replace('{{NICHE_FROM_AIRTABLE}}', currentFormData.niche.replace(/'/g, "\\'"));
    uniqueScript = uniqueScript.replace('{{URL_OF_AIRTABLE}}', currentFormData.airtableUrl.replace(/'/g, "\\'"));
    uniqueScript = uniqueScript.replace('{{API_OF_AIRTABLE}}', currentFormData.apiKey.replace(/'/g, "\\'"));
    uniqueScript = uniqueScript.replace('{{DAYS_TO_BAN}}', parseInt(currentFormData.banDays, 10).toString());
    uniqueScript = uniqueScript.replace('{{DAYS_TO_FREEZE}}', parseInt(currentFormData.freezeDays, 10).toString());

    return uniqueScript;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const script = generateUniqueAirtableScript(formData);
    setGeneratedScript(script);
    setShowResult(true);
    setCopyButtonText('Copy Script');

    (Object.keys(formData) as Array<keyof FormData>).forEach(key => {
      updateFieldHistory(key, formData[key]);
    });
  };

  const handleCopyScript = () => {
    navigator.clipboard.writeText(generatedScript)
      .then(() => setCopyButtonText('Copied!'))
      .catch(err => console.error('Failed to copy script: ', err));
  };

  const handleBackToForm = () => {
    setShowResult(false);
    setGeneratedScript('');
    setCopyButtonText('Copy Script');
  };

  const inputFields: Array<{ id: keyof FormData; label: string; placeholder: string; type?: string; info: string }> = [
    { id: 'accountName', label: 'Account Name / Machine ID:', placeholder: 'Enter account identifier', info: 'Unique name to identify the account in Airtable.' },
    { id: 'buyerId', label: 'Media Buyer ID from Airtable:', placeholder: 'e.g., buyer123', info: 'Identifier of the buyer group in Airtable.' },
    { id: 'niche', label: 'Niche from Airtable:', placeholder: 'e.g., CRPT', info: 'Account theme for categorization.' },
    { id: 'airtableUrl', label: 'Airtable URL:', placeholder: 'https://api.airtable.com/v0/appXXXXXXXXXXXXXX/tableName', info: 'Full API URL to your Airtable table.' },
    { id: 'apiKey', label: 'Airtable API Key:', placeholder: 'keyXXXXXXXXXXXXXX', info: 'Your personal API key for Airtable access.' },
    { id: 'banDays', label: 'Days to Ban:', placeholder: 'e.g., 30', type: 'number', info: 'Number of inactive days before status changes to "Banned".' },
    { id: 'freezeDays', label: 'Days to Freeze:', placeholder: 'e.g., 15', type: 'number', info: 'Number of inactive days before status changes to "Frozen".' },
  ];

  return (
    <div className="min-h-screen bg-neutral-100 flex flex-col">
      <Header />
      <main className="flex-grow container mx-auto px-4 sm:px-6 lg:px-8 pt-36 pb-32">
        <div className="max-w-3xl mx-auto">
          {!showResult ? (
            <>
              <button
                onClick={() => navigate(-1)}
                className="mb-6 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <ArrowLeft className="mr-2 h-5 w-5" />
                Back to Tools
              </button>
              <div className="bg-white p-6 sm:p-8 rounded-xl shadow-lg mb-10">
                <div className="text-center mb-6">
                  <h1 className="text-3xl font-bold text-neutral-800">Airtable P&amp;L Script Generator</h1>
                  <p className="mt-2 text-neutral-600">Generate a Google Ads script to sync performance data with Airtable.</p>
                </div>

                <div className="bg-blue-50 border-l-4 border-blue-500 text-blue-700 p-4 mb-6 rounded-md" role="alert">
                  <p className="font-bold">What this script does:</p>
                  <p>This script connects to your Airtable table and automatically updates Google Ads account statistics. It also changes the account status to "Frozen" or "Banned" based on inactivity days.</p>
                </div>

                <form onSubmit={handleSubmit}>
                  {inputFields.map(field => (
                    <div className="mb-4" key={field.id}>
                      <label htmlFor={field.id} className="block text-sm font-medium text-neutral-700 mb-1">{field.label}</label>
                      <input
                        type={field.type || 'text'}
                        id={field.id}
                        name={field.id}
                        value={formData[field.id]}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-neutral-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                        placeholder={field.placeholder}
                        required
                        list={`${LOCAL_STORAGE_PREFIX}${field.id}-history`}
                      />
                      <datalist id={`${LOCAL_STORAGE_PREFIX}${field.id}-history`}>
                        {formHistory[field.id].map((item, index) => (
                          <option key={index} value={item} />
                        ))}
                      </datalist>
                      <p className="mt-1 text-xs text-neutral-500">{field.info}</p>
                    </div>
                  ))}
                  <div className="mt-8">
                    <button
                      type="submit"
                      className="w-full bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg shadow-md hover:shadow-lg transition-all duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-opacity-75 transform hover:scale-105"
                    >
                      Generate Script
                    </button>
                  </div>
                </form>
              </div>
            </>
          ) : (
            <div className="bg-white p-6 sm:p-8 rounded-xl shadow-xl">
              <h2 className="text-2xl font-semibold text-neutral-800 mb-2">Your Unique Script is Ready</h2>
              <p className="text-neutral-700 mb-6">Copy this script for use in Google Ads:</p>
              
              <div className="flex flex-col sm:flex-row gap-4 mb-6">
                <button
                  onClick={handleCopyScript}
                  className="flex-1 bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg shadow-md hover:shadow-lg transition-all duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-opacity-75 transform hover:scale-105"
                >
                  {copyButtonText}
                </button>
                <button
                  onClick={handleBackToForm}
                  className="flex-1 border border-primary-600 text-primary-600 hover:bg-primary-50 hover:text-primary-700 font-semibold py-3 px-6 rounded-lg shadow-md hover:shadow-lg transition-all duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-opacity-75 transform hover:scale-105"
                >
                  Generate New Script
                </button>
                <button
                  onClick={() => navigate(-1)}
                  className="flex-1 border border-neutral-300 text-neutral-600 hover:bg-neutral-50 font-semibold py-3 px-6 rounded-lg shadow-md hover:shadow-lg transition-all duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-neutral-400 focus:ring-opacity-75 transform hover:scale-105"
                >
                  Back to Previous Page
                </button>
              </div>

              <div className="bg-neutral-800 text-neutral-100 p-4 rounded-md h-[500px] overflow-auto">
                <pre className="whitespace-pre-wrap break-all"><code>{generatedScript}</code></pre>
              </div>
            </div>
          )}

          <div className="bg-white p-6 sm:p-8 rounded-xl shadow-lg mt-10">
            <h2 className="text-2xl font-semibold text-neutral-800 mb-4">How to Use the Script</h2>
            <ol className="list-decimal list-inside space-y-2 text-neutral-700">
              <li>Copy the generated script using the button above.</li>
              <li>Open your Google Ads account and navigate to "Tools &amp; Settings" &gt; "Bulk Actions" &gt; "Scripts".</li>
              <li>Click the "+" button to create a new script.</li>
              <li>Paste the copied code into the editor.</li>
              <li>Authorize the script (it will ask for permissions to manage your campaigns).</li>
              <li>Set up a schedule for the script to run (daily is recommended).</li>
              <li>Save and run the script.</li>
            </ol>
          </div>

          <div className="bg-white p-6 sm:p-8 rounded-xl shadow-lg mt-10">
            <h2 className="text-2xl font-semibold text-neutral-800 mb-4">How to Get Airtable Connection Details</h2>
            <div className="space-y-4 text-neutral-700">
              <div>
                <h3 className="font-semibold">Airtable URL:</h3>
                <p>Log in to your Airtable account → Open the desired base → Click "Help" (top right) → "API documentation" → In the new tab/window, find the "AUTHENTICATION" section. The URL typically looks like: <code>https://api.airtable.com/v0/YOUR_APP_ID/YOUR_TABLE_NAME</code>. Make sure to include your actual table name.</p>
              </div>
              <div>
                <h3 className="font-semibold">Airtable API Key:</h3>
                <p>Visit your Airtable account page: <a href="https://airtable.com/account" target="_blank" rel="noopener noreferrer" className="text-primary-600 hover:underline">https://airtable.com/account</a> → Go to the "API" section → Click "Generate API key" or use an existing one. The key usually starts with <code>key</code>.</p>
              </div>
            </div>
          </div>

        </div>
      </main>
      <Footer />
    </div>
  );
};

export default AirtablePnlScriptGeneratorPage;
