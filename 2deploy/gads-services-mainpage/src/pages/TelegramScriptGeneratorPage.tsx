import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import Header from '../components/Header';
import Footer from '../components/Footer';

const LOCAL_STORAGE_KEYS = {
  TOKEN: 'telegramTokensHistory',
  CHAT_ID: 'telegramChatIdsHistory',
  TAG: 'accountTagsHistory',
};

const MAX_HISTORY_LENGTH = 3;

const TelegramScriptGeneratorPage: React.FC = () => {
  const navigate = useNavigate();
  const [telegramToken, setTelegramToken] = useState('');
  const [telegramChatId, setTelegramChatId] = useState('');
  const [accountTag, setAccountTag] = useState('');

  const [tokenHistory, setTokenHistory] = useState<string[]>([]);
  const [chatIdHistory, setChatIdHistory] = useState<string[]>([]);
  const [tagHistory, setTagHistory] = useState<string[]>([]);

  const [generatedScript, setGeneratedScript] = useState('');
  const [showResult, setShowResult] = useState(false);
  const [copyButtonText, setCopyButtonText] = useState('Copy Script');

  // Load history from localStorage on component mount
  useEffect(() => {
    const loadHistory = (key: string, setter: React.Dispatch<React.SetStateAction<string[]>>) => {
      const storedHistory = localStorage.getItem(key);
      if (storedHistory) {
        setter(JSON.parse(storedHistory));
      }
    };
    loadHistory(LOCAL_STORAGE_KEYS.TOKEN, setTokenHistory);
    loadHistory(LOCAL_STORAGE_KEYS.CHAT_ID, setChatIdHistory);
    loadHistory(LOCAL_STORAGE_KEYS.TAG, setTagHistory);
  }, []);

  // Helper function to update history and localStorage
  const updateHistory = (value: string, key: string, currentHistory: string[], setter: React.Dispatch<React.SetStateAction<string[]>>) => {
    if (!value.trim()) return; // Don't save empty values
    const newHistory = [value, ...currentHistory.filter(item => item !== value)].slice(0, MAX_HISTORY_LENGTH);
    setter(newHistory);
    localStorage.setItem(key, JSON.stringify(newHistory));
  };

  const generateRandomVarName = (prefix = ''): string => {
    const adjectives = ['active', 'brave', 'calm', 'dynamic', 'eager', 'fast', 'good', 'happy'];
    const nouns = ['apple', 'bear', 'cat', 'dog', 'eagle', 'fox', 'goat', 'horse'];
    const randomAdjective = adjectives[Math.floor(Math.random() * adjectives.length)];
    const randomNoun = nouns[Math.floor(Math.random() * nouns.length)];
    const randomNum = Math.floor(Math.random() * 1000);
    return prefix + randomAdjective.charAt(0).toUpperCase() + randomAdjective.slice(1) +
           randomNoun.charAt(0).toUpperCase() + randomNoun.slice(1) + randomNum;
  };

  const generateUniqueScriptText = (token: string, chatId: string, tag: string): string => {
    const sendMsgFuncName = generateRandomVarName('send');
    const accountVarName = generateRandomVarName('account');
    const todayCostVarName = generateRandomVarName('cost');
    const todayClicksVarName = generateRandomVarName('clicks');
    const tagAccountsVarName = generateRandomVarName('tagFromApp'); // Renamed to avoid conflict with input 'tag'
    const configVarName = generateRandomVarName('config');
    const telegramUrlVarName = generateRandomVarName('url');
    const messageVarName = generateRandomVarName('msg');
    const sendMessageUrlVarName = generateRandomVarName('sendUrl');
    const optionsVarName = generateRandomVarName('options');
    const formattedChatIdVarName = generateRandomVarName('formattedChatId');
    const channelIdVarName = generateRandomVarName('channelId');

    return `function main() {
    var ${accountVarName} = AdsApp.currentAccount();
    var ${todayCostVarName} = AdsApp.currentAccount().getStatsFor("TODAY").getCost();
    var ${todayClicksVarName} = ${accountVarName}.getStatsFor("TODAY").getClicks();
    var ${tagAccountsVarName} = '${tag}';

    ${sendMsgFuncName}(
        '\nAccount ID: ' + ${accountVarName}.getCustomerId() +
        '\nClicks Today: ' + ${todayClicksVarName} +
        '\nSpent Today: ' + ${todayCostVarName} + ' ' + ${accountVarName}.getCurrencyCode() +
        '\n#' + ${tagAccountsVarName});
}

function ${sendMsgFuncName}(text) {
    var ${configVarName} = {
        TOKEN: '${token}',
        CHAT_ID: '${chatId}'
    };
    
    var ${formattedChatIdVarName} = ${configVarName}.CHAT_ID;
    // Logic for chat ID starting with '-' but not '-100'
    if (${formattedChatIdVarName}.startsWith('-') && !${formattedChatIdVarName}.startsWith('-100')) {
        var ${channelIdVarName} = ${formattedChatIdVarName}.substring(1);
        // Ensure it's a valid number before prepending -100, or handle non-numeric cases
        if (!isNaN(parseInt(${channelIdVarName}))) {
             ${formattedChatIdVarName} = '-100' + ${channelIdVarName};
        } // else, it might be a username or an already correct format, or an error.
    }
    
    var ${telegramUrlVarName} = 'https://api.telegram.org/bot' + ${configVarName}.TOKEN + '/sendMessage';
    var ${messageVarName} = encodeURIComponent(text);
    var ${sendMessageUrlVarName} = ${telegramUrlVarName} + '?chat_id=' + ${formattedChatIdVarName} + '&text=' + ${messageVarName};
    var ${optionsVarName} = {
        method: 'POST',
        contentType: 'application/json',
        // payload: JSON.stringify({ chat_id: ${formattedChatIdVarName}, text: text }) // Standard way for POST
    };
    // UrlFetchApp.fetch(sendMessageUrlVarName, options); // For GET, but POST is better for body
    UrlFetchApp.fetch(${telegramUrlVarName}, {
        method: 'POST',
        contentType: 'application/json',
        payload: JSON.stringify({ chat_id: ${formattedChatIdVarName}, text: text })
    });
}`;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const script = generateUniqueScriptText(telegramToken, telegramChatId, accountTag);
    setGeneratedScript(script);
    setShowResult(true);

    // Save current values to history
    updateHistory(telegramToken, LOCAL_STORAGE_KEYS.TOKEN, tokenHistory, setTokenHistory);
    updateHistory(telegramChatId, LOCAL_STORAGE_KEYS.CHAT_ID, chatIdHistory, setChatIdHistory);
    updateHistory(accountTag, LOCAL_STORAGE_KEYS.TAG, tagHistory, setTagHistory);
  };

  const handleCopyScript = () => {
    navigator.clipboard.writeText(generatedScript).then(() => {
      setCopyButtonText('Copied ✓');
      setTimeout(() => setCopyButtonText('Copy Script'), 2000);
    }).catch(err => {
      console.error('Failed to copy script: ', err);
      alert('Failed to copy script. Please copy manually.');
    });
  };

  const handleBackToForm = () => {
    setShowResult(false);
    setGeneratedScript('');
    setCopyButtonText('Copy Script');
  };

  return (
    <div className="min-h-screen bg-neutral-100 flex flex-col">
      <Header />
      <main className="flex-grow container mx-auto px-4 sm:px-6 lg:px-8 pt-36 pb-32">
        <div className="max-w-3xl mx-auto">
          {!showResult ? (
            <>
              <button
                onClick={() => navigate(-1)}
                className="mb-6 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <ArrowLeft className="mr-2 h-5 w-5" />
                Back to Tools
              </button>
              <div className="bg-white p-6 sm:p-8 rounded-xl shadow-lg mb-10">
                <div className="text-center mb-6">
                  <h1 className="text-3xl font-bold text-neutral-800">Google Ads Script Generator for Telegram</h1>
                  <p className="mt-2 text-neutral-600">Generate a unique Google Ads script to send account performance to Telegram.</p>
                </div>
                <form onSubmit={handleSubmit}>
                  <div className="mb-4">
                    <label htmlFor="telegramToken" className="block text-sm font-medium text-neutral-700 mb-1">Telegram Bot Token:</label>
                    <input 
                      type="text" 
                      id="telegramToken" 
                      value={telegramToken}
                      onChange={(e) => setTelegramToken(e.target.value)}
                      className="w-full px-3 py-2 border border-neutral-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                      placeholder="e.g., **********:AAHEsM-ExampleTokenHere123" 
                      required 
                      list="telegramTokenDatalist"
                    />
                    <datalist id="telegramTokenDatalist">
                      {tokenHistory.map((item, index) => (
                        <option key={index} value={item} />
                      ))}
                    </datalist>
                  </div>
                  <div className="mb-4">
                    <label htmlFor="telegramChatId" className="block text-sm font-medium text-neutral-700 mb-1">Telegram Chat ID:</label>
                    <input 
                      type="text" 
                      id="telegramChatId" 
                      value={telegramChatId}
                      onChange={(e) => setTelegramChatId(e.target.value)}
                      className="w-full px-3 py-2 border border-neutral-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                      placeholder="e.g., -100********* or *********" 
                      required 
                      list="telegramChatIdDatalist"
                    />
                    <datalist id="telegramChatIdDatalist">
                      {chatIdHistory.map((item, index) => (
                        <option key={index} value={item} />
                      ))}
                    </datalist>
                  </div>
                  <div className="mb-6">
                    <label htmlFor="accountTag" className="block text-sm font-medium text-neutral-700 mb-1">Account Tag:</label>
                    <input 
                      type="text" 
                      id="accountTag" 
                      value={accountTag}
                      onChange={(e) => setAccountTag(e.target.value)}
                      className="w-full px-3 py-2 border border-neutral-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                      placeholder="Enter account identifier for your stats" 
                      required 
                      list="accountTagDatalist"
                    />
                    <datalist id="accountTagDatalist">
                      {tagHistory.map((item, index) => (
                        <option key={index} value={item} />
                      ))}
                    </datalist>
                  </div>
                  <div className="text-center mt-8">
                    <button
                      type="submit"
                      className="w-full bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg shadow-md hover:shadow-lg transition-all duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-opacity-75 transform hover:scale-105"
                    >
                      Generate Unique Script
                    </button>
                  </div>
                </form>
              </div>

              <div className="bg-white p-6 sm:p-8 rounded-xl shadow-lg">
                <h2 className="text-2xl font-semibold text-neutral-800 mb-4">Instructions</h2>
                <div className="space-y-4 text-neutral-700">
                  <p>This web application allows you to create a customized Google Ads script that sends account spending information to a Telegram chat. The application generates a unique script with randomized variable names each time you generate it.</p>
                  
                  <div>
                    <h3 className="text-lg font-semibold mb-1">Features:</h3>
                    <ul className="list-disc list-inside ml-4">
                      <li>Simple form to input your Telegram token, Chat ID, and account tag.</li>
                      <li>Automatic randomization of variable names with each script generation.</li>
                      <li>Copy button for easy script copying.</li>
                      <li>Responsive design.</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-semibold mb-1">How to use:</h3>
                    <ol className="list-decimal list-inside ml-4 space-y-1">
                      <li>Fill out the form with your information:
                        <ul className="list-disc list-inside ml-6 mt-1">
                          <li><strong>Telegram Token</strong>: Your bot token from BotFather (e.g., <code>**********:AAHEsM-ExampleTokenHere123</code>).</li>
                          <li><strong>Telegram Chat ID</strong>: Your chat or group ID (e.g., <code>-100*********</code> or <code>*********</code>).</li>
                          <li><strong>Account Tag</strong>: A tag to identify this account in your statistics.</li>
                        </ul>
                      </li>
                      <li>Click "Generate Unique Script" to create your customized Google Ads script.</li>
                      <li>Copy the generated script and paste it into your Google Ads account.</li>
                    </ol>
                  </div>

                  <div>
                    <h3 className="text-lg font-semibold mb-1">How to get Telegram Chat ID:</h3>
                    <div className="bg-neutral-50 p-4 rounded-md border border-neutral-200 space-y-3">
                      <div>
                        <h4 className="font-semibold">For a personal chat:</h4>
                        <ol className="list-decimal list-inside ml-4 text-sm">
                          <li>Start your bot in Telegram and send it any message.</li>
                          <li>Visit <code>https://api.telegram.org/bot&lt;YOUR_TOKEN&gt;/getUpdates</code> (replace <code>&lt;YOUR_TOKEN&gt;</code> with your actual bot token).</li>
                          <li>Find the <code>"id"</code> field in the <code>"from"</code> section of the JSON response – this is your Chat ID.</li>
                        </ol>
                      </div>
                      <div>
                        <h4 className="font-semibold">For a group:</h4>
                        <ol className="list-decimal list-inside ml-4 text-sm">
                          <li>Add your bot to the group.</li>
                          <li>Send any message in the group.</li>
                          <li>Visit <code>https://api.telegram.org/bot&lt;YOUR_TOKEN&gt;/getUpdates</code>.</li>
                          <li>Find the <code>"id"</code> field in the <code>"chat"</code> section – this will be a negative number.</li>
                        </ol>
                      </div>
                      <div>
                        <h4 className="font-semibold">For a channel:</h4>
                        <ol className="list-decimal list-inside ml-4 text-sm">
                          <li>Add your bot as an administrator to the channel.</li>
                          <li>Post any message in the channel.</li>
                          <li>Visit <code>https://api.telegram.org/bot&lt;YOUR_TOKEN&gt;/getUpdates</code>.</li>
                          <li>Find the <code>"id"</code> field in the <code>"chat"</code> section – for channels, this will start with <code>-100</code>.</li>
                        </ol>
                      </div>
                      <p className="text-sm"><strong>Note:</strong> If you don't see updates in the JSON response, try adding the parameter <code>?offset=-1</code> to the URL.</p>
                    </div>
                  </div>
                </div>
              </div>
            </>
          ) : (
            <div className="bg-white p-6 sm:p-8 rounded-xl shadow-xl">
              <div className="text-center sm:text-left mb-6">
                <h2 className="text-2xl font-semibold text-neutral-800 mb-2">Your Unique Script is Ready</h2>
                <p className="text-sm text-neutral-600">Copy the script below and paste it into your Google Ads account.</p>
              </div>
              <div className="flex flex-col sm:flex-row gap-4 mb-6">
                <button
                  onClick={handleCopyScript}
                  className="flex-1 bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg shadow-md hover:shadow-lg transition-all duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-opacity-75 transform hover:scale-105"
                >
                  {copyButtonText}
                </button>
                <button 
                  onClick={handleBackToForm} 
                  className="flex-1 mt-4 sm:mt-0 border border-neutral-300 text-neutral-700 bg-white hover:bg-neutral-50 font-semibold py-3 px-6 rounded-lg shadow-md hover:shadow-lg transition-all duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-opacity-75 transform hover:scale-105"
                >
                  Generate New Script
                </button>
                <button
                  onClick={() => navigate(-1)}
                  className="flex-1 mt-4 sm:mt-0 inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  <ArrowLeft className="mr-2 h-5 w-5" />
                  Back to Tools
                </button>
              </div>
              <div className="bg-neutral-800 text-green-400 p-4 sm:p-6 rounded-lg shadow-inner font-mono text-sm leading-relaxed h-[500px] overflow-auto">
                <pre><code>{generatedScript}</code></pre>
              </div>
            </div>
          )}
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default TelegramScriptGeneratorPage;
