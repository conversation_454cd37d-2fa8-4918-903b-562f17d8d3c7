import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import Header from '../components/Header';
import Footer from '../components/Footer';

const MAX_HISTORY_ITEMS = 3;
const historyPrefix = 'gadsBudgetUpdater_'; // Unique prefix for this page's localStorage items

const GAdsBudgetUpdaterPage: React.FC = () => {
  const navigate = useNavigate();
  const [campaignName, setCampaignName] = useState('');
  const [maxBudget, setMaxBudget] = useState('');
  const [maxTimes, setMaxTimes] = useState('');
  const [upPercent, setUpPercent] = useState('');
  const [telegramBotToken, setTelegramBotToken] = useState('');
  const [telegramChatId, setTelegramChatId] = useState('');

  const [generatedScript, setGeneratedScript] = useState('');
  const [showScript, setShowScript] = useState(false);
  const [alert, setAlert] = useState<{ message: string; type: 'success' | 'error' | 'info' | null }>({
    message: '',
    type: null,
  });
  const [showTelegramHelp, setShowTelegramHelp] = useState(false);

  // --- LocalStorage History Functions ---
  const getSavedValues = useCallback((field: string): string[] => {
    try {
      const savedData = localStorage.getItem(`${historyPrefix}${field}_history`);
      return savedData ? JSON.parse(savedData) : [];
    } catch (error) {
      console.error("Error reading from localStorage:", error);
      return [];
    }
  }, []);

  const saveFieldValueToHistory = useCallback((field: string, value: string) => {
    if (!value || !value.trim()) return; // Don't save empty or whitespace-only values
    try {
      let values = getSavedValues(field);
      // Remove the value if it already exists to move it to the top
      values = values.filter(v => v !== value);
      values.unshift(value); // Add new value to the beginning
      values = values.slice(0, MAX_HISTORY_ITEMS); // Keep only the last N items
      localStorage.setItem(`${historyPrefix}${field}_history`, JSON.stringify(values));
    } catch (error) {
      console.error("Error writing to localStorage:", error);
    }
  }, [getSavedValues]);

  // Effect to load initial values from history or defaults
  useEffect(() => {
    setCampaignName(getSavedValues('campaignName')[0] || 'Website traffic-Search-1');
    setMaxBudget(getSavedValues('maxBudget')[0] || '1300');
    setMaxTimes(getSavedValues('maxTimes')[0] || '10');
    setUpPercent(getSavedValues('upPercent')[0] || '0.1');
    setTelegramBotToken(getSavedValues('telegramBotToken')[0] || '');
    setTelegramChatId(getSavedValues('telegramChatId')[0] || '');
  }, [getSavedValues]);

  // Generic input change handler
  const handleInputChange = (
    setter: React.Dispatch<React.SetStateAction<string>>,
    fieldName: string
  ) => (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setter(value);
    saveFieldValueToHistory(fieldName, value);
  };
  
  // --- Script Generation Logic ---
  const generateRandomName = (prefix = 'customVar') => {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let result = prefix + '_';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  };

  const showAlertMessage = (message: string, type: 'success' | 'error' | 'info') => {
    setAlert({ message, type });
    setTimeout(() => setAlert({ message: '', type: null }), 5000);
  };

  const handleGenerateScript = () => {
    // Basic validation
    if (!campaignName || !maxBudget || !maxTimes || !upPercent) {
      showAlertMessage('Please fill in all required Google Ads fields.', 'error');
      return;
    }
    if (parseFloat(maxBudget) <= 0 || parseInt(maxTimes, 10) <= 0 || parseFloat(upPercent) <= 0) {
      showAlertMessage('Budget, number of increases, and percentage must be positive values.', 'error');
      return;
    }

    // Save current values to history (in case they were not saved by onBlur/onChange yet)
    saveFieldValueToHistory('campaignName', campaignName);
    saveFieldValueToHistory('maxBudget', maxBudget);
    saveFieldValueToHistory('maxTimes', maxTimes);
    saveFieldValueToHistory('upPercent', upPercent);
    if (telegramBotToken) saveFieldValueToHistory('telegramBotToken', telegramBotToken);
    if (telegramChatId) saveFieldValueToHistory('telegramChatId', telegramChatId);

    // Generate random variable names
    const campaignNameVar = generateRandomName('targetCampaignNameCfg');
    const maxBudgetVar = generateRandomName('maxBudgetCfg');
    const maxTimesVar = generateRandomName('maxIncrementsCfg');
    const upPercentVar = generateRandomName('incrementPercentCfg');
    const telegramBotTokenVar = generateRandomName('telegramTokenCfg');
    const telegramChatIdVar = generateRandomName('telegramChatIdCfg');
    const sendTelegramFunc = generateRandomName('sendTelegramNotificationUtil');
    const findCampaignFunc = generateRandomName('findCampaignUtil');
    const campaignVar = generateRandomName('campaignObj');
    const initialBudgetVar = generateRandomName('initialBudgetVal');
    const incrementCounterVar = generateRandomName('incrementCounter');
    const currentBudgetVar = generateRandomName('currentBudgetVal');
    const newBudgetVar = generateRandomName('newBudgetTarget');
    const finalBudgetVar = generateRandomName('finalBudgetVal');
    const messageVar = generateRandomName('statusMessage');
    const logMsgVar = generateRandomName('logMsg'); // For Telegram message

    let script = `// Generated by Windsurf AI for GAds Budget Updater
var ${campaignNameVar} = '${campaignName.replace(/'/g, "\\'")}';
var ${maxBudgetVar} = ${parseFloat(maxBudget)};
var ${maxTimesVar} = ${parseInt(maxTimes, 10)};
var ${upPercentVar} = ${parseFloat(upPercent)};
var ${telegramBotTokenVar} = '${telegramBotToken.replace(/'/g, "\\'")}';
var ${telegramChatIdVar} = '${telegramChatId.replace(/'/g, "\\'")}';

function ${sendTelegramFunc}(${logMsgVar}) {
  if (!${telegramBotTokenVar} || !${telegramChatIdVar}) {
    Logger.log("Telegram Bot Token or Chat ID not specified. Notification skipped.");
    return;
  }
  var url = 'https://api.telegram.org/bot' + ${telegramBotTokenVar} + '/sendMessage';
  var payload = {
    'chat_id': ${telegramChatIdVar},
    'text': ${logMsgVar},
    'parse_mode': 'HTML' // Using HTML for bold tags
  };
  var options = {
    'method': 'post',
    'contentType': 'application/json',
    'payload': JSON.stringify(payload),
    'muteHttpExceptions': true
  };
  try {
    var response = UrlFetchApp.fetch(url, options);
    Logger.log("Attempted to send Telegram notification. Response Code: " + response.getResponseCode() + ". Response: " + response.getContentText().substring(0, 500));
  } catch (e) {
    Logger.log("Error sending Telegram notification: " + e.toString());
  }
}

function ${findCampaignFunc}(name) {
  var selectors = [];
  // Add all supported campaign types with try-catch for availability
  try { selectors.push({ selector: AdsApp.performanceMaxCampaigns(), type: 'Performance Max' }); } catch(e){ Logger.log('No access to Performance Max campaigns: ' + e); }
  try { selectors.push({ selector: AdsApp.videoCampaigns(), type: 'Video' }); } catch(e){ Logger.log('No access to Video campaigns: ' + e); }
  try { selectors.push({ selector: AdsApp.shoppingCampaigns(), type: 'Shopping' }); } catch(e){ Logger.log('No access to Shopping campaigns: ' + e); }
  
  // Campaign types requiring general selector with CampaignType condition
  try { selectors.push({ selector: AdsApp.campaigns().withCondition("CampaignType = DISPLAY"), type: 'Display' }); } catch(e){ Logger.log('No access to Display campaigns (AdsApp.campaigns): ' + e); }
  try { selectors.push({ selector: AdsApp.campaigns().withCondition("CampaignType = SEARCH"), type: 'Search' }); } catch(e){ Logger.log('No access to Search campaigns (AdsApp.campaigns): ' + e); }
  try { selectors.push({ selector: AdsApp.campaigns().withCondition("CampaignType = SMART"), type: 'Smart' }); } catch(e){ Logger.log('No access to Smart campaigns (AdsApp.campaigns): ' + e); }
  try { selectors.push({ selector: AdsApp.campaigns().withCondition("CampaignType = HOTEL"), type: 'Hotel' }); } catch(e){ Logger.log('No access to Hotel campaigns (AdsApp.campaigns): ' + e); }

  // Fallback to a general selector for other types, or if specific ones fail.
  try { selectors.push({ selector: AdsApp.campaigns(), type: 'Other/General' }); } catch(e){ Logger.log('No access to AdsApp.campaigns(): ' + e); }

  var nameVariations = [
    name, // Original name
    name.replace(/-/g, ' '),
    name.replace(/\\"/g, ''), // Remove quotes if they are escaped
    name.replace(/"/g, '')    // Remove quotes
  ];
  nameVariations = nameVariations.filter(function(item, pos) { return nameVariations.indexOf(item) == pos; }); // Unique variations

  Logger.log('Searching for campaign with name variations: ' + JSON.stringify(nameVariations));

  for (var v = 0; v < nameVariations.length; v++) {
    var currentName = nameVariations[v];
    Logger.log('Trying name variation: "' + currentName + '"');
    for (var i = 0; i < selectors.length; i++) {
      try {
        var campaignIterator = selectors[i].selector
          .withCondition('CampaignName = "' + currentName.replace(/"/g, '\\"') + '"') // Escape quotes for the query
          .withLimit(1)
          .get();
        if (campaignIterator.hasNext()) {
          var foundCampaign = campaignIterator.next();
          Logger.log('Campaign "' + foundCampaign.getName() + '" found with type: ' + selectors[i].type);
          return foundCampaign;
        }
      } catch (e) {
        // Logger.log('Error searching in type ' + selectors[i].type + ' for name "' + currentName + '": ' + e.toString().substring(0, 200));
        // This can be noisy, so only log if truly unexpected or keep it minimal.
      }
    }
  }
  
  Logger.log('Exact match failed. Trying case-insensitive and partial matching...');
  try {
    var allCampaignsIterator = AdsApp.campaigns().get();
    while (allCampaignsIterator.hasNext()) {
      var c = allCampaignsIterator.next();
      var cName = c.getName();
      for (var v = 0; v < nameVariations.length; v++) {
        var variationLower = nameVariations[v].toLowerCase();
        var cNameLower = cName.toLowerCase();
        if (cNameLower === variationLower) {
          Logger.log('Found campaign by case-insensitive match: "' + cName + '"');
          return c;
        }
        if (cNameLower.indexOf(variationLower) !== -1 || variationLower.indexOf(cNameLower) !== -1) {
           Logger.log('Found campaign by partial match (contains/contained by): "' + cName + '" with variation "' + nameVariations[v] + '"');
           return c;
        }
      }
    }
  } catch (e) {
    Logger.log('Error during case-insensitive/partial search: ' + e.toString());
  }

  var campaignList = 'Available campaigns (first 10): ';
  var count = 0;
  try {
    var allCamps = AdsApp.campaigns().get();
    while(allCamps.hasNext() && count < 10) {
      var camp = allCamps.next();
      campaignList += '\\n- "' + camp.getName() + '" (ID: ' + camp.getId() + ')';
      count++;
    }
    if (allCamps.hasNext()) campaignList += '\\n- ... and more.';
  } catch(e) { campaignList = "Could not retrieve campaign list.";}
  
  throw new Error('Campaign not found: "' + name + '". Please check the name and type. ' + campaignList);
}

function main() {
  var ${campaignVar};
  try {
    ${campaignVar} = ${findCampaignFunc}(${campaignNameVar});
  } catch (e) {
    Logger.log("Error finding campaign: " + e.toString());
    ${sendTelegramFunc}("❌ <b>Google Ads Script Error:</b>\\nFailed to find campaign: " + ${campaignNameVar} + "\\nError: " + e.toString().substring(0, 500));
    return;
  }

  var ${initialBudgetVar} = ${campaignVar}.getBudget().getAmount();
  var ${incrementCounterVar} = 0;
  Logger.log("Campaign: '" + ${campaignVar}.getName() + "'. Initial budget: " + ${initialBudgetVar}.toFixed(2));
  
  while (${incrementCounterVar} < ${maxTimesVar} && ${campaignVar}.getBudget().getAmount() < ${maxBudgetVar}) {
    var ${currentBudgetVar} = ${campaignVar}.getBudget().getAmount();
    var ${newBudgetVar} = ${currentBudgetVar} * (1 + ${upPercentVar});
    
    if (${newBudgetVar} > ${maxBudgetVar}) {
      ${newBudgetVar} = ${maxBudgetVar};
    }

    // Ensure new budget is actually higher to prevent tiny increments or infinite loops with rounding
    if (parseFloat(${newBudgetVar}.toFixed(2)) <= parseFloat(${currentBudgetVar}.toFixed(2))) {
        Logger.log("New calculated budget (" + ${newBudgetVar}.toFixed(2) + ") is not greater than current budget (" + ${currentBudgetVar}.toFixed(2) + "). Stopping increase.");
        break;
    }
    
    ${campaignVar}.getBudget().setAmount(${newBudgetVar});
    ${incrementCounterVar}++;
    Logger.log("Iteration " + ${incrementCounterVar} + ": Budget set to " + ${campaignVar}.getBudget().getAmount().toFixed(2));
    
    if (${campaignVar}.getBudget().getAmount() >= ${maxBudgetVar}) {
      Logger.log("Maximum budget reached or exceeded.");
      break;
    }
  }
  
  var ${finalBudgetVar} = ${campaignVar}.getBudget().getAmount();
  var ${messageVar};
  
  if (${incrementCounterVar} > 0) {
    ${messageVar} = "✅ <b>Google Ads Script: Budget Updated</b>\\n" +
               "Campaign: <b>'" + ${campaignVar}.getName() + "'</b> (Target: '"+${campaignNameVar}+"')\\n" +
               "Old Budget: " + ${initialBudgetVar}.toFixed(2) + "\\n" +
               "New Budget: " + ${finalBudgetVar}.toFixed(2) + "\\n" +
               "Increments: " + ${incrementCounterVar};
    if (${incrementCounterVar} >= ${maxTimesVar} && ${finalBudgetVar} < ${maxBudgetVar}) {
      ${messageVar} += "\\nReached max increments limit (" + ${maxTimesVar} + ").";
    }
    if (${finalBudgetVar} >= ${maxBudgetVar}) {
      ${messageVar} += "\\nReached max budget limit (" + ${maxBudgetVar}.toFixed(2) + ").";
    }
  } else if (${initialBudgetVar} >= ${maxBudgetVar}) {
     ${messageVar} = "ℹ️ <b>Google Ads Script: Budget Not Changed</b>\\n" +
               "Campaign: <b>'" + ${campaignVar}.getName() + "'</b>\\n" +
               "Current Budget: " + ${finalBudgetVar}.toFixed(2) + "\\n" +
               "Reason: Budget is already at or above max target (" + ${maxBudgetVar}.toFixed(2) + ").";
  } else {
     ${messageVar} = "ℹ️ <b>Google Ads Script: Budget Not Changed</b>\\n" +
               "Campaign: <b>'" + ${campaignVar}.getName() + "'</b>\\n" +
               "Current Budget: " + ${finalBudgetVar}.toFixed(2) + "\\n" +
               "Reason: No increase was made (e.g., new budget not greater than current or other logic). Check logs for details.";
  }
  
  Logger.log(${messageVar}.replace(/<b>/g, "").replace(/<\/b>/g, "")); // Log plain text
  ${sendTelegramFunc}(${messageVar}); // Send HTML formatted message
}
`;

    // Remove single-line comments
    script = script.replace(/\/\/.*/g, '');
    // Remove multi-line comments
    script = script.replace(/\/\*[\s\S]*?\*\//g, '');
    // Remove empty lines that might result from comment removal
    script = script.replace(/^\s*$(?:\r\n?|\n)/gm, '');

    setGeneratedScript(script);
    setShowScript(true);
    showAlertMessage('Script generated successfully!', 'success');
    // Scroll to result
    setTimeout(() => {
        const resultSection = document.getElementById('resultSection');
        resultSection?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  };

  const handleCopyScript = async () => {
    if (!generatedScript) return;
    try {
      await navigator.clipboard.writeText(generatedScript);
      showAlertMessage('Script copied to clipboard!', 'success');
    } catch (err) {
      showAlertMessage('Failed to copy script.', 'error');
      console.error('Failed to copy script: ', err);
    }
  };

  const renderInputWithHistory = (
    id: string,
    label: string,
    value: string,
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void,
    type = 'text',
    placeholder = '',
    required = false,
    min?: string,
    max?: string,
    step?: string
  ) => {
    const historyValues = getSavedValues(id);
    return (
      <div className="mb-4">
        <label htmlFor={id} className="block text-sm font-medium text-gray-700 mb-1">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
        <div className="relative">
          <input
            type={type}
            id={id}
            name={id}
            value={value}
            onChange={onChange}
            placeholder={placeholder}
            required={required}
            min={min}
            max={max}
            step={step}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
          />
          {historyValues.length > 0 && (
            <select
              onChange={(e) => {
                const selectedValue = e.target.value;
                if (selectedValue) {
                  // Find the corresponding setter and call it
                  if (id === 'campaignName') setCampaignName(selectedValue);
                  else if (id === 'maxBudget') setMaxBudget(selectedValue);
                  else if (id === 'maxTimes') setMaxTimes(selectedValue);
                  else if (id === 'upPercent') setUpPercent(selectedValue);
                  else if (id === 'telegramBotToken') setTelegramBotToken(selectedValue);
                  else if (id === 'telegramChatId') setTelegramChatId(selectedValue);
                }
              }}
              className="absolute right-0 top-0 bottom-0 w-auto bg-gray-50 border-l border-gray-300 text-gray-700 text-xs rounded-r-md hover:bg-gray-100 focus:outline-none p-2 cursor-pointer"
              title="Load from history"
              defaultValue=""
            >
              <option value="" disabled>History</option>
              {historyValues.map((histVal, index) => (
                <option key={index} value={histVal}>{histVal.length > 20 ? histVal.substring(0,17) + '...' : histVal}</option>
              ))}
            </select>
          )}
        </div>
      </div>
    );
  };

  return (
    <> 
      <Header />
      <main className="flex-grow pt-36 pb-32">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-3xl">
          <button
            onClick={() => navigate(-1)}
            className="mb-6 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <ArrowLeft className="mr-2 h-5 w-5" />
            Back to Tools
          </button>
          <h1 className="text-2xl md:text-3xl font-bold text-gray-800 mb-6 text-center">
            Google Ads Budget Updater Script Generator
          </h1>

          {alert?.type && (
            <div className={`p-4 mb-4 text-sm rounded-lg ${            alert.type === 'success' ? 'bg-green-100 text-green-700' : 
            alert.type === 'error' ? 'bg-red-100 text-red-700' : 
            'bg-blue-100 text-blue-700'
          }`} role="alert">
              {alert.message}
            </div>
          )}

          <div className="bg-white shadow-xl rounded-lg p-6 md:p-8">
            <div className="mb-4 p-4 bg-blue-50 border-l-4 border-blue-500 text-blue-700">
              <p className="font-semibold">How it works:</p>
              <p className="text-sm">
                This tool generates a unique Google Ads script to automatically increase the budget of a specified campaign and (optionally) send Telegram notifications.
              </p>
            </div>

            <div className="mb-6 p-4 bg-yellow-50 border-l-4 border-yellow-500 text-yellow-700 text-sm">
                <p className="font-semibold mb-1">Supported Campaign Types for Budget Change:</p>
                <ul className="list-disc list-inside text-xs">
                    <li>Performance Max (including PMax for retail)</li>
                    <li>Video Campaigns</li>
                    <li>Shopping Campaigns (standard and PMax for retail)</li>
                    <li>Display Campaigns (including Smart Display)</li>
                    <li>Search Campaigns</li>
                    <li>Smart Campaigns</li>
                    <li>Hotel Campaigns</li>
                </ul>
                <p className="mt-2 text-xs">
                    Note: App Campaigns might be found if their name is unique and accessible via `AdsApp.campaigns()`, but no specific selector is used for them. The script searches by name across listed types, trying more specific selectors first.
                </p>
            </div>


            <form onSubmit={(e) => e.preventDefault()}>
              {/* Google Ads Settings */}
              <div className="mb-8 border border-gray-200 rounded-lg p-4">
                <h2 className="text-xl font-semibold text-gray-700 mb-4 pb-2 border-b border-gray-200">Google Ads Settings</h2>
                {renderInputWithHistory('campaignName', 'Campaign Name (Exact match from Google Ads)', campaignName, handleInputChange(setCampaignName, 'campaignName'), 'text', 'e.g., My Awesome Campaign - Search', true)}
                {renderInputWithHistory('maxBudget', 'Max Daily Budget (in account currency)', maxBudget, handleInputChange(setMaxBudget, 'maxBudget'), 'number', 'e.g., 1000', true, "1", undefined, "any")}
                {renderInputWithHistory('maxTimes', 'Max Budget Increases (per script run)', maxTimes, handleInputChange(setMaxTimes, 'maxTimes'), 'number', 'e.g., 5', true, "1", undefined, "1")}
                {renderInputWithHistory('upPercent', 'Budget Increase Percentage (e.g., 0.1 for 10%)', upPercent, handleInputChange(setUpPercent, 'upPercent'), 'number', 'e.g., 0.1', true, "0.01", "2", "0.01")}
              </div>

              {/* Telegram Settings */}
              <div className="mb-8 border border-gray-200 rounded-lg p-4">
                <h2 className="text-xl font-semibold text-gray-700 mb-4 pb-2 border-b border-gray-200">Telegram Settings (Optional)</h2>
                <p className="text-sm text-gray-600 mb-3">Leave fields empty if you do not want Telegram notifications.</p>
                {renderInputWithHistory('telegramBotToken', 'Telegram Bot Token', telegramBotToken, handleInputChange(setTelegramBotToken, 'telegramBotToken'), 'text', 'e.g., 123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11')}
                {renderInputWithHistory('telegramChatId', 'Telegram Chat ID', telegramChatId, handleInputChange(setTelegramChatId, 'telegramChatId'), 'text', 'e.g., -100123456789 or 987654321')}
                
                <div className="mt-4">
                    <button
                        type="button"
                        onClick={() => setShowTelegramHelp(!showTelegramHelp)}
                        className="text-sm text-primary-600 hover:text-primary-700 focus:outline-none"
                    >
                        {showTelegramHelp ? 'Hide' : 'Show'} How to get Telegram Bot Token and Chat ID
                    </button>
                    {showTelegramHelp && (
                        <div className="mt-2 p-3 bg-gray-50 rounded-md text-xs text-gray-700 border">
                            <h5 className="font-semibold mb-1">To get Bot Token:</h5>
                            <ol className="list-decimal list-inside ml-4 mb-2">
                                <li>In Telegram, search for BotFather.</li>
                                <li>Send it the command <code>/newbot</code>.</li>
                                <li>Follow instructions to create a new bot.</li>
                                <li>You will receive a token for your bot.</li>
                            </ol>
                            <h5 className="font-semibold mb-1">To get Chat ID:</h5>
                            <ol className="list-decimal list-inside ml-4">
                                <li><strong>For personal chat:</strong> Find @userinfobot (or @myidbot), send <code>/start</code>. It will reply with your ID.</li>
                                <li><strong>For group chat:</strong>
                                    <ul className="list-disc list-inside ml-4">
                                        <li>Add your newly created bot to the group.</li>
                                        <li>Send any message to the group.</li>
                                        <li>Open in browser (replace <code>&lt;YourBotToken&gt;</code>):<br /><code>https://api.telegram.org/bot&lt;YourBotToken&gt;/getUpdates</code></li>
                                        <li>Find the JSON object for your message. Chat ID is <code>chat.id</code> (usually negative for groups).</li>
                                    </ul>
                                </li>
                            </ol>
                        </div>
                    )}
                </div>
              </div>
              
              <button
                type="button"
                onClick={handleGenerateScript}
                className="w-full bg-primary-600 hover:bg-primary-700 text-white font-bold py-3 px-4 rounded-lg shadow-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-opacity-50 transition duration-150 ease-in-out"
              >
                Generate Unique Script
              </button>
            </form>

            {showScript && generatedScript && (
              <div id="resultSection" className="mt-8 p-6 bg-gray-800 rounded-lg shadow-inner">
                <div className="flex justify-between items-center mb-3">
                    <h3 className="text-lg font-semibold text-gray-100">Generated Google Ads Script:</h3>
                    <button
                        onClick={handleCopyScript}
                        className="bg-blue-500 hover:bg-blue-600 text-white text-sm py-2 px-4 rounded-md shadow focus:outline-none focus:ring-2 focus:ring-blue-400 transition duration-150"
                    >
                        Copy Script
                    </button>
                </div>
                <div className="bg-black p-4 rounded-md overflow-x-auto">
                  <pre className="text-sm text-green-300 whitespace-pre-wrap break-all">
                    <code>{generatedScript}</code>
                  </pre>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
};

export default GAdsBudgetUpdaterPage;
