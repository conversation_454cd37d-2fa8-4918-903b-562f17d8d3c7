import React from 'react';
import { features } from '../data/features';
import Card from './ui/Card';
import { <PERSON><PERSON><PERSON>, Zap, Target, FileText, PiggyBank, LayoutGrid } from 'lucide-react';

const iconMap = {
  'pie-chart': <PERSON><PERSON><PERSON>,
  'zap': Zap,
  'target': Target,
  'file-text': FileText,
  'piggy-bank': PiggyBank,
  'layout-grid': LayoutGrid,
};

const Features: React.FC = () => {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4 md:px-6">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-neutral-900 mb-4">
            Powerful Features
          </h2>
          <p className="text-lg text-neutral-600 max-w-2xl mx-auto">
            Our platform offers everything you need to create and manage successful advertising campaigns.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-12">
          {features.map((feature) => {
            const Icon = iconMap[feature.icon as keyof typeof iconMap];
            
            return (
              <div 
                key={feature.id} 
                className="flex flex-col items-start transition-all duration-300 hover:-translate-y-1"
              >
                <div className="mb-4 p-3 bg-primary-50 rounded-full">
                  <Icon className="h-6 w-6 text-primary-600" />
                </div>
                <h3 className="text-xl font-semibold text-neutral-900 mb-2">{feature.title}</h3>
                <p className="text-neutral-600">{feature.description}</p>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default Features;