import React from 'react';
import { <PERSON> } from 'react-router-dom';
import Button from './ui/Button';
import { ArrowRight } from 'lucide-react';

const Hero: React.FC = () => {
  return (
    <section className="pt-24 pb-12 md:pt-32 md:pb-24 bg-gradient-to-br from-primary-900 to-primary-700 text-white">
      <div className="container mx-auto px-4 md:px-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div className="order-2 lg:order-1 animate-slide-up">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight mb-6">
              Next-Generation <span className="text-secondary-300">Advertising</span> Solutions
            </h1>
            <p className="text-lg md:text-xl text-neutral-100 mb-8 max-w-2xl">
              Streamline your advertising campaigns with our cutting-edge platform.
              Drive more conversions and maximize your ROI with intelligent automation.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link to="/login">
                <Button 
                  size="lg" 
                  variant="secondary"
                  className="font-semibold group"
                >
                  Get Started
                  <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
                </Button>
              </Link>
            </div>
          </div>
          <div className="order-1 lg:order-2 flex justify-center animate-fade-in">
            <img
              src="https://images.pexels.com/photos/7688336/pexels-photo-7688336.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2"
              alt="Analytics Dashboard"
              className="rounded-lg shadow-hard w-full max-w-md object-cover"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;