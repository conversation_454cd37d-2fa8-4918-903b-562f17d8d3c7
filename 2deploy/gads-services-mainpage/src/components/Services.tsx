import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, User<PERSON><PERSON>, ArrowRight, DatabaseZap, Code2, DollarSign } from 'lucide-react'; 
import Card from './ui/Card';
import { useAuth } from '../context/AuthContext';
import { Link } from 'react-router-dom';

interface Service {
  id: string;
  icon: React.ElementType;
  title: string;
  description: string;
  link: string; 
  adminOnly?: boolean;
}

const allServices: Service[] = [
  {
    id: 'user-management',
    icon: UserCog, 
    title: 'User Management',
    description: 'Manage application users, roles, and permissions.',
    link: '/admin/user-management', 
    adminOnly: true,
  },
  {
    id: 'security',
    icon: ShieldCheck,
    title: 'Google Ads Script Generator for Telegram',
    description: 'Generate a unique Google Ads script to send account performance to Telegram.',
    link: '/tools/telegram-script-generator',
  },
  {
    id: 'audience-targeting',
    icon: DatabaseZap,
    title: 'Airtable P&L Script Generator',
    description: 'Generate a Google Ads script to sync performance data and account status with your Airtable base.',
    link: '/tools/airtable-pnl-script-generator',
    adminOnly: false, 
  },
  {
    id: 'performance-analytics',
    icon: Code2,
    title: 'Search Ads Script Generator',
    description: 'This tool will help you generate Google Ads scripts for your search campaigns.',
    link: '/tools/search-ads-script-generator',
  },
  {
    id: 'integration-api',
    icon: DollarSign,
    title: 'Google Ads Budget Updater',
    description: 'Automate Google Ads campaign budget adjustments and receive Telegram notifications.',
    link: '/tools/gads-budget-updater',
  },
  /*
  {
    id: 'support',
    icon: MessageSquare,
    title: 'Dedicated Support',
    description: 'Get expert assistance from our dedicated support team whenever you need it.',
    link: '#',
  },
  {
    id: 'customization',
    icon: Settings,
    title: 'Customization Options',
    description: 'Tailor the platform to your specific needs with extensive customization settings.',
    link: '#',
  },
  */
];

const Services: React.FC = () => {
  const { userStatus } = useAuth();

  if (userStatus === 'new') {
    return (
      <section id="services" className="py-16 bg-neutral-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-neutral-800 mb-4">Account Pending Review</h2>
          <p className="text-lg text-neutral-600 max-w-xl mx-auto">
            Thank you for registering! Your account is currently under review. 
            You will have access to services once your account is approved by an administrator.
          </p>
        </div>
      </section>
    );
  }

  if (userStatus === 'banned') {
    return (
      <section id="services" className="py-16 bg-neutral-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-red-600 mb-4">Account Access Restricted</h2>
          <p className="text-lg text-neutral-600 max-w-xl mx-auto">
            Your account access has been restricted. Please contact support if you believe this is an error.
          </p>
        </div>
      </section>
    );
  }

  const displayedServices = allServices.filter(service => {
    if (userStatus === 'admin') return true; 
    if (userStatus === 'alive') return !service.adminOnly; 
    return false; 
  });

  // Sort services based on user role
  if (userStatus === 'admin') {
    displayedServices.sort((a, b) => {
      if (a.id === 'user-management') return -1; // User Management first
      if (b.id === 'user-management') return 1;
      if (a.id === 'security') return -1; // Then Advanced Security
      if (b.id === 'security') return 1;
      return 0; // Keep original order for others
    });
  } else if (userStatus === 'alive') {
    displayedServices.sort((a, b) => {
      if (a.id === 'security') return -1; // Advanced Security first
      if (b.id === 'security') return 1;
      return 0; // Keep original order for others
    });
  }

  if (displayedServices.length === 0 && (userStatus === 'alive' || !userStatus) ) {
    return (
      <section id="services" className="py-16 bg-neutral-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-neutral-800 mb-4">No Services Available</h2>
          <p className="text-lg text-neutral-600 max-w-xl mx-auto">
            There are currently no services available for your account type. Please check back later or contact support.
          </p>
        </div>
      </section>
    );
  }

  return (
    <section id="services" className="py-16 bg-neutral-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-extrabold text-neutral-900 mb-4">
            Powerful Features, Simple Interface
          </h2>
          <p className="text-lg text-neutral-600 max-w-2xl mx-auto">
            Explore the wide range of services designed to elevate your advertising strategy and maximize your ROI.
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {displayedServices.map((service) => {
            const cardContent = (
              <Card key={service.id} className="flex flex-col h-full" hover={true}>
                <div className="mb-4 p-3 bg-primary-50 rounded-lg inline-block">
                  <service.icon className="h-6 w-6 text-primary-600" />
                </div>
                <h3 className="text-xl font-semibold text-neutral-900 mb-2">{service.title}</h3>
                <p className="text-neutral-600 mb-4 flex-grow">{service.description}</p>
                <div 
                  className={`inline-flex items-center font-medium group transition-colors ${ 
                    service.id === 'user-management' && userStatus !== 'admin' 
                      ? 'text-neutral-400 cursor-not-allowed' 
                      : 'text-primary-600'
                  }`}
                >
                  {service.id === 'user-management' ? 'Manage Users' : 'Learn more'}
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </div>
              </Card>
            );

            if (service.link.startsWith('/')) {
              if (service.id === 'user-management' && userStatus !== 'admin') {
                // For disabled admin link, don't wrap with Link, rely on Card's cursor-not-allowed style
                return <div key={service.id} className="cursor-not-allowed">{cardContent}</div>;
              }
              return (
                <Link key={service.id} to={service.link} className="block h-full">
                  {cardContent}
                </Link>
              );
            } else {
              // For external links or links not starting with '/', render as is (or wrap with <a> if desired)
              // For now, rendering cardContent directly, can be enhanced to wrap with <a> if needed
              return <div key={service.id}>{cardContent}</div>; // Or an <a> tag: <a href={service.link} className="block h-full">{cardContent}</a>
            }
          })}
        </div>
      </div>
    </section>
  );
};

export default Services;