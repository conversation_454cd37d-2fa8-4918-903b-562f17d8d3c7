import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom'; // Import useNavigate
import Input from './ui/Input';
import Button from './ui/Button';
import { auth } from '../firebase'; // Import Firebase auth
import { signInWithEmailAndPassword } from 'firebase/auth'; // Import Firebase auth function

interface LoginProps {
  className?: string;
}

const Login: React.FC<LoginProps> = ({ className }) => {
  const [email, setEmail] = useState(''); // Renamed username to email for consistency
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null); // For displaying errors
  const [loading, setLoading] = useState(false); // For loading state
  const navigate = useNavigate(); // Initialize useNavigate

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null); // Reset error
    setLoading(true);

    try {
      // Using 'email' as email for Firebase auth
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      // Signed in 
      const user = userCredential.user;
      console.log('User signed in:', user);
      // alert('Login successful!'); // Replaced with navigation
      navigate('/dashboard'); // Navigate to dashboard
      setEmail(''); // Clear fields after successful login
      setPassword('');
    } catch (err: any) {
      console.error('Error signing in:', err);
      setError(err.message); // Display Firebase error message
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-medium p-8 animate-scale-in ${className}`}>
      <div className="text-center mb-20">
        <h2 className="text-2xl font-bold text-neutral-900">Login</h2>
      </div>
      
      {error && <p className="text-red-500 text-sm text-center mb-4">{error}</p>} {/* Display error message */}

      <form onSubmit={handleSubmit}>
        <Input
          label="Email Address" // Changed label from Username to Email Address for clarity
          type="email" // Changed type to email
          placeholder="Enter your email address"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          className="mb-4"
        />
        
        <Input
          label="Password"
          type="password"
          placeholder="Enter your password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
          className="mb-6"
        />
        
        <div className="mt-6">
          <Button type="submit" variant="primary" className="w-full font-medium" disabled={loading}>
            {loading ? 'Signing In...' : 'Sign In'}
          </Button>
        </div>
        
        <div className="mt-4 text-center text-sm">
          <a href="/forgot-password" className="text-primary-600 hover:text-primary-700 transition-colors">
            Forgot password?
          </a>
          <span className="text-neutral-500 mx-2">|</span>
          <a href="/signup" className="text-primary-600 hover:text-primary-700 transition-colors">
            Sign Up
          </a>
        </div>
      </form>
    </div>
  );
};

export default Login;
