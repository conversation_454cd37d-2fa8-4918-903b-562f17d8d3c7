import React, { useState } from 'react';
import Input from './ui/Input';
import Button from './ui/Button';
import { auth } from '../firebase'; // Import Firebase auth
import { sendPasswordResetEmail } from 'firebase/auth'; // Import Firebase auth function

interface ForgotPasswordProps {
  className?: string;
}

const ForgotPassword: React.FC<ForgotPasswordProps> = ({ className }) => {
  const [email, setEmail] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccessMessage(null);
    setLoading(true);

    try {
      await sendPasswordResetEmail(auth, email);
      setSuccessMessage('If an account with this email exists, a password reset link has been sent.');
      setEmail(''); // Clear email field on success
    } catch (err: any) {
      console.error('Error sending password reset email:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-medium p-8 animate-scale-in ${className}`}>
      <div className="text-center mb-12">
        <h2 className="text-2xl font-bold text-neutral-900">Reset Password</h2>
      </div>

      {error && <p className="text-red-500 text-sm text-center mb-4">{error}</p>}
      {successMessage && <p className="text-green-500 text-sm text-center mb-4">{successMessage}</p>}
      
      <form onSubmit={handleSubmit}>
        <p className="text-neutral-600 text-sm mb-6 text-center">
          Enter the email address associated with your account, and we'll send you a link to reset your password.
        </p>
        <Input
          label="Email Address"
          type="email"
          placeholder="Enter your email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          className="mb-6"
        />
        
        <div className="mt-6">
          <Button type="submit" variant="primary" className="w-full font-medium" disabled={loading}>
            {loading ? 'Sending Link...' : 'Send Reset Link'}
          </Button>
        </div>
        
        <div className="mt-4 text-center text-sm">
          <a href="/login" className="text-primary-600 hover:text-primary-700 transition-colors font-medium">
            Back to Login
          </a>
        </div>
      </form>
    </div>
  );
};

export default ForgotPassword;
