import React from 'react';
import Button from './ui/Button';
import { Link } from 'react-router-dom';
import { CheckCircle } from 'lucide-react';

interface ThankYouProps {
  className?: string;
}

const ThankYou: React.FC<ThankYouProps> = ({ className }) => {
  return (
    <div className={`bg-white rounded-lg shadow-medium p-8 md:p-12 text-center animate-scale-in ${className}`}>
      <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-6" />
      <h2 className="text-3xl font-bold text-neutral-900 mb-4">Thank You!</h2>
      <p className="text-neutral-700 text-lg mb-8 max-w-lg mx-auto">
        Thank you for your request. The product owner will contact you shortly.
      </p>
      <Link to="/">
        <Button variant="primary" size="lg">
          Back to Homepage
        </Button>
      </Link>
    </div>
  );
};

export default ThankYou;
