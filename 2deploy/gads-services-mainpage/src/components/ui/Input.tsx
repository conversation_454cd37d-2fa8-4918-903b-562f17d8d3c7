import React from 'react';
import clsx from 'clsx';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  className?: string;
  fullWidth?: boolean;
}

const Input: React.FC<InputProps> = ({
  label,
  error,
  className,
  fullWidth = true,
  ...props
}) => {
  return (
    <div className={clsx('mb-4', fullWidth && 'w-full')}>
      {label && (
        <label className="block text-neutral-700 font-medium mb-2">
          {label}
        </label>
      )}
      <input
        className={clsx(
          'px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 transition-all',
          error
            ? 'border-error-500 focus:ring-error-500/20'
            : 'border-neutral-300 focus:border-primary-500 focus:ring-primary-500/20',
          fullWidth && 'w-full',
          className
        )}
        {...props}
      />
      {error && <p className="mt-1 text-sm text-error-600">{error}</p>}
    </div>
  );
};

export default Input;