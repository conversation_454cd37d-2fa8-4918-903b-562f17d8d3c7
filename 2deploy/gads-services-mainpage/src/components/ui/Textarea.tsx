import React from 'react';
import clsx from 'clsx';

interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
  className?: string;
  fullWidth?: boolean;
  rows?: number;
}

const Textarea: React.FC<TextareaProps> = ({
  label,
  error,
  className,
  fullWidth = true,
  rows = 4, // Default rows for a textarea
  ...props
}) => {
  return (
    <div className={clsx('mb-4', fullWidth && 'w-full')}>
      {label && (
        <label className="block text-neutral-700 font-medium mb-2">
          {label}
        </label>
      )}
      <textarea
        className={clsx(
          'px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 transition-all',
          'resize-none', // Often good to disable default resize or set to 'vertical'
          error
            ? 'border-error-500 focus:ring-error-500/20'
            : 'border-neutral-300 focus:border-primary-500 focus:ring-primary-500/20',
          fullWidth && 'w-full',
          className
        )}
        rows={rows}
        {...props}
      />
      {error && <p className="mt-1 text-sm text-error-600">{error}</p>}
    </div>
  );
};

export default Textarea;
