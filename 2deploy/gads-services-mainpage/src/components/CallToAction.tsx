import React from 'react';
import Button from './ui/Button';

const CallToAction: React.FC = () => {
  return (
    <section className="py-20 bg-gradient-to-r from-primary-600 to-primary-800 text-white">
      <div className="container mx-auto px-4 md:px-6 text-center">
        <h2 className="text-3xl md:text-4xl font-bold mb-6">Ready to transform your advertising strategy?</h2>
        <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
          Join thousands of businesses that have already improved their advertising performance with our platform.
        </p>
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Button
            size="lg"
            variant="secondary"
            className="font-semibold"
          >
            Get Started For Free
          </Button>
          <Button
            size="lg"
            variant="outline"
            className="font-semibold text-white border-white/30 hover:bg-white/10"
          >
            Schedule a Demo
          </Button>
        </div>
      </div>
    </section>
  );
};

export default CallToAction;