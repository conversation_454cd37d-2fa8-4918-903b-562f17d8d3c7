import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { navItems } from '../data/navigation'; 
import { Menu, X, LogOut as LogOutIcon } from 'lucide-react'; 
import Button from './ui/Button';
import { useAuth } from '../context/AuthContext';
import { auth } from '../firebase';
import { signOut } from 'firebase/auth';

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { currentUser } = useAuth(); 
  const navigate = useNavigate();

  const handleLogout = async () => {
    try {
      await signOut(auth);
      navigate('/');
      console.log('User signed out');
    } catch (error) {
      console.error('Error signing out: ', error);
    }
    setIsMenuOpen(false); 
  };

  const handleNavClick = (e: React.MouseEvent<HTMLAnchorElement>, href: string) => {
    if (href.startsWith('/#')) {
      e.preventDefault();
      const targetId = href.substring(2);
      const targetElement = document.getElementById(targetId);
      if (targetElement) {
        const headerOffset = 96; 
        const elementPosition = targetElement.getBoundingClientRect().top;
        const offsetPosition = elementPosition + window.pageYOffset - headerOffset;
        
        window.scrollTo({
          top: offsetPosition,
          behavior: 'smooth'
        });
      }
    } else {
      navigate(href);
    }
    setIsMenuOpen(false); 
  };

  return (
    <header
      className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md shadow-sm transition-all duration-300"
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-24">
          <Link to="/" className="text-3xl font-bold text-primary">
            gAds
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8 items-center">
            {navItems.map((item) => (
              <a 
                key={item.label}
                href={item.href} 
                onClick={(e) => handleNavClick(e, item.href)}
                className="text-neutral-700 hover:text-primary transition-colors duration-300 text-sm font-medium"
              >
                {item.label}
              </a>
            ))}
            {currentUser ? (
              <div className="flex items-center space-x-3">
                <Link to="/dashboard">
                  <Button variant="primary" size="sm">
                    Client's Area, {currentUser.email}
                  </Button>
                </Link>
                <button 
                  onClick={handleLogout} 
                  className="text-neutral-600 hover:text-primary text-sm font-medium flex items-center p-2 rounded-md hover:bg-neutral-100 transition-colors"
                  title="Logout"
                >
                  <LogOutIcon size={18} className="mr-1 md:mr-0" /> 
                  <span className="md:hidden ml-1">Logout</span> 
                </button>
              </div>
            ) : (
              <Link to="/login">
                <Button variant="primary" size="sm">
                  Get Started
                </Button>
              </Link>
            )}
          </nav>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-neutral-700 hover:text-primary focus:outline-none p-2"
              aria-label="Toggle menu"
            >
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */} 
      {isMenuOpen && (
        <div className="md:hidden bg-white shadow-lg absolute top-20 left-0 right-0 z-40">
          <nav className="flex flex-col space-y-1 p-4">
            {navItems.map((item) => (
              <a 
                key={item.label}
                href={item.href} 
                onClick={(e) => handleNavClick(e, item.href)}
                className="text-neutral-700 hover:text-primary py-2.5 px-3 rounded-md transition-colors duration-300 text-base font-medium block"
              >
                {item.label}
              </a>
            ))}
            <div className="pt-3 mt-2 border-t border-neutral-200">
              {currentUser ? (
                <>
                  <Link to="/dashboard" className="block w-full mb-2">
                    <Button variant="primary" className="w-full" size="md">
                      Client's Area, {currentUser.email}
                    </Button>
                  </Link>
                  <Button onClick={handleLogout} variant="outline" className="w-full flex items-center justify-center" size="md">
                    <LogOutIcon size={18} className="mr-2" /> Logout
                  </Button>
                </>
              ) : (
                <Link to="/login" className="block w-full">
                  <Button variant="primary" className="w-full" size="md">
                    Get Started
                  </Button>
                </Link>
              )}
            </div>
          </nav>
        </div>
      )}
    </header>
  );
};

export default Header;