import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom'; // Import useNavigate
import Input from './ui/Input';
import Button from './ui/Button';
import { auth } from '../firebase'; // Import Firebase auth
import { createUserWithEmailAndPassword } from 'firebase/auth'; // Import Firebase auth function

interface SignupProps {
  className?: string;
}

const Signup: React.FC<SignupProps> = ({ className }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState<string | null>(null); // For displaying errors
  const [loading, setLoading] = useState(false); // For loading state
  const navigate = useNavigate(); // Initialize useNavigate

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null); // Reset error before new attempt

    if (password !== confirmPassword) {
      setError("Passwords don't match!");
      return;
    }

    setLoading(true);
    try {
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      // Signed up 
      const user = userCredential.user;
      console.log('User signed up:', user);
      // alert('Signup successful! Please log in.'); // Replaced with navigation
      navigate('/thank-you'); // Navigate to thank-you page
      setEmail('');
      setPassword('');
      setConfirmPassword('');
    } catch (err: any) {
      console.error('Error signing up:', err);
      setError(err.message); // Display Firebase error message
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-medium p-8 animate-scale-in ${className}`}>
      <div className="text-center mb-12"> {/* Increased spacing to mb-12 for better visual separation */}
        <h2 className="text-2xl font-bold text-neutral-900">Create Account</h2>
      </div>
      
      {error && <p className="text-red-500 text-sm text-center mb-4">{error}</p>} {/* Display error message */}

      <form onSubmit={handleSubmit}>
        <Input
          label="Email Address"
          type="email"
          placeholder="Enter your email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          className="mb-4"
        />
        
        <Input
          label="Password"
          type="password"
          placeholder="Create a password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
          className="mb-4"
        />

        <Input
          label="Confirm Password"
          type="password"
          placeholder="Confirm your password"
          value={confirmPassword}
          onChange={(e) => setConfirmPassword(e.target.value)}
          required
          className="mb-6"
        />
        
        <div className="mt-6">
          <Button type="submit" variant="primary" className="w-full font-medium" disabled={loading}>
            {loading ? 'Signing Up...' : 'Sign Up'}
          </Button>
        </div>
        
        <div className="mt-4 text-center text-sm">
          <span>Already have an account? </span>
          <a href="/login" className="text-primary-600 hover:text-primary-700 transition-colors font-medium">
            Log In
          </a>
        </div>
      </form>
    </div>
  );
};

export default Signup;
