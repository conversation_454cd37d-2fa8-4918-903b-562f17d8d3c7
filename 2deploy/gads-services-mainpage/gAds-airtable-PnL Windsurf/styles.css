body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f7f9fc;
    color: #333;
}

.container {
    max-width: 1000px;
}

.form-container {
    background-color: #ffffff;
    padding: 25px;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
}

.result-container {
    background-color: #f9f9f9;
    padding: 20px;
    border-radius: 10px;
    margin-top: 25px;
    margin-bottom: 25px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.03);
}

h1 {
    color: #2c3e50;
    font-size: 24px;
}

.field-info {
    color: #7f8c8d;
    font-size: 12px;
    margin-top: 5px;
}

.info-block {
    background-color: #e5f7fd;
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid #3498db;
}

.info-title {
    font-weight: bold;
    margin-bottom: 5px;
    color: #2980b9;
}

pre {
    background-color: #f0f0f0;
    border-radius: 6px;
    white-space: pre-wrap;
    word-wrap: break-word;
    border: 1px solid #e0e0e0;
    max-height: 500px;
    overflow-y: auto;
    font-size: 14px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .form-container {
        padding: 15px;
    }
    
    h1 {
        font-size: 20px;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 10px;
    }
}
