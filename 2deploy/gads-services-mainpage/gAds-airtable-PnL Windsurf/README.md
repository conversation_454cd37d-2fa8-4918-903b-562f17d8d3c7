# Google Ads AirTable Script Generator

A web-based tool that generates customized Google Ads scripts for integrating with AirTable. This tool helps you create scripts that automatically update your AirTable with Google Ads account statistics and manage account statuses based on activity.

## Features

- **Customized Script Generation**: Create personalized Google Ads scripts with unique variable names
- **AirTable Integration**: Automatically connect your Google Ads account with AirTable
- **Account Status Management**: Automatically update account status to "Frozen" or "Banned" based on inactivity periods
- **Statistics Tracking**: Track important metrics like clicks, impressions, CTR, CPC, cost, and conversions
- **Form Input History**: Saves previous inputs for convenience (using localStorage)

## How It Works

1. Fill in the required information in the form:
   - Account name/Machine ID
   - Media buyer ID from AirTable
   - Niche category
   - AirTable URL
   - AirTable API key
   - Days until ban
   - Days until freeze

2. Click "Generate Script" to create your customized Google Ads script

3. Copy the generated script and implement it in your Google Ads account

## Implementation Instructions

1. Copy the generated script using the "Copy Script" button
2. Open your Google Ads account and navigate to "Tools" > "Scripts"
3. Create a new script and paste the copied code
4. Set up a schedule for script execution (daily recommended)
5. Authorize access to your account and run the script

## AirTable Connection Details

- **URL**: Find in AirTable by going to Help > API documentation > Copy URL from AUTHENTICATION section
- **API Key**: Generate at https://airtable.com/account in the API section

## Technologies Used

- HTML5
- CSS3 with Bootstrap 5
- JavaScript (Vanilla)
- Google Ads Script API
- AirTable API

## Browser Compatibility

This tool is compatible with all modern browsers including:
- Chrome
- Firefox
- Safari
- Edge

## License

This project is available for use under the [MIT License](https://opensource.org/licenses/MIT).

## Author

Created by [Your Name/Organization]

---

*Note: This tool requires a valid Google Ads account and AirTable API access to function properly.*
