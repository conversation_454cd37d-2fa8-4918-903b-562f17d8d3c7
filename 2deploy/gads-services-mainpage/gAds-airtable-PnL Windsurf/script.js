document.addEventListener('DOMContentLoaded', function() {
    const scriptForm = document.getElementById('scriptForm');
    const formContainer = document.getElementById('formContainer');
    const resultContainer = document.getElementById('resultContainer');
    const scriptContent = document.getElementById('scriptContent');
    const copyBtn = document.getElementById('copyBtn');
    const backBtn = document.getElementById('backBtn');
    
    // Form fields to track history for
    const formFields = [
        'accountName',
        'buyerId',
        'niche',
        'airtableUrl',
        'apiKey',
        'banDays',
        'freezeDays'
    ];
    
    // Load saved values and create datalists
    formFields.forEach(fieldId => {
        const input = document.getElementById(fieldId);
        if (!input) return;
        
        // Create datalist for this input
        const datalistId = `${fieldId}-history`;
        let datalist = document.getElementById(datalistId);
        
        if (!datalist) {
            datalist = document.createElement('datalist');
            datalist.id = datalistId;
            document.body.appendChild(datalist);
            input.setAttribute('list', datalistId);
        }
        
        // Load history from localStorage
        const history = getFieldHistory(fieldId);
        
        // Populate datalist with history items
        updateDatalist(datalist, history);
    });
    
    // Base script template
    const scriptTemplate = `function main() {
  var {{airName}} = '{{ACC_NAME_OR_MACHINE_ID}}'; 
  var {{airGroup}} = '{{BUYER_ID_FROM_AIRTABLE}}'; 
  var {{airTheme}} = '{{NICHE_FROM_AIRTABLE}}';  
  
  var {{airUrl}} = '{{URL_OF_AIRTABLE}}'; 
  var {{airKey}} = '{{API_OF_AIRTABLE}}'; 
  
  var {{statusBannedTerm}} = {{DAYS_TO_BAN}};
  var {{statusFrozenTerm}} = {{DAYS_TO_FREEZE}}; 
  
  var {{CTTT}} = AdsApp.currentAccount();
  
  var {{body}} = {
       'records':[    
        {
         'id': '',
         'fields':{
          'Status': 'Works',
          'Clicks (T)': {{CTTT}}.getStatsFor('TODAY').getClicks(),
          'Clicks (A)': {{CTTT}}.getStatsFor('ALL_TIME').getClicks(),
          'Impr. (T)': {{CTTT}}.getStatsFor('TODAY').getImpressions(),
          'Impr. (A)': {{CTTT}}.getStatsFor('ALL_TIME').getImpressions(),
          'CTR (T)': {{CTTT}}.getStatsFor('TODAY').getCtr(),
          'CTR (A)': {{CTTT}}.getStatsFor('ALL_TIME').getCtr(),
          'CPC (T)': {{CTTT}}.getStatsFor('TODAY').getAverageCpc(),
          'CPC (A)': {{CTTT}}.getStatsFor('ALL_TIME').getAverageCpc(),
          'Cost (T)': {{CTTT}}.getStatsFor('TODAY').getCost(),
          'Cost (A)': {{CTTT}}.getStatsFor('ALL_TIME').getCost(),
          'CPM (T)': {{CTTT}}.getStatsFor('TODAY').getAverageCpm(),
          'CPM (A)': {{CTTT}}.getStatsFor('ALL_TIME').getAverageCpm(),
          'Conv (A)': {{CTTT}}.getStatsFor('ALL_TIME').getConversions(),
          'Conv (T)': {{CTTT}}.getStatsFor('TODAY').getConversions()
         }
        }
       ]
    };
  
  var {{airSearchPattern}} = "AND({Account}='"+{{airName}}+"',{Group} ='"+{{airGroup}}+"')";
  
  var {{response}} = JSON.parse({{reqGet}}('GET', {{airUrl}}+"?filterByFormula="+encodeURI({{airSearchPattern}}), {{airKey}}).getContentText()); 

  if({{response}}['records'].length > 0){
    {{body}}['records'][0]['id'] = {{response}}['records'][0]['id']; 
    
    var {{fields}} = {{response}}['records'][0]['fields'];
    var {{dataDiff}} = {{dateComparison}}({{fields}}['Last update'], new Date().toISOString());
    var {{statusCurrent}} = {{fields}}['Status'];

    if({{body}}['records'][0]['fields']['Impr. (A)'] != {{fields}}['Impr. (A)']){
      {{reqCustom}}('PATCH', {{airUrl}}, {{body}}, {{airKey}})
    }else 
      if({{dataDiff}} > {{statusBannedTerm}} && {{statusCurrent}} != 'Banned'){
        {{body}}['records'][0]['fields'] = 
        { 
          'Status': 'Banned',
          'Ad status': AdsApp.ads().get().next().getPolicyApprovalStatus(),
          'Disapproved reason': AdsApp.ads().get().next().getDisapprovalReasons().toString()
        };
        
        {{reqCustom}}('PATCH', {{airUrl}}, {{body}}, {{airKey}});
      }else 
        if({{dataDiff}} > {{statusFrozenTerm}} && {{statusCurrent}} != 'Frozen'){
          {{body}}['records'][0]['fields'] = 
          { 
            'Status': 'Frozen',
            'Ad status': AdsApp.ads().get().next().getPolicyApprovalStatus(),
            'Disapproved reason': AdsApp.ads().get().next().getDisapprovalReasons().toString()
          };
          {{reqCustom}}('PATCH', {{airUrl}}, {{body}}, {{airKey}});
        }
  }else{
    var {{createBody}} = {
     'records':[
      {
       'fields':{
        'Account': {{airName}},
        'Group': {{airGroup}},
        'Theme': {{airTheme}},
        'Status': 'Works'
       }
      }
     ]
    };
  
  {{response}} = JSON.parse({{reqCustom}}('POST', {{airUrl}}, {{createBody}}, {{airKey}}).getContentText());
  {{body}}['records'][0]['id'] = {{response}}['records'][0]['id']; 
  
  {{reqCustom}}('PATCH', {{airUrl}}, {{body}}, {{airKey}});
  }
}

function {{reqGet}}(method, url, key){
    var {{header}} = {
      authorization: 'Bearer ' + key
    };
  
    const {{options}} = {
        method: method,
        headers: {{header}}
    };

    return UrlFetchApp.fetch(url, {{options}});
}

function {{reqCustom}}(method, url, body, key){
  var {{header}} = {
    authorization: 'Bearer ' + key
  }
  
  var {{options}} = {
    method : method,
    headers: {{header}},
    contentType: 'application/json',
    payload: JSON.stringify(body)
  };
  
  return UrlFetchApp.fetch(url, {{options}});
}

function {{dateComparison}}(date1, date2){
  var date1 = new Date(date1);
  var date2 = new Date(date2);
  var timeDiff = Math.abs(date2.getTime() - date1.getTime());
  var {{diffDays}} = Math.ceil(timeDiff / (1000 * 3600 * 24)); 
  
  return {{diffDays}};
}`;
    
    // Generate a random string
    function generateRandomString(length = 0) {
        if(!length) length = Math.floor(Math.random() * 6) + 5; // 5-10 characters
        const characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += characters.charAt(Math.floor(Math.random() * characters.length));
        }
        return result;
    }
    
    // Generate the script with user inputs
    function generateScript(formData) {
        let uniqueScript = scriptTemplate;
        
        // Replace variable names with random strings
        uniqueScript = uniqueScript.replace(/{{airName}}/g, generateRandomString());
        uniqueScript = uniqueScript.replace(/{{airGroup}}/g, generateRandomString());
        uniqueScript = uniqueScript.replace(/{{airTheme}}/g, generateRandomString());
        uniqueScript = uniqueScript.replace(/{{airUrl}}/g, generateRandomString());
        uniqueScript = uniqueScript.replace(/{{airKey}}/g, generateRandomString());
        uniqueScript = uniqueScript.replace(/{{CTTT}}/g, generateRandomString());
        uniqueScript = uniqueScript.replace(/{{body}}/g, generateRandomString(5));
        uniqueScript = uniqueScript.replace(/{{dateComparison}}/g, generateRandomString(10));
        uniqueScript = uniqueScript.replace(/{{response}}/g, generateRandomString(10));
        uniqueScript = uniqueScript.replace(/{{diffDays}}/g, generateRandomString(10));
        uniqueScript = uniqueScript.replace(/{{statusFrozenTerm}}/g, generateRandomString(10));
        uniqueScript = uniqueScript.replace(/{{statusBannedTerm}}/g, generateRandomString(10));
        uniqueScript = uniqueScript.replace(/{{reqGet}}/g, generateRandomString(8));
        uniqueScript = uniqueScript.replace(/{{reqCustom}}/g, generateRandomString(9));
        uniqueScript = uniqueScript.replace(/{{fields}}/g, generateRandomString(6));
        uniqueScript = uniqueScript.replace(/{{dataDiff}}/g, generateRandomString(8));
        uniqueScript = uniqueScript.replace(/{{statusCurrent}}/g, generateRandomString(13));
        uniqueScript = uniqueScript.replace(/{{airSearchPattern}}/g, generateRandomString(15));
        uniqueScript = uniqueScript.replace(/{{createBody}}/g, generateRandomString(10));
        uniqueScript = uniqueScript.replace(/{{header}}/g, generateRandomString(6));
        uniqueScript = uniqueScript.replace(/{{options}}/g, generateRandomString(7));
        
        // Replace form values
        uniqueScript = uniqueScript.replace(/{{ACC_NAME_OR_MACHINE_ID}}/g, formData.accountName);
        uniqueScript = uniqueScript.replace(/{{BUYER_ID_FROM_AIRTABLE}}/g, formData.buyerId);
        uniqueScript = uniqueScript.replace(/{{NICHE_FROM_AIRTABLE}}/g, formData.niche);
        uniqueScript = uniqueScript.replace(/{{URL_OF_AIRTABLE}}/g, formData.airtableUrl);
        uniqueScript = uniqueScript.replace(/{{API_OF_AIRTABLE}}/g, formData.apiKey);
        uniqueScript = uniqueScript.replace(/{{DAYS_TO_BAN}}/g, formData.banDays);
        uniqueScript = uniqueScript.replace(/{{DAYS_TO_FREEZE}}/g, formData.freezeDays);
        
        return uniqueScript;
    }
    
    // Functions to manage field history
    function getFieldHistory(fieldId) {
        const historyJson = localStorage.getItem(`${fieldId}_history`);
        return historyJson ? JSON.parse(historyJson) : [];
    }
    
    function saveFieldHistory(fieldId, value) {
        if (!value.trim()) return; // Don't save empty values
        
        let history = getFieldHistory(fieldId);
        
        // Remove the value if it already exists to avoid duplicates
        history = history.filter(item => item !== value);
        
        // Add the new value at the beginning
        history.unshift(value);
        
        // Keep only the last 3 values
        if (history.length > 3) {
            history = history.slice(0, 3);
        }
        
        // Save back to localStorage
        localStorage.setItem(`${fieldId}_history`, JSON.stringify(history));
        
        // Update the datalist
        const datalistId = `${fieldId}-history`;
        const datalist = document.getElementById(datalistId);
        if (datalist) {
            updateDatalist(datalist, history);
        }
    }
    
    function updateDatalist(datalist, items) {
        // Clear existing options
        datalist.innerHTML = '';
        
        // Add new options
        items.forEach(item => {
            const option = document.createElement('option');
            option.value = item;
            datalist.appendChild(option);
        });
    }
    
    // Form submission handler
    scriptForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = {
            accountName: document.getElementById('accountName').value,
            buyerId: document.getElementById('buyerId').value,
            niche: document.getElementById('niche').value,
            airtableUrl: document.getElementById('airtableUrl').value,
            apiKey: document.getElementById('apiKey').value,
            banDays: document.getElementById('banDays').value,
            freezeDays: document.getElementById('freezeDays').value
        };
        
        // Save form values to history
        for (const [fieldId, value] of Object.entries(formData)) {
            saveFieldHistory(fieldId, value);
        }
        
        // Generate the script and show result
        const generatedScript = generateScript(formData);
        scriptContent.textContent = generatedScript;
        
        // Show result container, hide form
        formContainer.classList.add('d-none');
        resultContainer.classList.remove('d-none');
    });
    
    // Copy button handler
    copyBtn.addEventListener('click', function() {
        const scriptText = scriptContent.textContent;
        navigator.clipboard.writeText(scriptText).then(() => {
            copyBtn.textContent = '✓ Скопійовано!';
            setTimeout(() => {
                copyBtn.textContent = 'Скопіювати скрипт';
            }, 2000);
        }).catch(err => {
            console.error('Помилка копіювання: ', err);
            alert('Не вдалося скопіювати. Спробуйте виділити текст вручну.');
        });
    });
    
    // Back button handler
    backBtn.addEventListener('click', function() {
        resultContainer.classList.add('d-none');
        formContainer.classList.remove('d-none');
    });
});
