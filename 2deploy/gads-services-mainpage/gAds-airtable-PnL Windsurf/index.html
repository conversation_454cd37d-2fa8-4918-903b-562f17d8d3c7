<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Ads AirTable Скрипт</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container py-4">
        <div class="form-container" id="formContainer">
            <h1 class="text-center mb-4">Генератор Google Ads скрипту для AirTable</h1>
            
            <div class="info-block mb-4">
                <div class="info-title">Що робить цей скрипт?</div>
                <p>Цей скрипт підключається до вашої таблиці AirTable і автоматично оновлює статистику рекламного акаунта Google Ads. Він також змінює статус акаунта на "Frozen" або "Banned" в залежності від кількості днів неактивності.</p>
            </div>
            
            <form id="scriptForm">
                <div class="mb-3 row">
                    <label for="accountName" class="col-sm-12 col-md-4 col-form-label">Назва акаунта / ID машини:</label>
                    <div class="col-sm-12 col-md-8">
                        <input type="text" class="form-control" id="accountName" placeholder="Введіть ідентифікатор акаунту" required>
                        <div class="field-info">Унікальна назва для ідентифікації акаунта в AirTable</div>
                    </div>
                </div>
                
                <div class="mb-3 row">
                    <label for="buyerId" class="col-sm-12 col-md-4 col-form-label">ID медіабаєра з AirTable:</label>
                    <div class="col-sm-12 col-md-8">
                        <input type="text" class="form-control" id="buyerId" placeholder="Наприклад: buyer123" required>
                        <div class="field-info">Ідентифікатор групи покупця в таблиці AirTable</div>
                    </div>
                </div>
                
                <div class="mb-3 row">
                    <label for="niche" class="col-sm-12 col-md-4 col-form-label">Ніша з AirTable:</label>
                    <div class="col-sm-12 col-md-8">
                        <input type="text" class="form-control" id="niche" placeholder="Наприклад: CRPT" required>
                        <div class="field-info">Тематика акаунта для категоризації</div>
                    </div>
                </div>
                
                <div class="mb-3 row">
                    <label for="airtableUrl" class="col-sm-12 col-md-4 col-form-label">URL AirTable:</label>
                    <div class="col-sm-12 col-md-8">
                        <input type="text" class="form-control" id="airtableUrl" placeholder="https://api.airtable.com/v0/appXXXXXXXXXXXXXX/tableName" required>
                        <div class="field-info">Повний URL до API таблиці AirTable</div>
                    </div>
                </div>
                
                <div class="mb-3 row">
                    <label for="apiKey" class="col-sm-12 col-md-4 col-form-label">API ключ AirTable:</label>
                    <div class="col-sm-12 col-md-8">
                        <input type="text" class="form-control" id="apiKey" placeholder="keyXXXXXXXXXXXXXX" required>
                        <div class="field-info">Ваш персональний API ключ для доступу до AirTable</div>
                    </div>
                </div>
                
                <div class="mb-3 row">
                    <label for="banDays" class="col-sm-12 col-md-4 col-form-label">Днів до бану:</label>
                    <div class="col-sm-12 col-md-8">
                        <input type="number" class="form-control" id="banDays" placeholder="Наприклад: 30" required>
                        <div class="field-info">Кількість днів бездіяльності до присвоєння статусу "Banned"</div>
                    </div>
                </div>
                
                <div class="mb-3 row">
                    <label for="freezeDays" class="col-sm-12 col-md-4 col-form-label">Днів до заморозки:</label>
                    <div class="col-sm-12 col-md-8">
                        <input type="number" class="form-control" id="freezeDays" placeholder="Наприклад: 15" required>
                        <div class="field-info">Кількість днів бездіяльності до присвоєння статусу "Frozen"</div>
                    </div>
                </div>
                
                <div class="text-center">
                    <button type="submit" class="btn btn-primary btn-lg px-4">Генерувати скрипт</button>
                </div>
            </form>
        </div>
        
        <div class="form-container d-none" id="resultContainer">
            <h1 class="text-center mb-4">Ваш унікалізований скрипт готовий</h1>
            <p>Цей скрипт містить унікальні назви змінних та ваші параметри для підключення до AirTable:</p>
            
            <div class="result-container">
                <pre id="scriptContent" class="p-3"></pre>
                <div class="d-grid gap-2 d-md-block text-center">
                    <button id="copyBtn" class="btn btn-success btn-lg px-4 me-md-2">Скопіювати скрипт</button>
                    <button id="backBtn" class="btn btn-secondary btn-lg px-4">← Повернутися до форми</button>
                </div>
            </div>
            
            <div class="info-block mt-4">
                <div class="info-title">Як використовувати скрипт</div>
                <ol>
                    <li>Скопіюйте згенерований скрипт, використовуючи кнопку вище</li>
                    <li>Відкрийте свій Google Ads акаунт і перейдіть до розділу "Інструменти" > "Скрипти"</li>
                    <li>Створіть новий скрипт і вставте скопійований код</li>
                    <li>Налаштуйте розклад виконання скрипту (рекомендовано щодня)</li>
                    <li>Дайте дозвіл на доступ до свого акаунту та запустіть скрипт</li>
                </ol>
            </div>
        </div>
        
        <div class="info-block mt-5">
            <div class="info-title">Як отримати дані для підключення до AirTable?</div>
            <p><strong>URL AirTable:</strong> Увійдіть в свій AirTable акаунт → Відкрийте потрібну базу даних → Натисніть "Help" → "API documentation" → Скопіюйте URL з розділу "AUTHENTICATION" (виглядає як: https://api.airtable.com/v0/appXXXXXXXXXXXXXX/tableName)</p>
            <p><strong>API ключ AirTable:</strong> Відвідайте <a href="https://airtable.com/account" target="_blank">https://airtable.com/account</a> → Розділ "API" → "Generate API key" → Скопіюйте згенерований ключ (виглядає як: keyXXXXXXXXXXXXXX)</p>
        </div>
    </div>
    
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="script.js"></script>
</body>
</html>
