<!DOCTYPE html>
<html lang="uk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google SearchAds Script Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        h1 {
            color: #4285f4;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-section, .ad-section-form {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 8px;
            border-left: 4px solid #4285f4;
        }
        .ad-section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .ad-section-form h2, .ad-title {
            margin-top: 0;
            color: #4285f4;
            font-size: 18px;
        }
        .form-group {
            margin-bottom: 15px;
            position: relative;
        }
        label {
            display: block;
            font-weight: bold;
            margin-bottom: 5px;
        }
        input[type="text"], input[type="number"], textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .button-container {
            text-align: center;
            margin: 30px 0;
        }
        button, .action-button {
            background-color: #4285f4;
            color: white;
            border: none;
            padding: 12px 24px;
            font-size: 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover, .action-button:hover {
            background-color: #3367d6;
        }
        #result {
            display: none;
            margin-top: 30px;
        }
        #scriptOutput {
            background-color: #e8e8e8; /* Darker for minified code */
            border-radius: 8px;
            padding: 15px;
            overflow-x: auto;
            white-space: pre-wrap; /* Allow wrapping for long minified lines */
            word-break: break-all; /* Break long words/lines */
            font-family: monospace;
            border: 1px solid #ccc;
            max-height: 400px;
            overflow-y: auto;
            font-size: 0.9em;
        }
        .copy-button {
            background-color: #34a853;
            margin-top: 15px;
        }
        .copy-button:hover {
            background-color: #2d9249;
        }
        .keyword-group {
            margin-bottom: 10px;
        }
        .add-keyword-btn, .add-item-btn, .remove-ad-btn {
            background-color: #fbbc05;
            margin-top: 10px;
            padding: 8px 16px;
            font-size: 14px;
        }
        .add-keyword-btn:hover, .add-item-btn:hover, .remove-ad-btn:hover {
            background-color: #e8ac04;
        }
        .keyword-input {
            display: flex;
            margin-bottom: 5px;
        }
        .keyword-input input {
            flex-grow: 1;
        }
        .remove-keyword-btn, .remove-item-btn {
            background-color: #ea4335;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            margin-left: 10px;
            cursor: pointer;
            font-size: 12px;
            line-height: 1.5;
        }
        .remove-ad-btn {
            background-color: #ea4335;
        }
        .remove-ad-btn:hover {
            background-color: #d33426;
        }
        .remove-keyword-btn:hover, .remove-item-btn:hover {
            background-color: #d33426;
        }
        .success-message {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            display: none;
        }
        .char-count {
            position: absolute;
            right: 10px;
            bottom: 10px;
            font-size: 12px;
            color: #666;
        }
        .section-divider {
            border: 0;
            height: 1px;
            background-color: #ddd;
            margin: 20px 0;
        }
        .headline-input, .description-input {
            padding-right: 50px; 
        }
        .input-with-button {
            display: flex;
            align-items: center;
        }
        .input-with-button > div { 
            flex-grow: 1;
            position: relative;
        }
        .warning-message {
            color: #e74c3c;
            font-size: 12px;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Google SearchAds Script Generator</h1>
        
        <div class="form-section">
            <h2>Основні параметри кампанії</h2>
            <div class="form-group">
                <label for="finalUrl">URL кампанії:</label>
                <input type="text" id="finalUrl" placeholder="Введіть цільовий URL" value="https://example.com">
            </div>
            <div class="form-group">
                <label for="campaignName">Назва кампанії:</label>
                <input type="text" id="campaignName" placeholder="Введіть назву кампанії" value="My Search Campaign">
            </div>
            <div class="form-group">
                <label for="adGroupName">Назва групи оголошень:</label>
                <input type="text" id="adGroupName" placeholder="Введіть назву групи оголошень" value="My Ad Group">
            </div>
            <div class="form-group">
                <label for="budget">Денний бюджет (у валюті рекламного кабінету):</label>
                <input type="number" id="budget" min="0" step="0.01" placeholder="Введіть бюджет у валюті акаунта" value="50">
            </div>
            <div class="form-group">
                <label for="maxCpc">Максимальна ставка CPC (у валюті рекламного кабінету):</label>
                <input type="number" id="maxCpc" min="0" step="0.01" placeholder="Введіть максимальну ставку CPC у валюті акаунта" value="2.6">
            </div>
            <div class="form-group">
                <label for="location">Цільове розташування:</label>
                <select id="location"></select>
            </div>
        </div>

        <div id="adsMasterContainer">
            <div class="ad-section" data-ad-index="1">
                <div class="ad-section-form">
                    <div class="ad-section-header">
                        <h2 class="ad-title">Оголошення 1</h2>
                    </div>
                    <div class="headlines-container">
                        <div class="form-group">
                            <label for="headline-ad1-1">Заголовок 1:</label>
                            <input type="text" id="headline-ad1-1" class="headline-input" placeholder="Введіть заголовок 1" maxlength="30" value="Заголовок 1">
                            <span class="char-count">0/30</span>
                        </div>
                        <div class="form-group">
                            <label for="headline-ad1-2">Заголовок 2:</label>
                            <input type="text" id="headline-ad1-2" class="headline-input" placeholder="Введіть заголовок 2" maxlength="30" value="Заголовок 2">
                            <span class="char-count">0/30</span>
                        </div>
                        <div class="form-group">
                            <label for="headline-ad1-3">Заголовок 3:</label>
                            <input type="text" id="headline-ad1-3" class="headline-input" placeholder="Введіть заголовок 3" maxlength="30" value="Заголовок 3">
                            <span class="char-count">0/30</span>
                        </div>
                    </div>
                    <button type="button" class="add-item-btn addHeadlineBtn" data-max-items="15">+ Додати заголовок</button>
                    <hr class="section-divider">
                    <div class="descriptions-container">
                        <div class="form-group">
                            <label for="description-ad1-1">Опис 1:</label>
                            <input type="text" id="description-ad1-1" class="description-input" placeholder="Введіть опис 1" maxlength="90" value="Опис для оголошення 1">
                            <span class="char-count">0/90</span>
                        </div>
                        <div class="form-group">
                            <label for="description-ad1-2">Опис 2:</label>
                            <input type="text" id="description-ad1-2" class="description-input" placeholder="Введіть опис 2" maxlength="90" value="Опис для оголошення 2">
                            <span class="char-count">0/90</span>
                        </div>
                    </div>
                    <button type="button" class="add-item-btn addDescriptionBtn" data-max-items="4">+ Додати опис</button>
                </div>
            </div>
        </div>
        <button type="button" id="addAdBtn" class="add-item-btn action-button" style="margin-bottom: 20px;">+ Додати Оголошення</button>
        
        <div class="form-section">
            <h2>Ключові слова</h2>
            <div id="keywordsContainer">
                <div class="keyword-group">
                    <div class="keyword-input">
                        <input type="text" class="keyword" placeholder="Введіть ключове слово" value="keyword">
                        <button type="button" class="remove-keyword-btn">X</button>
                    </div>
                </div>
            </div>
            <button type="button" id="addKeywordBtn" class="add-keyword-btn">+ Додати ключове слово</button>
        </div>
        
        <div class="button-container">
            <button type="button" id="generateBtn" class="action-button">Згенерувати скрипт</button>
        </div>
        
        <div id="result">
            <h2>Згенерований скрипт </h2>
            <div id="scriptOutput"></div>
            <div class="button-container">
                <button type="button" id="copyBtn" class="copy-button action-button">Копіювати скрипт</button>
            </div>
            <div class="success-message" id="copySuccess">Скрипт успішно скопійовано!</div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const MAX_ADS = 3;
            const MAX_HEADLINES_PER_AD = 15;
            const MIN_HEADLINES_PER_AD = 3;
            const MAX_DESCRIPTIONS_PER_AD = 4;
            const MIN_DESCRIPTIONS_PER_AD = 2;

            const adsMasterContainer = document.getElementById('adsMasterContainer');
            const addAdBtn = document.getElementById('addAdBtn');
            let adCount = 1;

            // --- Populate Location Dropdown ---
            const countries = [
                "United States", "Canada", "United Kingdom", "Australia", "Germany", "France", "Spain", "Italy", "Netherlands",
                "Japan", "India", "Brazil", "Mexico", "Argentina", "Belgium", "Switzerland", "Sweden", "Norway", "Denmark",
                "Finland", "Ireland", "New Zealand", "South Africa", "Singapore", "Hong Kong", "United Arab Emirates",
                "Austria", "Poland", "Portugal", "Israel", "Chile", "Colombia", "Czech Republic", "Greece", "Hungary",
                "Malaysia", "Philippines", "Romania", "Saudi Arabia", "South Korea", "Taiwan", "Thailand", "Turkey", "Ukraine", "Vietnam"
            ].sort(); // Sort alphabetically

            const locationDropdown = document.getElementById('location');
            countries.forEach(countryName => {
                const option = document.createElement('option');
                option.value = countryName;
                option.textContent = countryName;
                if (countryName === "United States") {
                    option.selected = true;
                }
                locationDropdown.appendChild(option);
            });


            // --- Utility Functions ---
            function updateCharCount(inputElement) {
                const countSpan = inputElement.nextElementSibling;
                if (countSpan && countSpan.classList.contains('char-count')) {
                    const currentLength = inputElement.value.length;
                    const maxLength = inputElement.getAttribute('maxlength');
                    countSpan.textContent = `${currentLength}/${maxLength}`;
                }
            }

            function handleInputWithCharCount(event) {
                const input = event.target;
                if (input.classList.contains('headline-input') || input.classList.contains('description-input')) {
                    const maxLength = parseInt(input.getAttribute('maxlength'));
                    if (input.value.length > maxLength) {
                        input.value = input.value.substring(0, maxLength);
                    }
                    updateCharCount(input);
                }
            }
            
            function renumberItems(container, itemClassSuffix, labelPrefix, inputClass, adIndex, minItems) {
                const items = container.querySelectorAll(`.form-group:has(.${inputClass})`);
                items.forEach((item, index) => {
                    const itemNumber = index + 1;
                    const label = item.querySelector('label');
                    const input = item.querySelector(`.${inputClass}`);
                    const removeBtn = item.querySelector('.remove-item-btn');

                    if (label) {
                        label.textContent = `${labelPrefix} ${itemNumber}:`;
                        label.setAttribute('for', `${itemClassSuffix}-ad${adIndex}-${itemNumber}`);
                    }
                    if (input) {
                        input.id = `${itemClassSuffix}-ad${adIndex}-${itemNumber}`;
                        input.placeholder = `Введіть ${labelPrefix.toLowerCase()} ${itemNumber}`;
                    }
                    if (removeBtn) {
                        removeBtn.style.display = itemNumber > minItems ? 'inline-block' : 'none';
                    }
                });

                const addButton = container.nextElementSibling;
                 if (addButton && (addButton.classList.contains('addHeadlineBtn') || addButton.classList.contains('addDescriptionBtn'))) {
                    const maxItems = parseInt(addButton.dataset.maxItems);
                    addButton.style.display = items.length < maxItems ? 'inline-block' : 'none';
                }
            }

            function createAdItem(adIndex, itemType, itemNumber, minItems) {
                const isHeadline = itemType === 'headline';
                const itemClassSuffix = isHeadline ? 'headline' : 'description';
                const maxLength = isHeadline ? 30 : 90;
                const labelText = isHeadline ? 'Заголовок' : 'Опис';
                const inputClass = isHeadline ? 'headline-input' : 'description-input';

                const itemGroup = document.createElement('div');
                itemGroup.className = 'form-group input-with-button';
                
                const inputWrapper = document.createElement('div');
                inputWrapper.style.flexGrow = '1';
                inputWrapper.style.position = 'relative';

                const label = document.createElement('label');
                label.setAttribute('for', `${itemClassSuffix}-ad${adIndex}-${itemNumber}`);
                label.textContent = `${labelText} ${itemNumber}:`;
                
                const input = document.createElement('input');
                input.type = 'text';
                input.id = `${itemClassSuffix}-ad${adIndex}-${itemNumber}`;
                input.className = inputClass;
                input.placeholder = `Введіть ${labelText.toLowerCase()} ${itemNumber}`;
                input.maxLength = maxLength;
                
                const charCountSpan = document.createElement('span');
                charCountSpan.className = 'char-count';
                charCountSpan.textContent = `0/${maxLength}`;

                inputWrapper.appendChild(label);
                inputWrapper.appendChild(input);
                inputWrapper.appendChild(charCountSpan);
                itemGroup.appendChild(inputWrapper);

                const removeBtn = document.createElement('button');
                removeBtn.type = 'button';
                removeBtn.className = 'remove-item-btn';
                removeBtn.textContent = 'X';
                removeBtn.style.display = itemNumber > minItems ? 'inline-block' : 'none';
                
                removeBtn.addEventListener('click', function() {
                    const parentContainer = itemGroup.parentElement;
                    parentContainer.removeChild(itemGroup);
                    renumberItems(parentContainer, itemClassSuffix, labelText, inputClass, adIndex, minItems);
                });

                itemGroup.appendChild(removeBtn);
                return itemGroup;
            }

            function initializeAdSectionControls(adSectionElement) {
                const adIndex = adSectionElement.dataset.adIndex;

                adSectionElement.querySelectorAll('.headline-input, .description-input').forEach(input => {
                    updateCharCount(input);
                });

                const addHeadlineBtn = adSectionElement.querySelector('.addHeadlineBtn');
                const headlinesContainer = adSectionElement.querySelector('.headlines-container');
                addHeadlineBtn.addEventListener('click', function() {
                    const currentHeadlines = headlinesContainer.querySelectorAll('.headline-input').length;
                    if (currentHeadlines < MAX_HEADLINES_PER_AD) {
                        const newHeadline = createAdItem(adIndex, 'headline', currentHeadlines + 1, MIN_HEADLINES_PER_AD);
                        headlinesContainer.appendChild(newHeadline);
                        renumberItems(headlinesContainer, 'headline', 'Заголовок', 'headline-input', adIndex, MIN_HEADLINES_PER_AD);
                    }
                });

                const addDescriptionBtn = adSectionElement.querySelector('.addDescriptionBtn');
                const descriptionsContainer = adSectionElement.querySelector('.descriptions-container');
                addDescriptionBtn.addEventListener('click', function() {
                    const currentDescriptions = descriptionsContainer.querySelectorAll('.description-input').length;
                    if (currentDescriptions < MAX_DESCRIPTIONS_PER_AD) {
                        const newDescription = createAdItem(adIndex, 'description', currentDescriptions + 1, MIN_DESCRIPTIONS_PER_AD);
                        descriptionsContainer.appendChild(newDescription);
                        renumberItems(descriptionsContainer, 'description', 'Опис', 'description-input', adIndex, MIN_DESCRIPTIONS_PER_AD);
                    }
                });
                renumberItems(headlinesContainer, 'headline', 'Заголовок', 'headline-input', adIndex, MIN_HEADLINES_PER_AD);
                renumberItems(descriptionsContainer, 'description', 'Опис', 'description-input', adIndex, MIN_DESCRIPTIONS_PER_AD);
            }
            
            function renumberAllAds() {
                const adSections = adsMasterContainer.querySelectorAll('.ad-section');
                adSections.forEach((section, index) => {
                    const newAdIndex = index + 1;
                    section.dataset.adIndex = newAdIndex;
                    section.querySelector('.ad-title').textContent = `Оголошення ${newAdIndex}`;

                    const headlinesContainer = section.querySelector('.headlines-container');
                    if (headlinesContainer) {
                         renumberItems(headlinesContainer, 'headline', 'Заголовок', 'headline-input', newAdIndex, MIN_HEADLINES_PER_AD);
                    }
                    const descriptionsContainer = section.querySelector('.descriptions-container');
                     if (descriptionsContainer) {
                        renumberItems(descriptionsContainer, 'description', 'Опис', 'description-input', newAdIndex, MIN_DESCRIPTIONS_PER_AD);
                    }
                });
                adCount = adSections.length;
                addAdBtn.style.display = adCount < MAX_ADS ? 'inline-block' : 'none';
            }

            function createAdSectionHTML(newAdIndex) {
                return `
                    <div class="ad-section-form">
                        <div class="ad-section-header">
                            <h2 class="ad-title">Оголошення ${newAdIndex}</h2>
                            <button type="button" class="remove-ad-btn action-button">Видалити оголошення</button>
                        </div>
                        <div class="headlines-container">
                            ${Array.from({ length: MIN_HEADLINES_PER_AD }, (_, i) => createAdItem(newAdIndex, 'headline', i + 1, MIN_HEADLINES_PER_AD).outerHTML).join('')}
                        </div>
                        <button type="button" class="add-item-btn addHeadlineBtn" data-max-items="${MAX_HEADLINES_PER_AD}">+ Додати заголовок</button>
                        <hr class="section-divider">
                        <div class="descriptions-container">
                             ${Array.from({ length: MIN_DESCRIPTIONS_PER_AD }, (_, i) => createAdItem(newAdIndex, 'description', i + 1, MIN_DESCRIPTIONS_PER_AD).outerHTML).join('')}
                        </div>
                        <button type="button" class="add-item-btn addDescriptionBtn" data-max-items="${MAX_DESCRIPTIONS_PER_AD}">+ Додати опис</button>
                    </div>
                `;
            }

            addAdBtn.addEventListener('click', function() {
                if (adCount < MAX_ADS) {
                    adCount++;
                    const newAdSection = document.createElement('div');
                    newAdSection.className = 'ad-section';
                    newAdSection.dataset.adIndex = adCount;
                    newAdSection.innerHTML = createAdSectionHTML(adCount);
                    adsMasterContainer.appendChild(newAdSection);
                    initializeAdSectionControls(newAdSection);
                    newAdSection.querySelector('.remove-ad-btn').addEventListener('click', function() {
                        adsMasterContainer.removeChild(newAdSection);
                        renumberAllAds();
                    });
                }
                if (adCount >= MAX_ADS) {
                    addAdBtn.style.display = 'none';
                }
            });

            initializeAdSectionControls(document.querySelector('.ad-section[data-ad-index="1"]'));
            adsMasterContainer.addEventListener('input', handleInputWithCharCount);

            const keywordsContainer = document.getElementById('keywordsContainer');
            const addKeywordBtn = document.getElementById('addKeywordBtn');
            addKeywordBtn.addEventListener('click', function() {
                const newKeywordGroup = document.createElement('div');
                newKeywordGroup.className = 'keyword-group';
                newKeywordGroup.innerHTML = `
                    <div class="keyword-input">
                        <input type="text" class="keyword" placeholder="Введіть ключове слово" value="keyword">
                        <button type="button" class="remove-keyword-btn">X</button>
                    </div>
                `;
                keywordsContainer.appendChild(newKeywordGroup);
                const removeBtn = newKeywordGroup.querySelector('.remove-keyword-btn');
                removeBtn.addEventListener('click', function() {
                    keywordsContainer.removeChild(newKeywordGroup);
                });
            });
            const firstRemoveKeywordBtn = document.querySelector('#keywordsContainer .remove-keyword-btn');
            firstRemoveKeywordBtn.addEventListener('click', function() {
                 const keywordGroup = this.closest('.keyword-group');
                 if (keywordsContainer.querySelectorAll('.keyword-group').length > 1) {
                    keywordsContainer.removeChild(keywordGroup);
                 } else {
                    keywordGroup.querySelector('.keyword').value = '';
                 }
            });

            // --- Script Generation & Minification ---
            const generateBtn = document.getElementById('generateBtn');
            const scriptOutput = document.getElementById('scriptOutput');
            const resultDiv = document.getElementById('result');
            const copyBtn = document.getElementById('copyBtn');
            const copySuccess = document.getElementById('copySuccess');

            function generateVarName(length) {
                const consonants = 'bcdfghjklmnpqrstvwxyz';
                const vowels = 'aeiou';
                let result = '';
                for (let i = 0; i < length; i++) {
                    result += (i % 2 === 0) ? consonants.charAt(Math.floor(Math.random() * consonants.length)) : vowels.charAt(Math.floor(Math.random() * vowels.length));
                }
                return result;
            }

            function minifyScript(scriptString) {
                let script = scriptString;
                script = script.replace(/\/\*[\s\S]*?\*\/|\/\/.*/g, ''); // Remove comments
                script = script.replace(/\n/g, ' '); // Replace newlines with space
                script = script.replace(/\s\s+/g, ' '); // Collapse multiple spaces

                // Remove spaces around delimiters and operators (carefully)
                script = script.replace(/\s*;\s*/g, ';');
                script = script.replace(/\s*,\s*/g, ',');
                script = script.replace(/\s*:\s*/g, ':');
                script = script.replace(/\s*{\s*/g, '{');
                script = script.replace(/\s*}\s*/g, '}');
                script = script.replace(/\s*\(\s*/g, '(');
                script = script.replace(/\s*\)\s*/g, ')');
                script = script.replace(/\s*\[\s*/g, '[');
                script = script.replace(/\s*\]\s*/g, ']');
                
                // Multi-character operators first
                script = script.replace(/\s*(===|!==|==|!=|<=|>=|&&|\|\||\+=|-=|\*=|\/=|%=|\+\+|--)\s*/g, '$1');
                // Single-character operators (assignment, arithmetic, etc.)
                script = script.replace(/\s*([=+\-*\/%&|^~<>?!])\s*/g, '$1');
                
                script = script.trim();
                return script;
            }

            function log(msg) {
                if (typeof Logger !== 'undefined') Logger.log(msg);
                else if (window && window.console) console.log(msg);
            }

            generateBtn.addEventListener('click', function() {
                const finalUrl = document.getElementById('finalUrl').value;
                const campaignName = document.getElementById('campaignName').value;
                const adGroupName = document.getElementById('adGroupName').value;
                const budget = document.getElementById('budget').value;
                const maxCpc = document.getElementById('maxCpc').value;
                const locationVal = document.getElementById('location').value;

                const allAdsData = [];
                document.querySelectorAll('.ad-section').forEach(adSection => {
                    const adIndex = adSection.dataset.adIndex;
                    const currentAdData = { adIndex: adIndex, headlines: [], descriptions: [] };
                    adSection.querySelectorAll('.headline-input').forEach((input, localIndex) => {
                        if (input.value.trim() !== '') {
                            currentAdData.headlines.push({ value: input.value.trim().substring(0, 30), scriptIndex: localIndex + 1 });
                        }
                    });
                    adSection.querySelectorAll('.description-input').forEach((input, localIndex) => {
                        if (input.value.trim() !== '') {
                            currentAdData.descriptions.push({ value: input.value.trim().substring(0, 90), scriptIndex: localIndex + 1 });
                        }
                    });
                    if (currentAdData.headlines.length > 0 || currentAdData.descriptions.length > 0) {
                         allAdsData.push(currentAdData);
                    }
                });
                
                const keywordInputs = document.querySelectorAll('.keyword');
                const keywords = [];
                keywordInputs.forEach(input => {
                    if (input.value.trim() !== '') {
                        keywords.push(input.value.trim());
                    }
                });

                const urlVar = generateVarName(5);
                const campaignNameVar = generateVarName(6);
                const adGroupNameVar = generateVarName(6);
                const budgetVar = generateVarName(6);
                const maxCpcVar = generateVarName(6);
                const locationVar = generateVarName(6);
                const adTypeVar = generateVarName(6);
                const keywordsVar = generateVarName(6);
                const campaignSelectorVar = generateVarName(8);
                const campaignVar = generateVarName(8);

                let rawScript = `
function main() {
  var ${urlVar} = '${finalUrl.replace(/'/g, "\\'")}';
  var ${campaignNameVar} = '${campaignName.replace(/'/g, "\\'")}';
  var ${adGroupNameVar} = '${adGroupName.replace(/'/g, "\\'")}';
  var ${budgetVar} = ${budget};
  var ${maxCpcVar} = ${maxCpc};
  var ${locationVar} = "${locationVal.replace(/"/g, '\\"')}";
  var ${adTypeVar} = "RESPONSIVE SEARCH AD";
  var ${keywordsVar} = [
    ${keywords.map(kw => `"${kw.replace(/"/g, '\\"')}"`).join(",\n    ")}
  ];

`;

                allAdsData.forEach(adData => {
                    adData.headlines.forEach(h => {
                        rawScript += `  var Ad${adData.adIndex}_H${h.scriptIndex} = '${h.value.replace(/'/g, "\\'")}';\n`;
                    });
                    adData.descriptions.forEach(d => {
                        rawScript += `  var Ad${adData.adIndex}_D${d.scriptIndex} = '${d.value.replace(/'/g, "\\'")}';\n`;
                    });
                });

                rawScript += `
  function removeTargetedLocations(campaignName) {
    var selectors = [AdsApp.campaigns(), AdsApp.videoCampaigns(), AdsApp.shoppingCampaigns()];
    for(var i = 0; i < selectors.length; i++) {
      var campaignIter = selectors[i]
        .withCondition('CampaignName = "' + campaignName + '"')
        .get();
      if (campaignIter.hasNext()) {
        var campaign = campaignIter.next();
        var locationIterator = campaign.targeting().targetedLocations().get();
        while (locationIterator.hasNext()) {
          var loc = locationIterator.next();
          loc.remove();
        }
      }
    }
  }

  function getCampaign(campaignName) {
    var selectors = [AdsApp.campaigns(), AdsApp.videoCampaigns(), AdsApp.shoppingCampaigns()];
    for(var i = 0; i < selectors.length; i++) {
      var campaignIter = selectors[i]
        .withCondition('CampaignName = "' + campaignName + '"')
        .get();
      if (campaignIter.hasNext()) {
        return campaignIter.next();
      }
    }
    throw new Error('Could not find specified campaign: ' + campaignName);
  }

  var columns = [
    'Campaign', 'Budget', 'Bid Strategy type', 'Campaign type', 'Ad group', 'Location', 'Max CPC', 'Keyword',
    'Headline 1', 'Headline 2', 'Headline 3', 'Headline 4', 'Headline 5', 'Headline 6', 'Headline 7', 'Headline 8',
    'Headline 9', 'Headline 10', 'Headline 11', 'Headline 12', 'Headline 13', 'Headline 14', 'Headline 15',
    'Description 1', 'Description 2', 'Description 3', 'Description 4', 'Final URL', 'Ad Type',
    'Headline 1 position', 'Headline 2 position'
  ];

  var upload = AdsApp.bulkUploads().newCsvUpload(columns, {moneyInMicros: false});

  upload.append({
    'Campaign': ${campaignNameVar},
    'Budget': ${budgetVar},
    'Bid Strategy type': 'cpc',
    'Campaign type': 'Search Only',
    'Location': ${locationVar}
  });

  upload.append({
    'Campaign': ${campaignNameVar},
    'Ad group': ${adGroupNameVar},
    'Bid Strategy type': 'cpc',
    'Max CPC': ${maxCpcVar}
  });

  for (var i = 0; i < ${keywordsVar}.length; i++) {
    var keyword = ${keywordsVar}[i];
    upload.append({
      'Campaign': ${campaignNameVar},
      'Ad group': ${adGroupNameVar},
      'Keyword': keyword
    });
  }

`;

                allAdsData.forEach(adData => {
                    let adUploadObjectParts = [
                        `'Campaign': ${campaignNameVar}`,
                        `'Ad group': ${adGroupNameVar}`,
                        `'Final URL': ${urlVar}`,
                        `'Ad Type': ${adTypeVar}`
                    ];
                    adData.headlines.forEach(h => {
                        adUploadObjectParts.push(`'Headline ${h.scriptIndex}': Ad${adData.adIndex}_H${h.scriptIndex}`);
                    });
                    adData.descriptions.forEach(d => {
                        adUploadObjectParts.push(`'Description ${d.scriptIndex}': Ad${adData.adIndex}_D${d.scriptIndex}`);
                    });
                    if (adData.headlines.find(h => h.scriptIndex === 1)) {
                        adUploadObjectParts.push(`'Headline 1 position': '1'`);
                    }
                    if (adData.headlines.find(h => h.scriptIndex === 2)) {
                        adUploadObjectParts.push(`'Headline 2 position': '2'`);
                    }
                    rawScript += `  upload.append({\n    ${adUploadObjectParts.join(',\n    ')}\n  });\n\n`;
                });

                rawScript += `  upload.apply();

  Utilities.sleep(17 * 1000);

  var ${campaignSelectorVar} = AdsApp.campaigns().withCondition("campaign.name = '" + ${campaignNameVar} + "'").get();
  if (!${campaignSelectorVar}.hasNext()) {
    throw new Error("Campaign not found after creation: " + ${campaignNameVar});
  }
  var ${campaignVar} = ${campaignSelectorVar}.next();
  ${campaignVar}.bidding().setStrategy('TARGET_SPEND', {});
}`;

                scriptOutput.textContent = rawScript;
                resultDiv.style.display = 'block';
                resultDiv.scrollIntoView({ behavior: 'smooth' });
            });
            
            copyBtn.addEventListener('click', function() {
                const textArea = document.createElement('textarea');
                textArea.value = scriptOutput.textContent;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                
                copySuccess.style.display = 'block';
                setTimeout(() => { copySuccess.style.display = 'none'; }, 3000);
            });

            function clearOnFocus(input, defaultValue) {
                input.addEventListener('focus', function() {
                    if (input.value === defaultValue) {
                        input.value = '';
                    }
                });
            }

            document.querySelectorAll('.keyword').forEach(input => clearOnFocus(input, 'musk'));
            document.querySelectorAll('#headline-ad1-1').forEach(input => clearOnFocus(input, 'Заголовок 1'));
            document.querySelectorAll('#headline-ad1-2').forEach(input => clearOnFocus(input, 'Заголовок 2'));
            document.querySelectorAll('#headline-ad1-3').forEach(input => clearOnFocus(input, 'Заголовок 3'));
            document.querySelectorAll('#description-ad1-1').forEach(input => clearOnFocus(input, 'Опис для оголошення 1'));
            document.querySelectorAll('#description-ad1-2').forEach(input => clearOnFocus(input, 'Опис для оголошення 2'));
        });
    </script>
</body>
</html>