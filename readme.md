# gAds Supercharge - Google Ads Automation Platform

A comprehensive React-based web application for Google Ads automation and management. This platform provides tools for budget updating, performance reporting, script generation, and campaign optimization.

## 🚀 Features

- **Google Ads Budget Updater**: Automated budget management with Telegram notifications
- **Performance Analysis Tools**: Campaign, ad, and keyword performance tracking
- **Script Generators**: Custom Google Ads scripts for automation
- **AirTable Integration**: P&L reporting and data synchronization
- **Telegram Bot Integration**: Real-time notifications and alerts
- **Device Bid Optimization**: Performance-based bid adjustments
- **Keyword Conflict Detection**: Campaign optimization insights

## 🛠️ Technology Stack

- **Frontend**: React 18 with TypeScript
- **Build Tool**: Vite 5.4.2
- **Styling**: Tailwind CSS with PostCSS
- **Routing**: React Router DOM v7.6.0
- **UI Components**: Radix UI with custom components
- **Authentication**: CSV-based system (demo/development)
- **Deployment**: Netlify with automatic builds

## 📁 Project Structure

```
gads-services-mainpage/
├── src/
│   ├── components/
│   │   ├── ui/                    # Reusable UI components
│   │   ├── tools/                 # Tool-specific components
│   │   ├── dashboard/             # Dashboard components
│   │   └── ...
│   ├── pages/
│   │   ├── ToolPage.tsx           # Dynamic tool router
│   │   ├── DashboardPage.tsx      # Main dashboard
│   │   ├── LoginPage.tsx          # Authentication
│   │   └── ...
│   ├── utils/
│   │   ├── auth.ts                # Authentication utilities
│   │   └── ...
│   ├── App.tsx                    # Main application
│   └── main.tsx                   # Entry point
├── public/
│   ├── root.csv                   # User authentication data
│   └── ...
├── package.json                   # Dependencies
├── vite.config.ts                 # Build configuration
├── tailwind.config.js             # Styling configuration
└── netlify.toml                   # Deployment configuration
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18 or higher
- npm or yarn package manager

### Installation

1. **Clone and navigate to the project**:
   ```bash
   git clone <repository-url>
   cd gAds-supercharge/gads-services-mainpage
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Start development server**:
   ```bash
   npm run dev
   ```

4. **Open in browser**:
   ```
   http://localhost:5173
   ```

### Build for Production

```bash
npm run build
```

### Preview Production Build

```bash
npm run preview
```

## 🔐 Authentication

The application uses a CSV-based authentication system for demo purposes:

### Login Credentials
- **File**: `public/root.csv`
- **Format**: `login;password;role`
- **Access**: Login at `/login` with credentials from the CSV file

### Admin Access
Users with admin role have access to:
- User management features
- Advanced tool configurations
- System administration

## 🛠️ Available Tools

### Budget Management
- **Google Ads Budget Updater** (`/dashboard/gads-budget-updater`)
  - Automatic budget increases
  - Campaign pattern matching
  - Telegram notifications
  - Historical tracking

- **Budget Monitor** (`/dashboard/budget-monitor`)
  - Real-time budget monitoring
  - Threshold alerts
  - Spending analysis

### Performance Analysis
- **Campaign Performance** (`/dashboard/campaign-performance`)
- **Ad Performance** (`/dashboard/ad-performance`)
- **Keyword Performance** (`/dashboard/keyword-performance`)
- **Search Query Analysis** (`/dashboard/search-query`)

### Automation Tools
- **Telegram Script Generator** (`/dashboard/telegram-script-generator`)
- **AirTable Script Generator** (`/dashboard/airtable-script`)
- **Performance Max Analyzer** (`/dashboard/performance-max`)
- **Device Bid Adjuster** (`/dashboard/device-bid`)
- **Keyword Conflict Detector** (`/dashboard/keyword-conflict`)
- **General Script Generator** (`/dashboard/script-generator`)

## 🌐 Deployment

### Production Deployment
- **URL**: https://gads-supercharge.netlify.app/
- **Platform**: Netlify
- **Auto-deploy**: Enabled from main branch

### Manual Deployment
1. Build the project: `npm run build`
2. Deploy the `dist/` folder to your hosting platform
3. Configure SPA redirects (see `netlify.toml`)

## 📚 Documentation

- **Project Context**: `context.md` - Detailed architecture and components
- **Deployment Guide**: `deployment-context.md` - Comprehensive deployment information

## 🔧 Development

### Code Quality
```bash
npm run lint
```

### Project Structure
- **Modular Components**: Reusable UI and tool components
- **Dynamic Routing**: Tool pages loaded dynamically
- **Type Safety**: Full TypeScript implementation
- **Responsive Design**: Mobile-first approach

### Adding New Tools
1. Create component in `src/components/tools/`
2. Add route mapping in `src/pages/ToolPage.tsx`
3. Update navigation in dashboard components

## 🔒 Security

- Role-based access control
- Protected routes for authenticated users
- Input validation and sanitization
- Secure session management

**Note**: Current authentication is for demo purposes. For production, implement proper user management with encrypted passwords and secure authentication methods.

## 📈 Performance

- **Fast Development**: Vite for instant HMR
- **Optimized Builds**: Tree shaking and code splitting
- **Lazy Loading**: Route-based component loading
- **CDN Delivery**: Global content delivery via Netlify

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the documentation files
- Review the troubleshooting section in `deployment-context.md`
- Open an issue in the repository