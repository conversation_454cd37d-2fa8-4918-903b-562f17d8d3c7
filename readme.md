# gAds Supercharge

A comprehensive Google Ads automation and management platform built with React, TypeScript, and Vite.

## 🚀 Quick Start

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build
```

## 🔐 Login

- **URL**: http://localhost:5173/login
- **Email**: `<EMAIL>`
- **Password**: `hIgio12@pf`

## 📚 Documentation

- **[Full Documentation](docs/README.md)** - Complete project documentation
- **[Project Context](docs/context.md)** - Architecture and components
- **[Deployment Guide](docs/deployment-context.md)** - Deployment instructions
- **[Optimization Plan](docs/netlify-optimization-plan.md)** - Performance optimization

## 🛠️ Available Tools

- Google Ads Budget Updater
- Telegram Script Generator
- AirTable Script Generator
- Campaign Performance Analysis
- Keyword Performance Tracking
- Device Bid Optimization
- And many more...

## 🌐 Live Demo

**Production**: https://gads-supercharge.netlify.app/

## 📄 License

MIT License - see [LICENSE](LICENSE) for details.
