-- gAds Supercharge Database Schema
-- PostgreSQL initialization script

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table for authentication
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'user' CHECK (role IN ('admin', 'user')),
    is_active BOOLEAN DEFAULT true,
    preferred_language VARCHAR(5) DEFAULT 'en' CHECK (preferred_language IN ('en', 'ua')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP WITH TIME ZONE
);

-- Content management for multilingual support
CREATE TABLE content_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key_name VARCHAR(255) UNIQUE NOT NULL,
    category VARCHAR(100) NOT NULL, -- 'navigation', 'auth', 'tools', 'common', etc.
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Multilingual content storage
CREATE TABLE content_translations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    content_key_id UUID NOT NULL REFERENCES content_keys(id) ON DELETE CASCADE,
    language_code VARCHAR(5) NOT NULL CHECK (language_code IN ('en', 'ua')),
    translation_text TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(content_key_id, language_code)
);

-- User sessions tracking
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    ip_address INET,
    user_agent TEXT,
    browser_info JSONB,
    login_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    logout_time TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL
);

-- User activity logging
CREATE TABLE user_activities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_id UUID REFERENCES user_sessions(id) ON DELETE SET NULL,
    activity_type VARCHAR(100) NOT NULL, -- 'login', 'logout', 'page_view', 'tool_use', 'script_generate', etc.
    activity_data JSONB, -- Store additional activity-specific data
    ip_address INET,
    user_agent TEXT,
    page_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- User preferences
CREATE TABLE user_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    preference_key VARCHAR(100) NOT NULL,
    preference_value JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, preference_key)
);

-- Indexes for performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_active ON users(is_active);

CREATE INDEX idx_content_keys_name ON content_keys(key_name);
CREATE INDEX idx_content_keys_category ON content_keys(category);

CREATE INDEX idx_content_translations_key_lang ON content_translations(content_key_id, language_code);
CREATE INDEX idx_content_translations_language ON content_translations(language_code);

CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX idx_user_sessions_active ON user_sessions(is_active);
CREATE INDEX idx_user_sessions_expires ON user_sessions(expires_at);

CREATE INDEX idx_user_activities_user_id ON user_activities(user_id);
CREATE INDEX idx_user_activities_session_id ON user_activities(session_id);
CREATE INDEX idx_user_activities_type ON user_activities(activity_type);
CREATE INDEX idx_user_activities_created ON user_activities(created_at);

CREATE INDEX idx_user_preferences_user_key ON user_preferences(user_id, preference_key);

-- Update timestamp triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_content_keys_updated_at BEFORE UPDATE ON content_keys
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_content_translations_updated_at BEFORE UPDATE ON content_translations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_preferences_updated_at BEFORE UPDATE ON user_preferences
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default admin user (password: admin123)
INSERT INTO users (email, password_hash, role) VALUES
('<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin');

-- Insert sample user (password: user123)
INSERT INTO users (email, password_hash, role) VALUES
('<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user');

-- Insert content keys and translations
INSERT INTO content_keys (key_name, category, description) VALUES
-- Navigation
('nav.dashboard', 'navigation', 'Dashboard navigation link'),
('nav.login', 'navigation', 'Login navigation link'),
('nav.logout', 'navigation', 'Logout navigation link'),
('nav.features', 'navigation', 'Features navigation link'),
('nav.portfolio', 'navigation', 'Portfolio navigation link'),

-- Common
('common.loading', 'common', 'Loading text'),
('common.error', 'common', 'Error text'),
('common.success', 'common', 'Success text'),
('common.cancel', 'common', 'Cancel button'),
('common.save', 'common', 'Save button'),
('common.delete', 'common', 'Delete button'),
('common.edit', 'common', 'Edit button'),
('common.back', 'common', 'Back button'),
('common.next', 'common', 'Next button'),
('common.previous', 'common', 'Previous button'),
('common.submit', 'common', 'Submit button'),
('common.generate', 'common', 'Generate button'),
('common.copy', 'common', 'Copy button'),
('common.download', 'common', 'Download button'),

-- Authentication
('auth.login', 'auth', 'Login text'),
('auth.email', 'auth', 'Email field label'),
('auth.password', 'auth', 'Password field label'),
('auth.loginButton', 'auth', 'Login button text'),
('auth.loginSuccess', 'auth', 'Login success message'),
('auth.loginError', 'auth', 'Login error message'),
('auth.logout', 'auth', 'Logout text'),
('auth.welcome', 'auth', 'Welcome message'),

-- Dashboard
('dashboard.title', 'dashboard', 'Dashboard page title'),
('dashboard.welcome', 'dashboard', 'Dashboard welcome message'),
('dashboard.description', 'dashboard', 'Dashboard description'),
('dashboard.tools', 'dashboard', 'Available tools section'),
('dashboard.recentActivity', 'dashboard', 'Recent activity section'),

-- Tools
('tools.telegram.title', 'tools', 'Telegram tool title'),
('tools.telegram.description', 'tools', 'Telegram tool description'),
('tools.airtable.title', 'tools', 'Airtable tool title'),
('tools.airtable.description', 'tools', 'Airtable tool description'),
('tools.budget.title', 'tools', 'Budget tool title'),
('tools.budget.description', 'tools', 'Budget tool description'),
('tools.campaign.title', 'tools', 'Campaign tool title'),
('tools.campaign.description', 'tools', 'Campaign tool description'),
('tools.keyword.title', 'tools', 'Keyword tool title'),
('tools.keyword.description', 'tools', 'Keyword tool description'),

-- Forms
('form.required', 'forms', 'Required field message'),
('form.invalid', 'forms', 'Invalid format message'),
('form.botToken', 'forms', 'Bot token field label'),
('form.chatId', 'forms', 'Chat ID field label'),
('form.accountTag', 'forms', 'Account tag field label'),
('form.apiKey', 'forms', 'API key field label'),
('form.url', 'forms', 'URL field label'),

-- Messages
('message.scriptGenerated', 'messages', 'Script generated success message'),
('message.scriptError', 'messages', 'Script generation error message'),
('message.copySuccess', 'messages', 'Copy success message'),
('message.copyError', 'messages', 'Copy error message'),

-- Footer
('footer.rights', 'footer', 'All rights reserved text'),
('footer.company', 'footer', 'Company name'),

-- Language
('language.english', 'language', 'English language name'),
('language.ukrainian', 'language', 'Ukrainian language name');

-- Insert English translations
INSERT INTO content_translations (content_key_id, language_code, translation_text)
SELECT id, 'en',
  CASE key_name
    -- Navigation
    WHEN 'nav.dashboard' THEN 'Dashboard'
    WHEN 'nav.login' THEN 'Login'
    WHEN 'nav.logout' THEN 'Logout'
    WHEN 'nav.features' THEN 'Features'
    WHEN 'nav.portfolio' THEN 'Portfolio'

    -- Common
    WHEN 'common.loading' THEN 'Loading...'
    WHEN 'common.error' THEN 'Error'
    WHEN 'common.success' THEN 'Success'
    WHEN 'common.cancel' THEN 'Cancel'
    WHEN 'common.save' THEN 'Save'
    WHEN 'common.delete' THEN 'Delete'
    WHEN 'common.edit' THEN 'Edit'
    WHEN 'common.back' THEN 'Back'
    WHEN 'common.next' THEN 'Next'
    WHEN 'common.previous' THEN 'Previous'
    WHEN 'common.submit' THEN 'Submit'
    WHEN 'common.generate' THEN 'Generate'
    WHEN 'common.copy' THEN 'Copy'
    WHEN 'common.download' THEN 'Download'

    -- Authentication
    WHEN 'auth.login' THEN 'Login'
    WHEN 'auth.email' THEN 'Email'
    WHEN 'auth.password' THEN 'Password'
    WHEN 'auth.loginButton' THEN 'Sign In'
    WHEN 'auth.loginSuccess' THEN 'Login successful!'
    WHEN 'auth.loginError' THEN 'Invalid credentials'
    WHEN 'auth.logout' THEN 'Logout'
    WHEN 'auth.welcome' THEN 'Welcome back!'

    -- Dashboard
    WHEN 'dashboard.title' THEN 'Dashboard'
    WHEN 'dashboard.welcome' THEN 'Welcome to gAds Supercharge'
    WHEN 'dashboard.description' THEN 'Your Google Ads automation and management platform'
    WHEN 'dashboard.tools' THEN 'Available Tools'
    WHEN 'dashboard.recentActivity' THEN 'Recent Activity'

    -- Tools
    WHEN 'tools.telegram.title' THEN 'Telegram Script Generator'
    WHEN 'tools.telegram.description' THEN 'Generate Google Ads scripts for Telegram notifications'
    WHEN 'tools.airtable.title' THEN 'Airtable Script Generator'
    WHEN 'tools.airtable.description' THEN 'Generate scripts for Airtable P&L reporting'
    WHEN 'tools.budget.title' THEN 'Budget Updater'
    WHEN 'tools.budget.description' THEN 'Automated budget management with notifications'
    WHEN 'tools.campaign.title' THEN 'Campaign Performance'
    WHEN 'tools.campaign.description' THEN 'Analyze campaign performance metrics'
    WHEN 'tools.keyword.title' THEN 'Keyword Performance'
    WHEN 'tools.keyword.description' THEN 'Track keyword performance and optimization'

    -- Forms
    WHEN 'form.required' THEN 'This field is required'
    WHEN 'form.invalid' THEN 'Invalid format'
    WHEN 'form.botToken' THEN 'Bot Token'
    WHEN 'form.chatId' THEN 'Chat ID'
    WHEN 'form.accountTag' THEN 'Account Tag'
    WHEN 'form.apiKey' THEN 'API Key'
    WHEN 'form.url' THEN 'URL'

    -- Messages
    WHEN 'message.scriptGenerated' THEN 'Script generated successfully!'
    WHEN 'message.scriptError' THEN 'Error generating script'
    WHEN 'message.copySuccess' THEN 'Copied to clipboard!'
    WHEN 'message.copyError' THEN 'Failed to copy'

    -- Footer
    WHEN 'footer.rights' THEN 'All rights reserved'
    WHEN 'footer.company' THEN 'gAds Supercharge'

    -- Language
    WHEN 'language.english' THEN 'English'
    WHEN 'language.ukrainian' THEN 'Українська'
  END
FROM content_keys;

-- Insert Ukrainian translations
INSERT INTO content_translations (content_key_id, language_code, translation_text)
SELECT id, 'ua',
  CASE key_name
    -- Navigation
    WHEN 'nav.dashboard' THEN 'Панель керування'
    WHEN 'nav.login' THEN 'Вхід'
    WHEN 'nav.logout' THEN 'Вихід'
    WHEN 'nav.features' THEN 'Можливості'
    WHEN 'nav.portfolio' THEN 'Портфоліо'

    -- Common
    WHEN 'common.loading' THEN 'Завантаження...'
    WHEN 'common.error' THEN 'Помилка'
    WHEN 'common.success' THEN 'Успішно'
    WHEN 'common.cancel' THEN 'Скасувати'
    WHEN 'common.save' THEN 'Зберегти'
    WHEN 'common.delete' THEN 'Видалити'
    WHEN 'common.edit' THEN 'Редагувати'
    WHEN 'common.back' THEN 'Назад'
    WHEN 'common.next' THEN 'Далі'
    WHEN 'common.previous' THEN 'Попередній'
    WHEN 'common.submit' THEN 'Відправити'
    WHEN 'common.generate' THEN 'Генерувати'
    WHEN 'common.copy' THEN 'Копіювати'
    WHEN 'common.download' THEN 'Завантажити'

    -- Authentication
    WHEN 'auth.login' THEN 'Вхід'
    WHEN 'auth.email' THEN 'Електронна пошта'
    WHEN 'auth.password' THEN 'Пароль'
    WHEN 'auth.loginButton' THEN 'Увійти'
    WHEN 'auth.loginSuccess' THEN 'Успішний вхід!'
    WHEN 'auth.loginError' THEN 'Невірні дані для входу'
    WHEN 'auth.logout' THEN 'Вихід'
    WHEN 'auth.welcome' THEN 'Ласкаво просимо!'

    -- Dashboard
    WHEN 'dashboard.title' THEN 'Панель керування'
    WHEN 'dashboard.welcome' THEN 'Ласкаво просимо до gAds Supercharge'
    WHEN 'dashboard.description' THEN 'Ваша платформа для автоматизації та управління Google Ads'
    WHEN 'dashboard.tools' THEN 'Доступні інструменти'
    WHEN 'dashboard.recentActivity' THEN 'Остання активність'

    -- Tools
    WHEN 'tools.telegram.title' THEN 'Генератор Telegram скриптів'
    WHEN 'tools.telegram.description' THEN 'Генерація скриптів Google Ads для Telegram сповіщень'
    WHEN 'tools.airtable.title' THEN 'Генератор Airtable скриптів'
    WHEN 'tools.airtable.description' THEN 'Генерація скриптів для звітності P&L в Airtable'
    WHEN 'tools.budget.title' THEN 'Оновлювач бюджету'
    WHEN 'tools.budget.description' THEN 'Автоматизоване управління бюджетом з сповіщеннями'
    WHEN 'tools.campaign.title' THEN 'Ефективність кампаній'
    WHEN 'tools.campaign.description' THEN 'Аналіз метрик ефективності кампаній'
    WHEN 'tools.keyword.title' THEN 'Ефективність ключових слів'
    WHEN 'tools.keyword.description' THEN 'Відстеження та оптимізація ключових слів'

    -- Forms
    WHEN 'form.required' THEN 'Це поле обов''язкове'
    WHEN 'form.invalid' THEN 'Невірний формат'
    WHEN 'form.botToken' THEN 'Токен бота'
    WHEN 'form.chatId' THEN 'ID чату'
    WHEN 'form.accountTag' THEN 'Тег акаунту'
    WHEN 'form.apiKey' THEN 'API ключ'
    WHEN 'form.url' THEN 'URL'

    -- Messages
    WHEN 'message.scriptGenerated' THEN 'Скрипт успішно згенеровано!'
    WHEN 'message.scriptError' THEN 'Помилка генерації скрипту'
    WHEN 'message.copySuccess' THEN 'Скопійовано в буфер обміну!'
    WHEN 'message.copyError' THEN 'Не вдалося скопіювати'

    -- Footer
    WHEN 'footer.rights' THEN 'Всі права захищені'
    WHEN 'footer.company' THEN 'gAds Supercharge'

    -- Language
    WHEN 'language.english' THEN 'English'
    WHEN 'language.ukrainian' THEN 'Українська'
  END
FROM content_keys;
