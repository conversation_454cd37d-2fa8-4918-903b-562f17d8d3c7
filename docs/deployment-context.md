# gAds Supercharge - Deployment Context

## Deployment Architectures

### Current Frontend-Only Deployment
- **URL**: https://gads-supercharge.netlify.app/
- **Platform**: Netlify
- **Source**: `gads-services-mainpage/` directory
- **Build Command**: `npm run build` (Vite build)
- **Publish Directory**: `dist/`

### Full-Stack Deployment (Docker)
- **Architecture**: Multi-container Docker setup
- **Services**: PostgreSQL + Node.js Backend + React Frontend + Redis
- **Orchestration**: Docker Compose
- **Reverse Proxy**: Nginx for production
- **Database**: PostgreSQL with persistent volumes

## Deployment Environments

### 1. Frontend-Only Production (Netlify)
**Configuration**:
- Automatic deployments from main branch
- Build command: `vite build`
- Node version: Latest LTS (18+)
- Environment variables: Set in Netlify dashboard
- Authentication: CSV-based (legacy)

### 2. Full-Stack Development (Docker)
**Configuration**:
- Local development with Docker Compose
- Hot reload for frontend and backend
- PostgreSQL with sample data
- JWT authentication with session tracking
- Real-time language switching

### 3. Full-Stack Production (Docker)
**Configuration**:
- Production-optimized Docker images
- SSL/TLS termination with Nginx
- Database backups and monitoring
- Health checks and logging
- Environment-based configuration

## Docker Deployment Guide

### Quick Start
```bash
# Clone repository
git clone <repository-url>
cd gads-supercharge

# Start all services
./start.sh

# Or manually
docker-compose up -d
```

### Service Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Frontend │    │  Node.js Backend │    │   PostgreSQL    │
│   (Port 5173)   │◄──►│   (Port 3001)   │◄──►│   (Port 5432)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         └──────────────►│      Redis      │◄─────────────┘
                        │   (Port 6379)   │
                        └─────────────────┘
```

### Environment Configuration

#### Backend (.env)
```env
NODE_ENV=production
PORT=3001
DB_HOST=postgres
DB_PORT=5432
DB_NAME=gads_db
DB_USER=gads_user
DB_PASSWORD=gads_password
JWT_SECRET=your-super-secret-jwt-key
CORS_ORIGIN=http://localhost:5173
```

#### Frontend (Vite)
```env
VITE_API_URL=http://localhost:3001/api
VITE_NODE_ENV=production
```

### Production Deployment
```bash
# Build production images
docker-compose -f docker-compose.yml -f docker-compose.prod.yml build

# Deploy with production settings
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Monitor services
docker-compose logs -f
```

### Health Monitoring
- **Database**: `pg_isready` health checks
- **Backend**: `/health` endpoint monitoring
- **Frontend**: Nginx status checks
- **Redis**: Connection monitoring

**Required Files for Frontend-Only Deployment**:
```
gads-services-mainpage/
├── src/                  # Application source code
├── public/               # Static assets including root.csv
├── package.json          # Dependencies and build scripts
├── vite.config.ts        # Build configuration
├── tailwind.config.js    # Styling configuration
├── tsconfig.json         # TypeScript configuration
├── netlify.toml          # Netlify deployment settings
└── index.html            # Entry point
```

### 2. Development Environment
**Local Setup**:
```bash
cd gads-services-mainpage
npm install
npm run dev
```

**Requirements**:
- Node.js 18+ 
- npm or yarn
- Modern browser with ES6+ support

## Build Process

### Frontend Build (`gads-services-mainpage/`)
1. **Install Dependencies**: `npm install`
2. **Type Checking**: TypeScript compilation
3. **Linting**: ESLint validation (optional)
4. **Build**: Vite bundles for production
5. **Output**: Static files in `dist/` directory

### Build Optimization
- **Tree Shaking**: Unused code elimination
- **Code Splitting**: Dynamic imports for route-based splitting
- **Asset Optimization**: Image and CSS optimization
- **Minification**: JavaScript and CSS minification

## Deployment Pipeline

### Netlify Configuration
```toml
# netlify.toml
[build]
  base = "gads-services-mainpage/"
  command = "npm run build"
  publish = "dist/"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Security headers
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# Cache static assets
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
```

### Manual Deployment Steps
1. Navigate to project directory: `cd gads-services-mainpage`
2. Install dependencies: `npm install`
3. Build project: `npm run build`
4. Deploy `dist/` folder to hosting platform

## Authentication System

### CSV-based Authentication
**File**: `public/root.csv`
- Format: `login;password;role`
- Deployed with static assets
- Accessible at `/root.csv` endpoint

**Security Note**: This is a development/demo authentication system. For production, consider:
- Database-based user management
- Encrypted password storage
- JWT or session-based authentication
- OAuth integration

## Environment Management

### Development Environment
- Local development server with hot reload
- Source maps enabled for debugging
- Development-specific configurations

### Production Environment
- Optimized builds with minification
- Asset compression and caching
- Error tracking and monitoring

## Performance Optimization

### Build Optimizations
- **Bundle Analysis**: Monitor bundle size and dependencies
- **Lazy Loading**: Route-based code splitting implemented
- **Asset Optimization**: Automatic image and CSS optimization
- **CDN**: Netlify's global CDN for static assets

### Runtime Optimizations
- **Caching**: Browser caching for static assets
- **Compression**: Gzip/Brotli compression enabled
- **Preloading**: Critical resource preloading

## Monitoring and Maintenance

### Health Checks
- **Uptime Monitoring**: Netlify status dashboard
- **Performance**: Core Web Vitals tracking
- **Error Tracking**: Console error monitoring

### Maintenance Tasks
- **Dependency Updates**: Regular npm audit and updates
- **Security Patches**: Automated security updates
- **Performance Reviews**: Monthly performance audits

## Backup and Recovery

### Code Repository
- **Primary**: GitHub repository
- **Backup**: Automated GitHub backups
- **Versioning**: Git tags for releases

### Static Assets
- **Authentication Data**: `root.csv` backed up securely
- **Configuration**: All config files version controlled

## Scaling Considerations

### Traffic Scaling
- **CDN**: Netlify's global CDN handles traffic spikes
- **Caching**: Aggressive caching for static content
- **Performance**: Optimized bundle sizes

### Feature Scaling
- **Modular Architecture**: Easy to add new tools/features
- **Component Library**: Reusable UI components
- **Tool System**: Dynamic tool loading architecture

## Security Configuration

### HTTPS
- **SSL/TLS**: Automatic HTTPS via Netlify
- **HSTS**: HTTP Strict Transport Security headers
- **CSP**: Content Security Policy headers

### Authentication Security
- **Session Management**: localStorage-based sessions
- **Access Control**: Role-based access control
- **Input Validation**: Form validation and sanitization

## Troubleshooting

### Common Issues
1. **Build Failures**: Check Node.js version compatibility
2. **Authentication Issues**: Verify `root.csv` format and accessibility
3. **Routing Issues**: Ensure SPA redirect rules are configured
4. **Asset Loading**: Check public folder structure

### Debug Commands
```bash
# Local development
npm run dev

# Build locally
npm run build

# Preview production build
npm run preview

# Check dependencies
npm audit
```

## Deployment Checklist

### Pre-Deployment
- [ ] All tests passing
- [ ] Code linted and formatted
- [ ] Authentication data configured
- [ ] Performance audit completed
- [ ] Security headers configured

### Post-Deployment
- [ ] Site accessibility verified
- [ ] All routes working correctly
- [ ] Authentication flow tested
- [ ] Tool functionality verified
- [ ] Performance metrics reviewed

## Tool-Specific Deployment Notes

### Google Ads Tools
- Scripts generated are client-side only
- No server-side Google Ads API integration required
- All tools work with static deployment

### CSV Authentication
- `root.csv` must be in `public/` folder
- Accessible at `/root.csv` endpoint
- Format: `login;password;role`

### Asset Requirements
- All static assets in `public/` folder
- Images, icons, and data files
- Proper MIME type handling
