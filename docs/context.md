# gAds Supercharge - Project Context

## Project Overview

gAds Supercharge is a comprehensive Google Ads automation and management platform with full-stack architecture including React frontend, Node.js backend, PostgreSQL database, and Docker deployment. The application provides a suite of tools for campaign management, budget optimization, performance reporting, and script generation for Google Ads automation.

**Current Status**: Full-stack application with complete bilingual support (English/Ukrainian), PostgreSQL-based user authentication, session management, and comprehensive dashboard with 478+ translated content keys.

## Architecture

### Full-Stack Architecture
- **Frontend**: React 18 + TypeScript + Vite
- **Backend**: Node.js + Express.js + PostgreSQL
- **Database**: PostgreSQL with multilingual content management
- **Deployment**: Docker + Docker Compose
- **Authentication**: JWT-based with session tracking
- **Internationalization**: Database-driven content (English/Ukrainian)

### Frontend Application
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite 5.4.8
- **Styling**: Tailwind CSS with PostCSS
- **Routing**: React Router DOM v7.6.0
- **UI Components**: Radix UI with custom components
- **Authentication**: JWT-based with API integration
- **State Management**: React Context API and hooks

### Backend Infrastructure
- **Runtime**: Node.js 18+ with Express.js framework
- **Database**: PostgreSQL 15 with connection pooling
- **Authentication**: JWT tokens with session management
- **Security**: Helmet, CORS, rate limiting, input validation
- **Logging**: Comprehensive request and activity logging
- **Health Monitoring**: Service health checks and monitoring

### Database Schema
- **users**: User authentication and preferences
- **content_keys**: Multilingual content keys
- **content_translations**: Language-specific translations (EN/UA)
- **user_sessions**: Session tracking with browser/device info
- **user_activities**: Comprehensive activity logging
- **user_preferences**: User-specific settings and preferences

### Key Technologies
- **React Helmet Async**: SEO and meta tag management
- **React Hook Form**: Form handling and validation
- **Lucide React**: Icon library
- **Canvas**: Chart and visualization support
- **Class Variance Authority**: Component styling utilities
- **Tailwind Merge**: Dynamic class merging utility
- **PostgreSQL**: Primary database with JSONB support
- **Docker**: Containerization and deployment
- **Nginx**: Reverse proxy and static file serving

## Recent Updates and Improvements

### Full-Stack Architecture Implementation (Latest)
- **Backend API**: Complete Node.js + Express.js backend with PostgreSQL database
- **Database-driven Content**: Multilingual content management system (EN/UA)
- **User Authentication**: JWT-based authentication with session tracking
- **Activity Logging**: Comprehensive user activity tracking and analytics
- **Docker Deployment**: Full containerization with Docker Compose
- **Real-time Language Switching**: Seamless language changes without page reload
- **Session Management**: Device tracking and browser information logging
- **Health Monitoring**: Service health checks and monitoring endpoints

### Security Enhancements
- **Variable Obfuscation**: All script generators use completely obfuscated variable names
- **JWT Authentication**: Secure token-based authentication with expiration
- **Session Validation**: Active session tracking with device information
- **Rate Limiting**: API protection against DDoS and abuse
- **CORS Protection**: Secure cross-origin resource sharing
- **SQL Injection Prevention**: Parameterized queries and input validation
- **XSS Protection**: Security headers and content sanitization

### Database-driven Multilingual Support
- **Content Management**: All UI text stored in PostgreSQL database
- **Language Switching**: Real-time language changes with API integration
- **User Preferences**: Language preferences saved to user profile
- **Fallback System**: Graceful fallback to English for missing translations
- **Admin Content Management**: API endpoints for content creation/updates

### User Activity Analytics
- **Login/Logout Tracking**: Detailed authentication event logging
- **Session Analytics**: Browser, device, and IP address tracking
- **Page View Logging**: Comprehensive page navigation tracking
- **Tool Usage Analytics**: Detailed tool interaction monitoring
- **API Call Logging**: Request/response tracking with performance metrics

### Message Format Optimization
- **Telegram Reports**: Optimized message format for hourly updates with compact multi-column layout
- **Readability**: Reduced message height by 50% while maintaining all metrics
- **User Experience**: Improved scannable format for frequent notifications

### Documentation Enhancement
- **Full-Stack Setup**: Complete Docker deployment documentation
- **API Documentation**: Comprehensive endpoint documentation
- **Database Schema**: Detailed schema documentation with relationships
- **Security Guidelines**: Best practices for production deployment

## Core Features

### 1. Authentication System
**Location**: `src/contexts/AuthContext.tsx`, `backend/src/routes/auth.js`
- PostgreSQL-based user authentication with bcryptjs password hashing
- JWT token-based authentication with 24-hour expiration
- Role-based access control (admin, user roles)
- Session tracking with browser and device information
- Protected routes for dashboard access
- User credentials:
  - <EMAIL> / admin123 (admin role)
  - <EMAIL> / user123 (user role)
  - <EMAIL> / demo123 (user role)

### 2. Dashboard and Tools
**Main Dashboard**: `src/components/Dashboard.tsx`
- User profile and account information
- Tool navigation and quick access
- Recent budget adjustments summary
- Role-based feature access

### 3. Google Ads Tools Suite

#### Budget Management Tools
- **Google Ads Budget Updater** (`/dashboard/gads-budget-updater`)
  - Automatic budget increases up to specified maximums
  - Campaign pattern matching
  - Telegram notification integration
  - Historical data tracking

- **Budget Monitor** (`/dashboard/budget-monitor`)
  - Campaign budget monitoring and alerts
  - Threshold-based notifications
  - Spending analysis

#### Performance Analysis Tools
- **Campaign Performance** (`/dashboard/campaign-performance`)
  - Campaign metrics and KPI tracking
  - Performance comparison and analysis

- **Ad Performance** (`/dashboard/ad-performance`)
  - Ad-level performance metrics
  - Creative performance analysis

- **Keyword Performance** (`/dashboard/keyword-performance`)
  - Keyword-level analytics
  - Search term performance

- **Search Query Performance** (`/dashboard/search-query`)
  - Search query analysis and optimization
  - Query performance insights

#### Automation and Optimization Tools
- **Telegram Script Generator** (`/dashboard/telegram-script-generator`)
  - Generate Google Ads scripts with Telegram notifications
  - Custom notification templates
  - Bot integration setup

- **AirTable Script Generator** (`/dashboard/airtable-script`)
  - P&L reporting integration with AirTable
  - Data synchronization scripts
  - Custom reporting workflows

- **Performance Max Asset Analyzer** (`/dashboard/performance-max`)
  - Performance Max campaign analysis
  - Asset performance insights

- **Device Bid Adjuster** (`/dashboard/device-bid`)
  - Device-specific bid adjustments
  - Performance-based optimization

- **Keyword Conflict Detector** (`/dashboard/keyword-conflict`)
  - Identify keyword conflicts across campaigns
  - Optimization recommendations

- **Script Generator** (`/dashboard/script-generator`)
  - General Google Ads script generation
  - Custom automation scripts

## File Structure

```
gAds-supercharge/
├── src/
│   ├── components/
│   │   ├── ui/                    # Reusable UI components
│   │   │   ├── Button.tsx
│   │   │   ├── Card.tsx
│   │   │   ├── Input.tsx
│   │   │   └── Textarea.tsx
│   │   ├── tools/                 # Tool-specific components
│   │   │   ├── TelegramScriptGenerator.tsx
│   │   │   ├── AirtablePnlScriptGenerator.tsx
│   │   │   ├── GAdsBudgetUpdater.tsx
│   │   │   ├── CampaignPerformance.tsx
│   │   │   ├── AdPerformance.tsx
│   │   │   ├── KeywordPerformance.tsx
│   │   │   ├── BudgetMonitor.tsx
│   │   │   ├── DeviceBidAdjuster.tsx
│   │   │   ├── SearchQueryPerformance.tsx
│   │   │   ├── PerformanceMaxAssetAnalyzer.tsx
│   │   │   ├── KeywordConflictDetector.tsx
│   │   │   ├── ScriptGenerator.tsx
│   │   │   └── ToolPlaceholder.tsx
│   │   ├── dashboard/             # Dashboard components
│   │   │   ├── Sidebar.tsx
│   │   │   ├── Header.tsx
│   │   │   └── ClientArea.tsx
│   │   ├── Layout.tsx             # Main layout wrapper
│   │   ├── Hero.tsx               # Homepage hero section
│   │   ├── Services.tsx           # Services showcase
│   │   ├── Features.tsx           # Features section
│   │   ├── CallToAction.tsx       # CTA components
│   │   ├── Footer.tsx             # Site footer
│   │   ├── Login.tsx              # Login form
│   │   ├── Signup.tsx             # Registration form
│   │   ├── ForgotPassword.tsx     # Password recovery
│   │   ├── ThankYou.tsx           # Thank you page
│   │   ├── ProtectedRoute.tsx     # Route protection
│   │   └── PortfolioPage.tsx      # Portfolio showcase
│   ├── pages/
│   │   ├── ToolPage.tsx           # Dynamic tool router
│   │   ├── DashboardPage.tsx      # Main dashboard layout
│   │   ├── LoginPage.tsx          # Login page
│   │   ├── SignupPage.tsx         # Registration page
│   │   ├── ForgotPasswordPage.tsx # Password recovery page
│   │   ├── ThankYouPage.tsx       # Thank you page
│   │   ├── FeaturesPage.tsx       # Features showcase
│   │   ├── UserManagementPage.tsx # User management (admin)
│   │   ├── TelegramScriptGeneratorPage.tsx
│   │   ├── AirtablePnlScriptGeneratorPage.tsx
│   │   ├── GAdsBudgetUpdaterPage.tsx
│   │   ├── SearchAdsScriptGeneratorPage.tsx
│   │   └── LoginRedirect.tsx      # Login redirect handler
│   ├── contexts/
│   │   ├── AuthContext.tsx        # Authentication context
│   │   └── SidebarContext.tsx     # Sidebar state management
│   ├── hooks/
│   │   ├── useAuth.ts             # Authentication hook
│   │   ├── useAnalytics.ts        # Analytics tracking
│   │   └── useSidebar.ts          # Sidebar state hook
│   ├── utils/
│   │   ├── auth.ts                # Authentication utilities
│   │   └── analytics.ts           # Analytics utilities
│   ├── data/
│   │   ├── features.ts            # Features data
│   │   ├── navigation.ts          # Navigation configuration
│   │   └── services.ts            # Services data
│   ├── types/
│   │   └── index.ts               # TypeScript type definitions
│   ├── lib/
│   │   └── utils.ts               # Utility functions
│   ├── App.tsx                    # Main application component
│   ├── main.tsx                   # Application entry point
│   ├── index.css                  # Global styles
│   └── vite-env.d.ts              # Vite type definitions
├── public/
│   ├── root.csv                   # User authentication data
│   ├── favicon files              # Various favicon sizes
│   ├── logo.svg                   # Application logo
│   ├── robots.txt                 # SEO robots file
│   ├── sitemap.xml                # SEO sitemap
│   ├── site.webmanifest           # PWA manifest
│   └── blog.txt                   # Blog content
├── scripts/
│   ├── generate-favicon.js        # Favicon generation script
│   └── generate-sitemap.js        # Sitemap generation script
├── docs/
│   ├── README.md                  # Complete project documentation
│   ├── context.md                 # Project context and architecture
│   ├── deployment-context.md      # Deployment guide
│   └── netlify-optimization-plan.md # Performance optimization plan
├── package.json                   # Dependencies and scripts
├── package-lock.json              # Dependency lock file
├── vite.config.ts                 # Vite configuration
├── tailwind.config.js             # Tailwind CSS configuration
├── postcss.config.js              # PostCSS configuration
├── tsconfig.json                  # TypeScript configuration
├── tsconfig.app.json              # App-specific TypeScript config
├── tsconfig.node.json             # Node-specific TypeScript config
├── eslint.config.js               # ESLint configuration
├── components.json                # Shadcn/ui components config
├── netlify.toml                   # Netlify deployment config
├── .gitignore                     # Git ignore rules
└── index.html                     # Main HTML template
```

## Component Architecture

### Tool Page System
**Location**: `src/pages/ToolPage.tsx`
- Dynamic component loading based on URL path
- Centralized tool routing and management
- Consistent layout and navigation

### UI Component Library
**Location**: `src/components/ui/`
- Button, Input, Card, Textarea components
- Consistent design system
- Accessible and responsive components

### Tool Components
**Location**: `src/components/tools/`
- Modular tool implementations
- Shared utilities and helpers
- Form handling and validation

## Authentication Flow

1. **Login Process**: User enters credentials on `/login`
2. **CSV Validation**: Credentials checked against `/root.csv`
3. **Role Assignment**: User role determined from CSV data
4. **Session Management**: User data stored in localStorage
5. **Protected Access**: Dashboard routes require authentication
6. **Logout**: Clears session and redirects to login

## Routing Structure

```
/ (Homepage)
├── /login (Authentication)
├── /app (Login redirect)
├── /portfolio (Portfolio showcase)
└── /dashboard (Protected area)
    ├── / (Main dashboard)
    ├── /telegram-script-generator
    ├── /airtable-script
    ├── /gads-budget-updater
    ├── /campaign-performance
    ├── /ad-performance
    ├── /keyword-performance
    ├── /budget-monitor
    ├── /device-bid
    ├── /search-query
    ├── /performance-max
    ├── /keyword-conflict
    ├── /script-generator
    └── /settings
```

## Data Management

### User Authentication
- PostgreSQL-based user database with encrypted passwords
- JWT token authentication with session tracking
- User roles: admin, user
- Secure password hashing with bcryptjs
- Session management with browser/device tracking

### Tool Configuration
- Form state management with React Hook Form
- Local storage for user preferences
- History tracking for form inputs

### Performance Data
- Seasonal budget summary components
- Campaign performance tracking
- Historical data visualization

## Development Workflow

### Local Development
```bash
cd gads-services-mainpage
npm install
npm run dev
```

### Build Process
```bash
npm run build
```

### Code Quality
```bash
npm run lint
```

## Integration Points

### Google Ads API
- Script generation for campaign automation
- Budget management and optimization
- Performance data retrieval and analysis

### Telegram Integration
- Bot notifications for campaign events
- Custom message templates
- Real-time alerts and updates

### AirTable Integration
- P&L data synchronization
- Custom reporting workflows
- Data export and analysis

## Security Considerations

- CSV-based authentication (development/demo purposes)
- Role-based access control
- Protected routes and components
- Input validation and sanitization
- Secure data handling practices

## Performance Optimizations

- Vite for fast development and building
- Code splitting with React Router
- Lazy loading of tool components
- Optimized bundle size with tree shaking

## Recent Updates (2025-01-13)

### Bilingual Support Implementation
- **Database-driven translations**: Complete migration from static JSON to PostgreSQL-based content management
- **478+ translation keys**: Comprehensive Ukrainian translations for all major pages and tools
- **Real-time language switching**: Seamless language changes without page reload
- **SEO optimization**: Language-specific meta tags and structured data

### Pages Fully Translated
- **Homepage**: Hero, services, features, call-to-action sections
- **About Page**: Company information, team details, mission statement
- **Services Page**: Service descriptions, pricing, features
- **Contact Page**: Contact forms, information, support details
- **Careers Page**: Job listings, company culture, application process
- **Dashboard**: Tool descriptions, navigation, feature sections
- **Legal Pages**: Privacy policy, terms of service, cookies policy
- **Tool Pages**: All Google Ads tools with complete Ukrainian localization

### Tool Localization Fixes (Latest)

- **Budget Monitor**: Complete Ukrainian translation including "How It Works" section
- **Performance Max Asset Analyzer**: Full localization with form fields and descriptions
- **Device Bid Adjuster**: Complete Ukrainian interface with technical explanations
- **Form Components**: All input fields, buttons, and validation messages translated
- **Error Handling**: Fixed syntax errors and removed hardcoded English fallbacks
- **Code Quality**: Removed conditional logic for language detection, using database-driven approach

### Technical Improvements

- **Fixed footer duplication**: Resolved Layout component conflicts
- **Enhanced routing**: Proper SEO wrapper implementation
- **Database optimization**: Efficient content loading and caching
- **User experience**: Improved navigation and interface consistency
- **Syntax Fixes**: Resolved compilation errors in tool components
- **Translation System**: Migrated from hardcoded conditionals to database lookups

### Content Management

- **Structured translations**: Organized by page categories and sections
- **Tool-specific keys**: Added comprehensive translations for all Google Ads tools
- **Fallback system**: English defaults for missing translations
- **Easy maintenance**: Database-driven content updates without code changes
- **Scalable architecture**: Ready for additional languages
- **Efficient re-rendering**: React hooks with proper dependency management

### Authentication System Upgrade (Latest)

- **PostgreSQL Migration**: Migrated from CSV-based to PostgreSQL user authentication
- **Enhanced Security**: Implemented bcryptjs password hashing with salt rounds
- **JWT Tokens**: 24-hour expiration with secure session management
- **User Management**: Updated user credentials with stronger passwords
- **Session Tracking**: Browser and device information logging
- **API Integration**: Full frontend-backend authentication flow
- **Removed Dependencies**: Eliminated CSV file dependency for better security
