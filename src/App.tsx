import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';
import { SidebarProvider } from './contexts/SidebarContext';
import { LanguageProvider } from './contexts/LanguageContext';
import { useAnalytics } from './hooks/useAnalytics';
import Layout from './components/Layout';
import Hero from './components/Hero';
import Services from './components/Services';
import Features from './components/Features';
import CallToAction from './components/CallToAction';
import LoginPage from './pages/LoginPage';
import DashboardPage from './pages/DashboardPage';
import ProtectedRoute from './components/ProtectedRoute';
import ToolPage from './pages/ToolPage';
import LoginRedirect from './pages/LoginRedirect';
import ServicesPage from './pages/ServicesPage';
import PricingPage from './pages/PricingPage';
import ContactPage from './pages/ContactPage';
import FeaturesPage from './pages/FeaturesPage';
import PortfolioPage from './pages/PortfolioPage';
import AboutPage from './pages/AboutPage';
import CareersPage from './pages/CareersPage';
import PrivacyPolicy from './pages/PrivacyPolicy';
import TermsOfService from './pages/TermsOfService';
import CookiesPolicy from './pages/CookiesPolicy';
import ClientArea from './components/ClientArea';
import ScrollToTop from './components/ScrollToTop';
import SEO from './components/SEO';
import ErrorBoundary from './components/ErrorBoundary';

const HomePage = () => (
  <>
    <SEO 
      title="Professional Google Ads & Marketing Services"
      description="Expert Google Ads and marketing solutions to grow your business. Certified professionals with 10+ years of experience in digital advertising."
      path="/"
    />
    <Layout>
    {/* Hero Section */}
    <Hero />
    
    {/* Services Section */}
    <Services />
    
    {/* Call to Action Section */}
    <CallToAction />
    
    {/* Features Section */}
    <Features />
    </Layout>
  </>
);

// Define SEO props type
interface SEOPageProps {
  title: string;
  description: string;
  type?: 'website' | 'article';
  image?: string;
}

// Define Page component props
type PageProps = {
  children: React.ReactNode;
  seo: SEOPageProps;
};

// Page component wrapper for consistent layout and SEO
const Page: React.FC<PageProps> = ({ children, seo }) => {
  const location = useLocation();
  
  return (
    <ErrorBoundary>
      {seo && <SEO {...seo} path={location.pathname} />}
      <Layout>{children}</Layout>
    </ErrorBoundary>
  );
};

function App() {
  // Initialize analytics - wrapped in try/catch to prevent crashes
  try {
    useAnalytics();
  } catch (error) {
    console.error('Analytics initialization failed:', error);
  }

  return (
    <ErrorBoundary>
      <HelmetProvider>
        <LanguageProvider>
          <SidebarProvider>
            <Router>
      <ScrollToTop />
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/login" element={<LoginPage />} />
        <Route path="/app" element={<LoginRedirect />} />
        <Route path="/portfolio" element={
          <Page seo={{
            title: "Our Portfolio - Google Ads Case Studies & Success Stories",
            description: "Explore our portfolio of successful Google Ads campaigns and see how we've helped businesses achieve their marketing goals.",
          }}>
            <PortfolioPage />
          </Page>
        } />
        <Route path="/dashboard" element={
          <ProtectedRoute>
            <DashboardPage />
          </ProtectedRoute>
        }>
          <Route index element={<ClientArea />} />
          <Route path="telegram-script-generator" element={<ToolPage />} />
          <Route path="airtable-script" element={<ToolPage />} />
          <Route path="performance" element={<ToolPage />} />
          <Route path="gads-budget-updater" element={<ToolPage />} />
          <Route path="campaign-performance" element={<ToolPage />} />
          <Route path="ad-performance" element={<ToolPage />} />
          <Route path="keyword-performance" element={<ToolPage />} />
          <Route path="budget-monitor" element={<ToolPage />} />
          <Route path="device-bid" element={<ToolPage />} />
          <Route path="search-query" element={<ToolPage />} />
          <Route path="performance-max" element={<ToolPage />} />
          <Route path="keyword-conflict" element={<ToolPage />} />
          <Route path="script-generator" element={<ToolPage />} />
          <Route path="settings" element={<div className="py-8">Settings - Coming Soon</div>} />
        </Route>
        <Route path="/services" element={
          <Page seo={{
            title: "Our Services - Google Ads Management & Marketing Solutions",
            description: "Comprehensive Google Ads and digital marketing services to help your business grow. From campaign setup to optimization and reporting.",
          }}>
            <ServicesPage />
          </Page>
        } />
        <Route path="/pricing" element={
          <Page seo={{
            title: "Pricing Plans - Transparent Google Ads Management Pricing",
            description: "Affordable and transparent pricing for our Google Ads management services. Choose the plan that fits your business needs.",
          }}>
            <PricingPage />
          </Page>
        } />
        <Route path="/contact" element={
          <Page seo={{
            title: "Contact Us - Get in Touch with Our Google Ads Experts",
            description: "Have questions about our services? Contact our Google Ads experts today for a free consultation.",
          }}>
            <ContactPage />
          </Page>
        } />
        <Route path="/features" element={
          <Page seo={{
            title: "Features - Advanced Google Ads Management Tools",
            description: "Discover the powerful features of our Google Ads management services and tools designed to maximize your advertising ROI.",
          }}>
            <FeaturesPage />
          </Page>
        } />
        <Route path="/about" element={
          <Page seo={{
            title: "About Us - Google Ads Certified Experts",
            description: "Meet our team of Google Ads certified experts with years of experience in digital marketing and advertising.",
          }}>
            <AboutPage />
          </Page>
        } />
        <Route path="/careers" element={
          <Page seo={{
            title: "Careers - Join Our Google Ads Team",
            description: "Join our team of Google Ads professionals. Explore career opportunities in digital marketing and advertising.",
          }}>
            <CareersPage />
          </Page>
        } />
        <Route path="/privacy-policy" element={
          <Page seo={{
            title: "Privacy Policy - gAds Services",
            description: "Read our privacy policy to understand how we collect, use, and protect your personal information.",
          }}>
            <PrivacyPolicy />
          </Page>
        } />
        <Route path="/terms-of-service" element={
          <Page seo={{
            title: "Terms of Service - gAds",
            description: "Review our terms of service for using our Google Ads management and marketing services.",
          }}>
            <TermsOfService />
          </Page>
        } />
        <Route path="/cookies-policy" element={
          <Page seo={{
            title: "Cookies Policy - gAds",
            description: "Learn about how we use cookies and similar technologies on our website.",
          }}>
            <CookiesPolicy />
          </Page>
        } />
        {/* Catch-all redirect to home page */}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
            </Router>
          </SidebarProvider>
        </LanguageProvider>
      </HelmetProvider>
    </ErrorBoundary>
  );
}

export default App;