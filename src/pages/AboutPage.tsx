import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowLeft, BarChart2, Zap, Target, Award, Check } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

const AboutPage: React.FC = () => {
  const { t } = useLanguage();

  const stats = [
    { value: '10+', label: t('about.stats.experience') || 'Years Experience' },
    { value: '1000+', label: t('about.stats.campaigns') || 'Campaigns Managed' },
    { value: '50M+', label: t('about.stats.spend') || 'Ad Spend Optimized' },
    { value: '500+', label: t('about.stats.clients') || 'Happy Clients' }
  ];

  const values = [
    {
      icon: <BarChart2 className="w-6 h-6 text-blue-400" />,
      title: t('about.values.data.title') || 'Data-Driven',
      description: t('about.values.data.description') || 'Every decision backed by comprehensive analytics and performance metrics.'
    },
    {
      icon: <Zap className="w-6 h-6 text-blue-400" />,
      title: t('about.values.performance.title') || 'Performance-Focused',
      description: t('about.values.performance.description') || 'Relentless focus on maximizing ROI and campaign effectiveness.'
    },
    {
      icon: <Target className="w-6 h-6 text-blue-400" />,
      title: t('about.values.targeting.title') || 'Precision Targeting',
      description: t('about.values.targeting.description') || 'Advanced audience segmentation and targeting strategies.'
    },
    {
      icon: <Award className="w-6 h-6 text-blue-400" />,
      title: t('about.values.results.title') || 'Results-Oriented',
      description: t('about.values.results.description') || 'Committed to delivering measurable business growth and success.'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-900 text-white pt-24">
        <div className="container mx-auto px-4 py-12 max-w-6xl">
          <Link
            to="/"
            className="inline-flex items-center text-blue-400 hover:text-blue-300 mb-8 transition-colors group"
          >
            <ArrowLeft className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform" />
            {t('about.back_home') || 'Back to Home'}
          </Link>

          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-cyan-400">
              {t('about.title') || 'About gAds'}
            </h1>
            <p className="text-xl text-blue-100/70 max-w-3xl mx-auto">
              {t('about.subtitle') || 'Digital advertising experts with 10+ years of experience, transforming campaigns into high-performance growth engines.'}
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
            {stats.map((stat, index) => (
              <div key={index} className="bg-gray-800/50 p-6 rounded-xl text-center">
                <div className="text-3xl font-bold text-blue-400 mb-2">{stat.value}</div>
                <div className="text-blue-100/70">{stat.label}</div>
              </div>
            ))}
          </div>

          <div className="bg-gray-800/50 rounded-2xl p-8 md:p-12 mb-16">
            <h2 className="text-2xl font-bold mb-8">{t('about.mission.title') || 'Our Mission'}</h2>
            <p className="text-blue-100/70 text-lg mb-8">
              {t('about.mission.description') || 'We are on a mission to make digital advertising simpler, smarter, and more effective. With over a decade of experience managing thousands of campaigns, we have seen what works and what does not. Now, we are using that knowledge to build tools that help businesses of all sizes achieve better results with less effort.'}
            </p>
            <div className="space-y-4">
              <div className="flex items-start">
                <Check className="w-5 h-5 text-green-400 mt-1 mr-3 flex-shrink-0" />
                <p className="text-blue-100/70">{t('about.mission.point1') || '10+ years of hands-on experience in digital advertising'}</p>
              </div>
              <div className="flex items-start">
                <Check className="w-5 h-5 text-green-400 mt-1 mr-3 flex-shrink-0" />
                <p className="text-blue-100/70">{t('about.mission.point2') || 'Managed millions in ad spend across all major platforms'}</p>
              </div>
              <div className="flex items-start">
                <Check className="w-5 h-5 text-green-400 mt-1 mr-3 flex-shrink-0" />
                <p className="text-blue-100/70">{t('about.mission.point3') || 'Proven track record of delivering exceptional ROI'}</p>
              </div>
            </div>
          </div>

          <div className="mb-16">
            <h2 className="text-2xl font-bold mb-8">{t('about.values.title') || 'Our Values'}</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {values.map((value, index) => (
                <div key={index} className="bg-gray-800/50 p-6 rounded-xl">
                  <div className="w-12 h-12 flex items-center justify-center bg-blue-900/30 rounded-lg mb-4">
                    {value.icon}
                  </div>
                  <h3 className="text-xl font-semibold mb-2">{value.title}</h3>
                  <p className="text-blue-100/70">{value.description}</p>
                </div>
              ))}
            </div>
          </div>

          <div className="bg-gradient-to-r from-blue-900/30 to-cyan-900/30 rounded-2xl p-8 md:p-12 text-center">
            <h2 className="text-2xl md:text-3xl font-bold mb-4">{t('about.cta.title') || 'Ready to Transform Your Advertising?'}</h2>
            <p className="text-blue-100/70 text-lg mb-8 max-w-2xl mx-auto">
              {t('about.cta.subtitle') || 'Join hundreds of businesses that have already supercharged their advertising performance with our expert tools and strategies.'}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/contact"
                className="px-8 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 text-white font-medium rounded-lg hover:opacity-90 transition-opacity"
              >
                {t('about.cta.contact') || 'Get in Touch'}
              </Link>
              <Link
                to="/features"
                className="px-8 py-3 bg-transparent border-2 border-blue-400 text-blue-400 font-medium rounded-lg hover:bg-blue-400/10 transition-colors"
              >
                {t('about.cta.features') || 'Explore Features'}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AboutPage;
