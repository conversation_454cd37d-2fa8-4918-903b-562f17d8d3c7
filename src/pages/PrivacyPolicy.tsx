import React from 'react';
// import Layout from '../components/Layout'; 
import LegalLayout from '../components/legal/LegalLayout';

const PrivacyPolicyContent = () => {
  return (
    <LegalLayout 
      title="Privacy Policy"
      lastUpdated="May 20, 2025"
    >
      <h2>1. Introduction</h2>
      <p>
        Welcome to our Privacy Policy. Your privacy is important to us, and we are committed to protecting your personal data.
      </p>

      <h2>2. Information We Collect</h2>
      <p>We may collect and process the following data about you:</p>
      <ul>
        <li>Personal identification information (Name, email address, phone number, etc.)</li>
        <li>Usage data</li>
        <li>Cookies and usage data</li>
        <li>Advertising and communication data</li>
      </ul>

      <h2>3. How We Use Your Information</h2>
      <p>We use the information we collect in various ways, including to:</p>
      <ul>
        <li>Provide, operate, and maintain our services</li>
        <li>Improve, personalize, and expand our services</li>
        <li>Understand and analyze how you use our services</li>
        <li>Develop new products, services, features, and functionality</li>
        <li>Communicate with you, either directly or through one of our partners</li>
      </ul>

      <h2>4. Data Security</h2>
      <p>
        We implement appropriate technical and organizational measures to protect your personal data against unauthorized or unlawful processing, accidental loss, destruction, or damage.
      </p>

      <h2>5. Your Data Protection Rights</h2>
      <p>You have the right to:</p>
      <ul>
        <li>Request access to your personal data</li>
        <li>Request correction of your personal data</li>
        <li>Request erasure of your personal data</li>
        <li>Object to processing of your personal data</li>
        <li>Request restriction of processing your personal data</li>
        <li>Request transfer of your personal data</li>
        <li>Withdraw your consent</li>
      </ul>

      <h2>6. Changes to This Privacy Policy</h2>
      <p>
        We may update our Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page.
      </p>

      <h2>7. Contact Us</h2>
      <p>
        If you have any questions about this Privacy Policy, please contact us at:
        <br />
        Email: <EMAIL>
      </p>
    </LegalLayout>
  );
};

const PrivacyPolicy: React.FC = () => {
  return (
      <PrivacyPolicyContent />
  );
};

export default PrivacyPolicy;
