import React from 'react';
// import Layout from '../components/Layout'; 
import LegalLayout from '../components/legal/LegalLayout';

const TermsOfServiceContent = () => {
  return (
    <LegalLayout 
      title="Terms of Service"
      lastUpdated="May 20, 2025"
    >
      <h2>1. Acceptance of Terms</h2>
      <p>
        By accessing or using our services, you agree to be bound by these Terms of Service and all applicable laws and regulations.
      </p>

      <h2>2. Use License</h2>
      <p>
        Permission is granted to temporarily download one copy of the materials on our website for personal, non-commercial transitory viewing only.
      </p>

      <h2>3. User Responsibilities</h2>
      <p>As a user of our services, you agree to:</p>
      <ul>
        <li>Provide accurate and complete information</li>
        <li>Maintain the security of your account credentials</li>
        <li>Comply with all applicable laws and regulations</li>
        <li>Not engage in any activity that interferes with or disrupts our services</li>
      </ul>

      <h2>4. Intellectual Property</h2>
      <p>
        All content included on this site, such as text, graphics, logos, and software, is the property of our company or its content suppliers and protected by copyright laws.
      </p>

      <h2>5. Limitation of Liability</h2>
      <p>
        In no event shall we be liable for any damages arising out of the use or inability to use our services, even if we have been notified of the possibility of such damage.
      </p>

      <h2>6. Changes to Terms</h2>
      <p>
        We reserve the right to modify these terms at any time. Your continued use of our services after any changes constitutes your acceptance of the new terms.
      </p>

      <h2>7. Governing Law</h2>
      <p>
        These terms shall be governed by and construed in accordance with the laws of [Your Country], without regard to its conflict of law provisions.
      </p>

      <h2>8. Contact Information</h2>
      <p>
        If you have any questions about these Terms of Service, please contact us at:
        <br />
        Email: <EMAIL>
      </p>
    </LegalLayout>
  );
};

const TermsOfService: React.FC = () => {
  return (
      <TermsOfServiceContent />
  );
};

export default TermsOfService;
