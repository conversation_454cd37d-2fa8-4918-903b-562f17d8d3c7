import React from 'react';
import Layout from '../components/Layout';
import { Link } from 'react-router-dom';
import { ArrowLeft, Code, Zap, Users, MessageSquare, BarChart2 } from 'lucide-react';

const CareersPage: React.FC = () => {
  const positions = [
    {
      icon: <Code className="w-6 h-6 text-blue-400" />,
      title: "Developers",
      description: "Build the future of advertising tools with cutting-edge tech."
    },
    {
      icon: <Zap className="w-6 h-6 text-blue-400" />,
      title: "Testers",
      description: "Help us ensure our products are flawless and user-friendly."
    },
    {
      icon: <MessageSquare className="w-6 h-6 text-blue-400" />,
      title: "Influencers",
      description: "Spread the word about our innovative advertising solutions."
    },
    {
      icon: <Users className="w-6 h-6 text-blue-400" />,
      title: "Sales Managers",
      description: "Connect businesses with tools that transform their advertising."
    }
  ];

  return (
    <Layout>
      <div className="min-h-screen bg-gray-900 text-white pt-24">
        <div className="container mx-auto px-4 py-12 max-w-4xl">
          <Link 
            to="/" 
            className="inline-flex items-center text-blue-400 hover:text-blue-300 mb-8 transition-colors group"
          >
            <ArrowLeft className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform" />
            Back to Home
          </Link>
          
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-cyan-400">
              Join Our Team
            </h1>
            <p className="text-xl text-blue-100/70">
              We're looking for passionate individuals to help us revolutionize advertising tools.
            </p>
          </div>

          <div className="bg-gray-800/50 rounded-2xl p-8 border border-gray-700/50 mb-12">
            <h2 className="text-2xl font-bold mb-6 flex items-center">
              <BarChart2 className="w-6 h-6 mr-3 text-blue-400" />
              Open Positions
            </h2>
            
            <div className="grid gap-6">
              {positions.map((position, index) => (
                <div key={index} className="flex items-start space-x-4 p-4 bg-gray-700/30 rounded-lg hover:bg-gray-700/50 transition-colors">
                  <div className="flex-shrink-0 mt-1">
                    {position.icon}
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold">{position.title}</h3>
                    <p className="text-blue-100/70">{position.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="text-center">
            <p className="text-blue-100/70 mb-6">
              Ready to join our team? Get in touch with us to learn more about current opportunities.
            </p>
            <Link 
              to="/contact" 
              className="inline-block bg-gradient-to-r from-blue-500 to-cyan-500 text-white font-medium px-6 py-3 rounded-lg hover:opacity-90 transition-opacity"
            >
              Contact Us
            </Link>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default CareersPage;
