import React from 'react';
// import Layout from '../components/Layout'; 
import { Link } from 'react-router-dom';
import { ArrowLeft, BarChart2, Zap, Layers, Bell, Shield, Code } from 'lucide-react';

const FeaturesPage: React.FC = () => {
  const features = [
    {
      icon: <BarChart2 className="w-8 h-8 text-blue-400" />,
      title: "Advanced Analytics",
      description: "Get deep insights into your ad performance with our comprehensive analytics dashboard.",
      link: "/#analytics"
    },
    {
      icon: <Zap className="w-8 h-8 text-blue-400" />,
      title: "Automated Optimization",
      description: "Let our AI optimize your campaigns in real-time for maximum performance.",
      link: "/#automation"
    },
    {
      icon: <Layers className="w-8 h-8 text-blue-400" />,
      title: "Multi-Platform Support",
      description: "Manage all your ad campaigns from different platforms in one place.",
      link: "/#integrations"
    },
    {
      icon: <Bell className="w-8 h-8 text-blue-400" />,
      title: "Real-time Alerts",
      description: "Get instant notifications about important changes in your ad performance.",
      link: "/#alerts"
    },
    {
      icon: <Shield className="w-8 h-8 text-blue-400" />,
      title: "Security First",
      description: "Your data is encrypted and protected with enterprise-grade security measures.",
      link: "/#security"
    },
    {
      icon: <Code className="w-8 h-8 text-blue-400" />,
      title: "Developer Friendly",
      description: "Powerful API and webhooks for custom integrations and automation.",
      link: "/#api"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-900 text-white pt-24">
      <div className="container mx-auto px-4 py-12 max-w-6xl">
        <div className="max-w-3xl mx-auto text-center mb-16">
          <Link 
            to="/" 
            className="inline-flex items-center text-blue-400 hover:text-blue-300 mb-8 transition-colors group"
          >
            <ArrowLeft className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform" />
            Back to Home
          </Link>
          <h1 className="text-4xl md:text-5xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-cyan-400">
            Powerful Features for Your Ad Campaigns
          </h1>
          <p className="text-xl text-blue-100/70">
            Discover how our platform can transform your advertising strategy with cutting-edge tools and automation.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {features.map((feature, index) => (
            <div 
              key={index}
              className="bg-gray-800/50 hover:bg-gray-800/70 rounded-2xl p-6 transition-all hover:-translate-y-1 border border-gray-700/50 hover:border-blue-400/30"
            >
              <div className="w-12 h-12 flex items-center justify-center bg-blue-900/30 rounded-lg mb-4">
                {feature.icon}
              </div>
              <h3 className="text-xl font-semibold mb-2">
                <Link to={feature.link} className="hover:text-blue-300 transition-colors">
                  {feature.title}
                </Link>
              </h3>
              <p className="text-blue-100/70">{feature.description}</p>
            </div>
          ))}
        </div>

        <div className="bg-gradient-to-r from-blue-900/30 to-cyan-900/30 rounded-2xl p-8 md:p-12 border border-blue-400/20">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-2xl md:text-3xl font-bold mb-4">Ready to boost your ad performance?</h2>
            <p className="text-blue-100/70 text-lg mb-8">
              Join thousands of marketers who trust our platform to optimize their advertising campaigns.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                to="/pricing" 
                className="px-8 py-3 bg-gradient-to-r from-blue-500 to-cyan-500 text-white font-medium rounded-lg hover:opacity-90 transition-opacity"
              >
                View Pricing
              </Link>
              <Link 
                to="/contact" 
                className="px-8 py-3 bg-transparent border-2 border-blue-400 text-blue-400 font-medium rounded-lg hover:bg-blue-400/10 transition-colors"
              >
                Contact Sales
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FeaturesPage;
