import React from 'react';
// import Layout from '../components/Layout'; 
import LegalLayout from '../components/legal/LegalLayout';

const CookiesPolicyContent = () => {
  return (
    <LegalLayout 
      title="Cookies Policy"
      lastUpdated="May 20, 2025"
    >
      <h2>1. What Are Cookies</h2>
      <p>
        Cookies are small text files that are placed on your computer by websites that you visit. They are widely used in order to make websites work, or work more efficiently, as well as to provide information to the owners of the site.
      </p>

      <h2>2. How We Use Cookies</h2>
      <p>We use cookies for the following purposes:</p>
      <ul>
        <li><strong>Essential Cookies:</strong> Necessary for the website to function properly</li>
        <li><strong>Performance Cookies:</strong> Help us understand how visitors interact with our website</li>
        <li><strong>Functionality Cookies:</strong> Enable enhanced functionality and personalization</li>
        <li><strong>Targeting Cookies:</strong> Used to deliver relevant content and advertisements</li>
      </ul>

      <h2>3. Third-Party Cookies</h2>
      <p>
        We may also use various third-party cookies to report usage statistics of the service, deliver advertisements on and through the service, and so on.
      </p>

      <h2>4. Your Choices Regarding Cookies</h2>
      <p>
        You can set or amend your web browser controls to accept or refuse cookies. If you choose to reject cookies, you may still use our website though your access to some functionality and areas of our website may be restricted.
      </p>

      <h2>5. Types of Cookies We Use</h2>
      <div className="overflow-x-auto">
        <table className="min-w-full border border-white/10">
          <thead>
            <tr className="bg-white/5">
              <th className="px-4 py-2 text-left">Cookie Name</th>
              <th className="px-4 py-2 text-left">Purpose</th>
              <th className="px-4 py-2 text-left">Duration</th>
            </tr>
          </thead>
          <tbody>
            <tr className="border-t border-white/10">
              <td className="px-4 py-3">session_id</td>
              <td className="px-4 py-3">Maintain your session</td>
              <td className="px-4 py-3">Session</td>
            </tr>
            <tr className="border-t border-white/10">
              <td className="px-4 py-3">_ga</td>
              <td className="px-4 py-3">Google Analytics</td>
              <td className="px-4 py-3">2 years</td>
            </tr>
            <tr className="border-t border-white/10">
              <td className="px-4 py-3">_gid</td>
              <td className="px-4 py-3">Google Analytics</td>
              <td className="px-4 py-3">24 hours</td>
            </tr>
          </tbody>
        </table>
      </div>

      <h2>6. Changes to This Policy</h2>
      <p>
        We may update our Cookies Policy from time to time. We will notify you of any changes by posting the new Cookies Policy on this page.
      </p>

      <h2>7. Contact Us</h2>
      <p>
        If you have any questions about our use of cookies, please contact us at:
        <br />
        Email: <EMAIL>
      </p>
    </LegalLayout>
  );
};

const CookiesPolicy: React.FC = () => {
  return (
    <CookiesPolicyContent />
  );
};

export default CookiesPolicy;
