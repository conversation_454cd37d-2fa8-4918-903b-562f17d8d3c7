import React, { useState, useEffect } from 'react';
import { <PERSON>ger<PERSON>, Bot } from 'lucide-react';
import { TooltipProvider } from '@/components/ui/tooltip';

import ToolPageLayout from '../ui/shared/ToolPageLayout';
import FormSection from '../ui/shared/FormSection';
import FormItem from '../ui/shared/FormItem';
import StyledInput from '../ui/shared/StyledInput';
import StyledButton from '../ui/shared/StyledButton';
import ScriptDisplay from '../ui/shared/ScriptDisplay';
import NotificationMessage from '../ui/shared/NotificationMessage';

const LOCAL_STORAGE_PREFIX = 'keywordConflictDetector_';

const GOOGLE_ADS_DATE_RANGES_KCD = [
  { value: 'LAST_7_DAYS', label: 'Last 7 Days' },
  { value: 'LAST_14_DAYS', label: 'Last 14 Days' },
  { value: 'LAST_30_DAYS', label: 'Last 30 Days' },
  { value: 'LAST_90_DAYS', label: 'Last 90 Days' },
];

const KeywordConflictDetector: React.FC = () => {
  // Form inputs
  const [accountId, setAccountId] = useState('');
  const [dateRange, setDateRange] = useState('LAST_30_DAYS');

  // Telegram
  const [useTelegram, setUseTelegram] = useState(false);
  const [showTelegramSettings, setShowTelegramSettings] = useState(false); // To control visibility
  const [telegramBotToken, setTelegramBotToken] = useState('');
  const [telegramChatId, setTelegramChatId] = useState('');

  // UI State
  const [generatedScript, setGeneratedScript] = useState('');
  const [showResult, setShowResult] = useState(false);
  const [message, setMessage] = useState<{text: string, type: 'error' | 'success' | 'info'} | null>(null);

  useEffect(() => {
    const fields = [
      { key: 'accountId', setter: setAccountId, type: 'string' },
      { key: 'dateRange', setter: setDateRange, type: 'string' },
      { key: 'useTelegram', setter: setUseTelegram, type: 'boolean' },
      { key: 'telegramBotToken', setter: setTelegramBotToken, type: 'string' },
      { key: 'telegramChatId', setter: setTelegramChatId, type: 'string' },
    ];

    fields.forEach(field => {
      const savedValue = localStorage.getItem(`${LOCAL_STORAGE_PREFIX}${field.key}`);
      if (savedValue !== null) {
        if (field.type === 'boolean') {
          const booleanValue = savedValue === 'true';
          (field.setter as React.Dispatch<React.SetStateAction<boolean>>)(booleanValue);
          if (field.key === 'useTelegram') {
            setShowTelegramSettings(booleanValue);
          }
        } else {
          (field.setter as React.Dispatch<React.SetStateAction<string>>)(savedValue);
        }
      }
    });
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setMessage(null);
    const { name, value, type } = e.target;
    const valToSet = type === 'checkbox' ? (e.target as HTMLInputElement).checked : value;

    const fieldSetters: Record<string,
      React.Dispatch<React.SetStateAction<string>> | React.Dispatch<React.SetStateAction<boolean>>
    > = {
      accountId: setAccountId,
      dateRange: setDateRange,
      useTelegram: setUseTelegram,
      telegramBotToken: setTelegramBotToken,
      telegramChatId: setTelegramChatId,
    };

    if (fieldSetters[name]) {
      const setter = fieldSetters[name];
      if (type === 'checkbox'){
        const booleanValue = valToSet as boolean;
        (setter as React.Dispatch<React.SetStateAction<boolean>>)(booleanValue);
        if (name === 'useTelegram') {
          setShowTelegramSettings(booleanValue);
          if (!booleanValue) {
            // Clear Telegram fields if disabled
            setTelegramBotToken('');
            setTelegramChatId('');
            localStorage.removeItem(`${LOCAL_STORAGE_PREFIX}telegramBotToken`);
            localStorage.removeItem(`${LOCAL_STORAGE_PREFIX}telegramChatId`);
          }
        }
      } else {
        (setter as React.Dispatch<React.SetStateAction<string>>)(valToSet as string);
      }
      localStorage.setItem(`${LOCAL_STORAGE_PREFIX}${name}`, String(valToSet));
    }
  };

  const handleGenerateScript = () => {
    setMessage(null);

    // Validate Telegram credentials format
    if (useTelegram) {
      if (!telegramBotToken.trim() || !telegramChatId.trim()) {
        setMessage({ text: 'Please enter both Telegram Bot Token and Chat ID for Telegram notifications.', type: 'error' });
        return;
      }
      
      // Basic format validation for bot token (numbers:letters-and-numbers)
      if (!/^\d+:[A-Za-z0-9_-]{35}$/.test(telegramBotToken.trim())) {
        setMessage({ text: 'Invalid Telegram Bot Token format. Expected format: *********:ABCdefGhIJKlmNoPQRsTuVwXyZ', type: 'error' });
        return;
      }
      
      // Basic format validation for chat ID (numeric value)
      if (!/^-?\d+$/.test(telegramChatId.trim())) {
        setMessage({ text: 'Invalid Telegram Chat ID format. Should be numeric (e.g., ********* or -*********)', type: 'error' });
        return;
      }
    }

    const script = generateKeywordConflictDetectorScript(
      accountId,
      dateRange,
      useTelegram,
      telegramBotToken,
      telegramChatId
    );
    setGeneratedScript(script);
    setShowResult(true);
    setTimeout(() => {
      document.getElementById('resultSectionKCD')?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  };

  const generateKeywordConflictDetectorScript = (
    accId: string, 
    dateRange: string, 
    useTelegram: boolean, 
    telegramBotToken: string, 
    telegramChatId: string
  ): string => {
    const uniquePrefix = 'kw_' + Math.random().toString(36).substring(2, 8) + '_';
    let script = '';

    if (useTelegram) {
      script += `
/**
 * Sends a message to a Telegram chat using a bot
 * @param {string} message - The message to send (supports HTML formatting)
 */
function sendTelegramMessage(message) {
  var telegramBotToken = ${JSON.stringify(telegramBotToken)};
  var telegramChatId = ${JSON.stringify(telegramChatId)};
  
  if (!telegramBotToken || !telegramChatId) {
    Logger.log('Telegram Bot Token or Chat ID is missing. Skipping Telegram notification.');
    return;
  }
  
  var payload = {
    'chat_id': telegramChatId,
    'text': message,
    'parse_mode': 'HTML',
    'disable_web_page_preview': true
  };
  
  var options = {
    'method': 'post',
    'contentType': 'application/json',
    'payload': JSON.stringify(payload),
    'muteHttpExceptions': true
  };
  
  try {
    UrlFetchApp.fetch('https://api.telegram.org/bot' + telegramBotToken + '/sendMessage', options);
    Logger.log('Telegram notification sent successfully');
  } catch (e) {
    Logger.log('Telegram Error: ' + e.toString() + ' Payload: ' + JSON.stringify(payload));
  }
}
`;
    }

    script += `
/**
 * Main function to find duplicate keywords across campaigns and ad groups
 */
function main() {
  // Configuration
  const ${uniquePrefix}accountId = '${accId}'; // User-provided Account ID
  const ${uniquePrefix}dateRange = '${dateRange}';

  Logger.log('Starting Keyword Conflict Detector');
  Logger.log('User-provided Account ID: ' + ${uniquePrefix}accountId);
  Logger.log('Date Range: ' + ${uniquePrefix}dateRange);

  if (${uniquePrefix}accountId !== 'MCC') {
    // A specific account ID is provided by the user
    if (typeof AdsManagerApp !== 'undefined') {
      // Script is likely running in an MCC environment, try to select the child account
      try {
        var ${uniquePrefix}accountIterator = AdsManagerApp.accounts().withIds([${uniquePrefix}accountId.replace(/-/g, '')]).get();
        if (${uniquePrefix}accountIterator.hasNext()) {
          var ${uniquePrefix}childAccount = ${uniquePrefix}accountIterator.next();
          AdsManagerApp.select(${uniquePrefix}childAccount);
          Logger.log('Successfully switched to account: ' + AdsApp.currentAccount().getName() + ' (' + AdsApp.currentAccount().getCustomerId() + ')');
        } else {
          Logger.log('Error: Account ID ' + ${uniquePrefix}accountId + ' not found under this MCC or MCC lacks permission.');
          return; // Stop if account not found/selectable
        }
      } catch (e) {
        Logger.log('Error selecting account ' + ${uniquePrefix}accountId + ' via AdsManagerApp: ' + e.toString());
        return; // Stop on error
      }
    } else {
      // AdsManagerApp is not defined. We are likely running in a single account.
      // Verify if the current account matches the provided accountId.
      var ${uniquePrefix}currentActualAccountId = AdsApp.currentAccount().getCustomerId();
      if (${uniquePrefix}currentActualAccountId.replace(/-/g, '') !== ${uniquePrefix}accountId.replace(/-/g, '')) {
        Logger.log('Warning: Script is running in account ' + ${uniquePrefix}currentActualAccountId + 
                   ', but the user-provided account ID was ' + ${uniquePrefix}accountId + '. ' +
                   'Proceeding with the current account (' + ${uniquePrefix}currentActualAccountId + ').');
      } else {
        Logger.log('Running in account: ' + AdsApp.currentAccount().getName() + ' (' + ${uniquePrefix}currentActualAccountId + ') as specified.');
      }
      // No selection needed, AdsApp already points to the current account.
    }
  } else {
    // User-provided accountId is 'MCC'
    if (typeof AdsManagerApp === 'undefined') {
      Logger.log('Error: "MCC" was specified for account ID, but script is not running in an MCC environment.');
      return;
    }
    Logger.log('MCC mode selected. This script is designed for single account analysis. ' +
               'Please provide a specific account ID or run this script within the target account. ' +
               'Alternatively, modify the script to iterate through accounts under the MCC.');
    Logger.log('Current MCC: ' + AdsApp.currentAccount().getName() + ' (' + AdsApp.currentAccount().getCustomerId() + ')');
    return; // Stop, as running conflict detection on an MCC itself is not standard for this script's design.
  }

  Logger.log('Proceeding with keyword conflict detection for: ' + AdsApp.currentAccount().getName() + ' (' + AdsApp.currentAccount().getCustomerId() + ')');

  // Fetch keywords
  const ${uniquePrefix}keywordIterator = AdsApp.keywords()
    .forDateRange(${uniquePrefix}dateRange)
    .withCondition('Status = ENABLED') // Simpler condition
    .get();
  
  // Create a map to store keywords and their instances
  const ${uniquePrefix}keywordMap = {};
  let ${uniquePrefix}keywordCount = 0;
  
  // Process each keyword
  while (${uniquePrefix}keywordIterator.hasNext()) {
    const ${uniquePrefix}keyword = ${uniquePrefix}keywordIterator.next();
    
    // Get stats for the date range first to filter by performance
    const ${uniquePrefix}stats = ${uniquePrefix}keyword.getStatsFor(${uniquePrefix}dateRange);
    const ${uniquePrefix}impressions = ${uniquePrefix}stats.getImpressions() || 0;
    const ${uniquePrefix}clicks = ${uniquePrefix}stats.getClicks() || 0; // Get clicks as well

    // Filter for performance in-script
    if (!(${uniquePrefix}clicks > 0 || ${uniquePrefix}impressions > 0)) {
      continue; // Skip if no clicks and no impressions
    }

    const ${uniquePrefix}keywordText = ${uniquePrefix}keyword.getText().toLowerCase();
    const ${uniquePrefix}matchType = ${uniquePrefix}keyword.getMatchType();
    const ${uniquePrefix}adGroup = ${uniquePrefix}keyword.getAdGroup();
    const ${uniquePrefix}campaign = ${uniquePrefix}adGroup.getCampaign();
    
    // Skip if any of these are null/undefined (should be rare after performance filter)
    if (!${uniquePrefix}keywordText || !${uniquePrefix}matchType || !${uniquePrefix}adGroup || !${uniquePrefix}campaign) {
      continue;
    }
    
    // CPC is fetched after confirming the keyword is relevant
    const ${uniquePrefix}cpc = ${uniquePrefix}stats.getAverageCpc() || 0;
    
    // Create a unique key combining keyword text and match type
    const ${uniquePrefix}key = ${uniquePrefix}keywordText + '|' + ${uniquePrefix}matchType;
    
    // Add to our map
    if (!${uniquePrefix}keywordMap[${uniquePrefix}key]) {
      ${uniquePrefix}keywordMap[${uniquePrefix}key] = [];
      ${uniquePrefix}keywordCount++;
    }
    
    ${uniquePrefix}keywordMap[${uniquePrefix}key].push({
      keyword: ${uniquePrefix}keywordText,
      matchType: ${uniquePrefix}matchType,
      campaign: ${uniquePrefix}campaign.getName(),
      campaignId: ${uniquePrefix}campaign.getId(),
      adGroup: ${uniquePrefix}adGroup.getName(),
      adGroupId: ${uniquePrefix}adGroup.getId(),
      cpc: ${uniquePrefix}cpc,
      impressions: ${uniquePrefix}impressions
    });
  }
  
  // Find keywords with multiple instances (conflicts)
  const ${uniquePrefix}conflicts = [];
  for (var ${uniquePrefix}keyword in ${uniquePrefix}keywordMap) {
    if (${uniquePrefix}keywordMap[${uniquePrefix}keyword].length > 1) {
      ${uniquePrefix}conflicts.push({
        keyword: ${uniquePrefix}keyword,
        instances: ${uniquePrefix}keywordMap[${uniquePrefix}keyword]
      });
    }
  }
  
  // Sort conflicts by CPC difference (highest to lowest)
  ${uniquePrefix}conflicts.sort(function(a, b) {
    var ${uniquePrefix}maxCpcA = Math.max.apply(null, a.instances.map(function(instance) { return instance.cpc; }));
    var ${uniquePrefix}minCpcA = Math.min.apply(null, a.instances.map(function(instance) { return instance.cpc > 0 ? instance.cpc : ${uniquePrefix}maxCpcA; }));
    var ${uniquePrefix}diffA = ${uniquePrefix}maxCpcA - ${uniquePrefix}minCpcA;
    
    var ${uniquePrefix}maxCpcB = Math.max.apply(null, b.instances.map(function(instance) { return instance.cpc; }));
    var ${uniquePrefix}minCpcB = Math.min.apply(null, b.instances.map(function(instance) { return instance.cpc > 0 ? instance.cpc : ${uniquePrefix}maxCpcB; }));
    var ${uniquePrefix}diffB = ${uniquePrefix}maxCpcB - ${uniquePrefix}minCpcB;
    
    return ${uniquePrefix}diffB - ${uniquePrefix}diffA;
  });
  
  // Log conflicts
  Logger.log('Found ' + ${uniquePrefix}conflicts.length + ' keyword conflicts');
  
  // Log detailed information about conflicts
  ${uniquePrefix}conflicts.forEach(function(${uniquePrefix}conflict) {
    Logger.log('Keyword: ' + ${uniquePrefix}conflict.keyword);
    
    // Sort instances by CPC (highest to lowest)
    ${uniquePrefix}conflict.instances.sort(function(a, b) {
      return b.cpc - a.cpc;
    });
    
    ${uniquePrefix}conflict.instances.forEach(function(${uniquePrefix}instance) {
      Logger.log('  Campaign: ' + ${uniquePrefix}instance.campaign + 
                ', AdGroup: ' + ${uniquePrefix}instance.adGroup + 
                ', Match Type: ' + ${uniquePrefix}instance.matchType + 
                ', CPC: $' + ${uniquePrefix}instance.cpc.toFixed(2) +
                ', Impressions: ' + ${uniquePrefix}instance.impressions);
    });
  });
  
  // Generate report
  if (${uniquePrefix}conflicts.length > 0) {
    let ${uniquePrefix}report = '=== Keyword Conflict Report ===\\n\\n';
    ${uniquePrefix}report += 'Date Range: ' + ${uniquePrefix}dateRange + '\\n';
    ${uniquePrefix}report += 'Total Conflicts: ' + ${uniquePrefix}conflicts.length + '\\n\\n';
    
    // Add top 10 conflicts to the report
    const ${uniquePrefix}topConflicts = ${uniquePrefix}conflicts.slice(0, Math.min(10, ${uniquePrefix}conflicts.length));
    ${uniquePrefix}report += 'Top Keyword Conflicts:\\n\\n';
    
    ${uniquePrefix}topConflicts.forEach(function(${uniquePrefix}conflict, ${uniquePrefix}index) {
      // Calculate CPC range
      const ${uniquePrefix}maxCpc = Math.max.apply(null, ${uniquePrefix}conflict.instances.map(function(instance) { return instance.cpc; }));
      const ${uniquePrefix}minCpc = Math.min.apply(null, ${uniquePrefix}conflict.instances.map(function(instance) { return instance.cpc > 0 ? instance.cpc : ${uniquePrefix}maxCpc; }));
      const ${uniquePrefix}cpcDiff = ${uniquePrefix}maxCpc - ${uniquePrefix}minCpc;
      
      ${uniquePrefix}report += (${uniquePrefix}index + 1) + '. "' + ${uniquePrefix}conflict.keyword + '"\\n';
      ${uniquePrefix}report += '   CPC Range: $' + ${uniquePrefix}minCpc.toFixed(2) + ' - $' + ${uniquePrefix}maxCpc.toFixed(2) + ' (Diff: $' + ${uniquePrefix}cpcDiff.toFixed(2) + ')\\n';
      
      // Sort instances by CPC (highest to lowest)
      ${uniquePrefix}conflict.instances.sort(function(a, b) {
        return b.cpc - a.cpc;
      });
      
      // Show up to 3 instances per keyword
      const ${uniquePrefix}instancesToShow = ${uniquePrefix}conflict.instances.slice(0, Math.min(3, ${uniquePrefix}conflict.instances.length));
      ${uniquePrefix}instancesToShow.forEach(function(${uniquePrefix}instance) {
        ${uniquePrefix}report += '   • ' + ${uniquePrefix}instance.campaign + ' / ' + ${uniquePrefix}instance.adGroup + '\\n';
        ${uniquePrefix}report += '     ' + ${uniquePrefix}instance.matchType + ', CPC: $' + ${uniquePrefix}instance.cpc.toFixed(2) + '\\n';
      });
      
      if (${uniquePrefix}conflict.instances.length > 3) {
        ${uniquePrefix}report += '   ...and ' + (${uniquePrefix}conflict.instances.length - 3) + ' more instances\\n';
      }
      
      ${uniquePrefix}report += '\\n';
    });
    
    if (${uniquePrefix}conflicts.length > 10) {
      ${uniquePrefix}report += '...and ' + (${uniquePrefix}conflicts.length - 10) + ' more conflicts\\n\\n';
    }
    
    ${uniquePrefix}report += 'Recommendation: Review these conflicts and consider removing duplicate keywords or adjusting match types to prevent keyword cannibalization.';
    
    // Log the full report
    Logger.log('\\n' + ${uniquePrefix}report);
    
    // Send Telegram notification if enabled
    ${useTelegram ? `
    // Get account info for Telegram message
    var ${uniquePrefix}tgAccountInfo = AdsApp.currentAccount();
    var ${uniquePrefix}tgAccountName = ${uniquePrefix}tgAccountInfo.getName();
    var ${uniquePrefix}tgAccountId = ${uniquePrefix}tgAccountInfo.getCustomerId();
    var ${uniquePrefix}formattedTime = Utilities.formatDate(new Date(), AdsApp.currentAccount().getTimeZone(), 'yyyy-MM-dd HH:mm:ss z');
    
    // Create a formatted Telegram message with emojis and better formatting
    var ${uniquePrefix}telegramMsg = [
      '✅ Script Execution Finished Successfully',
      '------------------------------------',
      '🏭 Account: ' + ${uniquePrefix}tgAccountName + ' (' + ${uniquePrefix}tgAccountId + ')',
      '📅 Date: ' + ${uniquePrefix}formattedTime,
      '📈 Results: ' + ${uniquePrefix}conflicts.length + ' keyword conflicts found',
      '------------------------------------'
    ].join('\\n');
    
    // Include top conflicts with highest CPC differences if any conflicts exist
    if (${uniquePrefix}conflicts.length > 0) {
      const ${uniquePrefix}topConflictsTelegram = ${uniquePrefix}conflicts.slice(0, Math.min(5, ${uniquePrefix}conflicts.length));
      
      ${uniquePrefix}telegramMsg += '\\n\\n⚠️ TOP KEYWORD CONFLICTS:';
      
      ${uniquePrefix}topConflictsTelegram.forEach(function(${uniquePrefix}conflict, ${uniquePrefix}index) {
        // Calculate CPC range
        const ${uniquePrefix}maxCpc = Math.max.apply(null, ${uniquePrefix}conflict.instances.map(function(instance) { return instance.cpc; }));
        const ${uniquePrefix}minCpc = Math.min.apply(null, ${uniquePrefix}conflict.instances.map(function(instance) { return instance.cpc > 0 ? instance.cpc : ${uniquePrefix}maxCpc; }));
        const ${uniquePrefix}cpcDiff = ${uniquePrefix}maxCpc - ${uniquePrefix}minCpc;
        
        ${uniquePrefix}telegramMsg += '\\n' + (${uniquePrefix}index + 1) + '. "' + ${uniquePrefix}conflict.keyword + '"';
        ${uniquePrefix}telegramMsg += '\\n   CPC Range: $' + ${uniquePrefix}minCpc.toFixed(2) + ' - $' + ${uniquePrefix}maxCpc.toFixed(2) + ' (Diff: $' + ${uniquePrefix}cpcDiff.toFixed(2) + ')';
        
        // Show instance details for each conflict
        ${uniquePrefix}conflict.instances.sort(function(a, b) {
          return b.cpc - a.cpc;
        });
        
        // Show up to 2 instances per keyword
        const ${uniquePrefix}instancesToShow = ${uniquePrefix}conflict.instances.slice(0, Math.min(2, ${uniquePrefix}conflict.instances.length));
        ${uniquePrefix}instancesToShow.forEach(function(${uniquePrefix}instance) {
          ${uniquePrefix}telegramMsg += '\\n     - ' + ${uniquePrefix}instance.adGroup + ' ($' + ${uniquePrefix}instance.cpc.toFixed(2) + '): ' + ${uniquePrefix}instance.matchType;
        });
        
        if (${uniquePrefix}conflict.instances.length > 2) {
          ${uniquePrefix}telegramMsg += '\\n   ...and ' + (${uniquePrefix}conflict.instances.length - 2) + ' more instances';
        }
        
        ${uniquePrefix}telegramMsg += '\\n';
      });
      
      if (${uniquePrefix}conflicts.length > 5) {
        ${uniquePrefix}telegramMsg += '\\n...and ' + (${uniquePrefix}conflicts.length - 5) + ' more conflicts';
      }
    }
    
    // Send Telegram notification
    sendTelegramMessage(${uniquePrefix}telegramMsg);
    ` : `
    // Telegram notifications are disabled
    Logger.log("Telegram notifications are disabled. Enable them in the script configuration to receive reports.");
    `}
  } else {
    // No conflicts found
    const ${uniquePrefix}noConflictsMsg = 'No keyword conflicts found in your account for the ' + ${uniquePrefix}dateRange + ' period.';
    Logger.log(${uniquePrefix}noConflictsMsg);
    
    ${useTelegram ? `
    // Get account info for Telegram message (even if no conflicts)
    var ${uniquePrefix}tgAccountInfo = AdsApp.currentAccount();
    var ${uniquePrefix}tgAccountName = ${uniquePrefix}tgAccountInfo.getName();
    var ${uniquePrefix}tgAccountId = ${uniquePrefix}tgAccountInfo.getCustomerId();
    var ${uniquePrefix}formattedTime = Utilities.formatDate(new Date(), AdsApp.currentAccount().getTimeZone(), 'yyyy-MM-dd HH:mm:ss z');

    const ${uniquePrefix}telegramMsg = "<b>✅ No Keyword Conflicts Found</b>\\n\\n" +
      "🏭 Account: " + ${uniquePrefix}tgAccountName + " (" + ${uniquePrefix}tgAccountId + ")\\n" +
      "📅 Date: " + ${uniquePrefix}formattedTime + "\\n" +
      "No duplicate keywords were found in your account for the " + ${uniquePrefix}dateRange + " period.\\n\\n" +
      "<i>This is a good sign! Your account doesn't have any conflicting keywords that might be competing against each other.</i>";
    sendTelegramMessage(${uniquePrefix}telegramMsg);
    ` : `
    // Telegram notifications are disabled
    Logger.log('Telegram notifications are disabled. Skipping notification for no conflicts.');
    `}
  }
  Logger.log('Keyword conflict detection completed.');
}
`;

    return removeComments(script);
  };

  const removeComments = (code: string): string => {
    // Only remove obvious comment blocks and empty lines
    let result = code.replace(/\/\*[\s\S]*?\*\//g, ''); // remove block comments
    result = result.replace(/^\s*\/\/.*$/gm, ''); // remove line comments (only at start of line)
    result = result.replace(/^\s*[\r\n]/gm, ''); // remove empty lines
    return result.trim();
  };

  return (
    <TooltipProvider>
      <ToolPageLayout title="Keyword Conflict Detector" description="Generates a Google Ads script to identify conflicting keywords across campaigns and ad groups.">
        <div className="space-y-6">
          <FormSection title="Configuration Settings" icon={<Fingerprint />} theme="sky">
            <FormItem 
              label="Google Ads Account ID (Optional)" 
              htmlFor="kcdAccountId"
              tooltipText="Enter your Google Ads Account ID (e.g., 123-456-7890). Leave blank or use 'MCC' to run on the MCC level (if script is in an MCC account)."
            >
              <StyledInput 
                id="kcdAccountId" 
                name="accountId" 
                value={accountId} 
                onChange={handleInputChange} 
                placeholder="e.g., 123-456-7890 or MCC"
              />
            </FormItem>
            <FormItem 
              label="Date Range for Analysis" 
              htmlFor="kcdDateRange"
              tooltipText="Select the period over which to analyze keyword performance for conflicts."
            >
              <select 
                id="kcdDateRange" 
                name="dateRange" 
                value={dateRange} 
                onChange={handleInputChange}
                className="block w-full p-2.5 border border-slate-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 bg-slate-700 text-slate-200 placeholder-slate-400 text-sm"
              >
                {GOOGLE_ADS_DATE_RANGES_KCD.map(range => (
                  <option key={range.value} value={range.value}>{range.label}</option>
                ))}
              </select>
            </FormItem>
          </FormSection>

          <FormSection 
            title="Telegram Notifications" 
            icon={<Bot />} 
            theme="blue" 
            collapsible={true} 
            initiallyCollapsed={!showTelegramSettings} // Controls if the section is collapsed initially
          >
            <div className="mb-4">
              <label className="flex items-center cursor-pointer group">
                <div className="relative">
                  <input
                    type="checkbox"
                    id="kcdUseTelegram"
                    name="useTelegram"
                    checked={useTelegram}
                    onChange={handleInputChange}
                    className="sr-only"
                    data-tooltip-content="Check to receive a summary of the script's findings via Telegram."
                  />
                  <div className={`
                    w-5 h-5 rounded border-2 flex items-center justify-center transition-all duration-200
                    ${useTelegram 
                      ? 'bg-sky-600 border-sky-600' 
                      : 'bg-slate-700/50 border-slate-500 group-hover:border-slate-400'}
                    }`}
                  >
                    {useTelegram && (
                      <svg className="w-3 h-3 text-white" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    )}
                  </div>
                </div>
                <span className="ml-3 text-gray-300 text-sm font-medium">
                  Receive summary via Telegram
                </span>
              </label>
            </div>
            {showTelegramSettings && (
              <>
                <FormItem 
                  label="Telegram Bot Token" 
                  htmlFor="kcdTelegramBotToken" 
                  required={useTelegram}
                  tooltipText="Your Telegram Bot Token."
                >
                  <StyledInput 
                    id="kcdTelegramBotToken" 
                    name="telegramBotToken" 
                    type="password" 
                    value={telegramBotToken} 
                    onChange={handleInputChange} 
                    placeholder="Enter Telegram Bot Token"
                  />
                </FormItem>
                <FormItem 
                  label="Telegram Chat ID" 
                  htmlFor="kcdTelegramChatId" 
                  required={useTelegram}
                  tooltipText="Your Telegram Chat ID."
                >
                  <StyledInput 
                    id="kcdTelegramChatId" 
                    name="telegramChatId" 
                    value={telegramChatId} 
                    onChange={handleInputChange} 
                    placeholder="Enter Telegram Chat ID"
                  />
                </FormItem>
              </>
            )}
          </FormSection>

          <StyledButton 
            onClick={handleGenerateScript} 
            variant="primary" 
            size="lg"
            className="w-full shadow-md hover:shadow-lg transition-shadow duration-300 ease-in-out"
          >
            Generate Script
          </StyledButton>

          {message && (
            <NotificationMessage 
              type={message.type} 
              message={message.text} 
              onDismiss={() => setMessage(null)} 
            />
          )}

          {showResult && generatedScript && (
            <ScriptDisplay 
              scriptContent={generatedScript} 
              title="Generated Keyword Conflict Detector Script"
              language="javascript"
            />
          )}
        </div>
      </ToolPageLayout>
    </TooltipProvider>
  );
};

export default KeywordConflictDetector;
