import React, { useState, useEffect, ReactNode } from 'react';
import { Table, Key, Settings, ListChecks, BookOpen, FileText, PlayCircle, Eye } from 'lucide-react'; 
import ToolPageLayout from '../ui/shared/ToolPageLayout';
import FormSection from '../ui/shared/FormSection';
import FormItem from '../ui/shared/FormItem';
import StyledInput from '../ui/shared/StyledInput';
import StyledButton from '../ui/shared/StyledButton';
import ScriptDisplay from '../ui/shared/ScriptDisplay';
import NotificationMessage from '../ui/shared/NotificationMessage';

const LOCAL_STORAGE_PREFIX = 'airtablePnlScriptGenerator_';
const MAX_HISTORY_LENGTH = 3;

interface FormData {
  accountName: string;
  buyerId: string;
  niche: string;
  airtableUrl: string;
  apiKey: string;
  banDays: string;
  freezeDays: string;
  warningDays: string;
}

const AirtablePnlScriptGenerator: React.FC = () => {
  const [formData, setFormData] = useState<FormData>(() => {
    const initialFormData: Partial<FormData> = {};
    const defaultValues: FormData = {
      accountName: '',
      buyerId: '',
      niche: '',
      airtableUrl: '',
      apiKey: '',
      banDays: '30',
      freezeDays: '15',
      warningDays: '7',
    };
    (Object.keys(defaultValues) as Array<keyof FormData>).forEach(key => {
      const storedValue = localStorage.getItem(`${LOCAL_STORAGE_PREFIX}${key}_lastInput`);
      initialFormData[key] = storedValue !== null ? storedValue : defaultValues[key];
    });
    return initialFormData as FormData;
  });

  const [formHistory, setFormHistory] = useState<Record<keyof FormData, string[]>>(() => {
    const initialFormHistory: Record<keyof FormData, string[]> = {
      accountName: [], buyerId: [], niche: [], airtableUrl: [], apiKey: [], banDays: [], freezeDays: [], warningDays: [],
    };
    (Object.keys(initialFormHistory) as Array<keyof FormData>).forEach(key => {
      const storedHistory = localStorage.getItem(`${LOCAL_STORAGE_PREFIX}${key}_history`);
      if (storedHistory) {
        initialFormHistory[key] = JSON.parse(storedHistory);
      }
    });
    return initialFormHistory;
  });

  const [generatedScript, setGeneratedScript] = useState('');
  const [showResult, setShowResult] = useState(false);
  const [message, setMessage] = useState<{text: string | ReactNode, type: 'error' | 'success' | 'info'} | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  const updateFieldHistory = (field: keyof FormData, value: string) => {
    if (!value.trim()) return;
    localStorage.setItem(`${LOCAL_STORAGE_PREFIX}${field}_lastInput`, value);
    setFormHistory(prevHistory => {
      const currentHistory = prevHistory[field];
      const newHistory = [value, ...currentHistory.filter(item => item !== value)].slice(0, MAX_HISTORY_LENGTH);
      localStorage.setItem(`${LOCAL_STORAGE_PREFIX}${field}_history`, JSON.stringify(newHistory));
      return { ...prevHistory, [field]: newHistory };
    });
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target as { name: keyof FormData, value: string };
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleInputBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const { name, value } = e.target as { name: keyof FormData, value: string };
    updateFieldHistory(name, value);
  };

  const generateRandomString = (length = 0): string => {
    if (!length) length = Math.floor(Math.random() * 6) + 5;
    const characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
  };

  const scriptTemplate = `function main() {
    try {
  var {{airName}} = '{{ACC_NAME_OR_MACHINE_ID}}'; 
  var {{airGroup}} = '{{BUYER_ID_FROM_AIRTABLE}}'; 
  var {{airTheme}} = '{{NICHE_FROM_AIRTABLE}}';  
  
  var {{airUrl}} = '{{URL_OF_AIRTABLE}}'; 
  var {{airKey}} = '{{API_OF_AIRTABLE}}'; 
  
  var {{statusBannedTerm}} = {{DAYS_TO_BAN}};
  var {{statusFrozenTerm}} = {{DAYS_TO_FREEZE}}; 
  var {{statusWarningTerm}} = {{DAYS_TO_WARNING}}; 
  
  var {{CTTT}} = AdsApp.currentAccount();
  
  var {{body}} = {
       'records':[    
        {
         'id': '',
         'fields':{
          'Status': 'Works',
          'Clicks (T)': {{CTTT}}.getStatsFor('TODAY').getClicks(),
          'Clicks (A)': {{CTTT}}.getStatsFor('ALL_TIME').getClicks(),
          'Impr. (T)': {{CTTT}}.getStatsFor('TODAY').getImpressions(),
          'Impr. (A)': {{CTTT}}.getStatsFor('ALL_TIME').getImpressions(),
          'CTR (T)': {{CTTT}}.getStatsFor('TODAY').getCtr(),
          'CTR (A)': {{CTTT}}.getStatsFor('ALL_TIME').getCtr(),
          'CPC (T)': {{CTTT}}.getStatsFor('TODAY').getAverageCpc(),
          'CPC (A)': {{CTTT}}.getStatsFor('ALL_TIME').getAverageCpc(),
          'Cost (T)': {{CTTT}}.getStatsFor('TODAY').getCost(),
          'Cost (A)': {{CTTT}}.getStatsFor('ALL_TIME').getCost(),
          'CPM (T)': {{CTTT}}.getStatsFor('TODAY').getAverageCpm(),
          'CPM (A)': {{CTTT}}.getStatsFor('ALL_TIME').getAverageCpm(),
          'Conv (A)': {{CTTT}}.getStatsFor('ALL_TIME').getConversions(),
          'Conv (T)': {{CTTT}}.getStatsFor('TODAY').getConversions(),
          'Conv. Rate (T)': {{CTTT}}.getStatsFor('TODAY').getConversionRate(),
          'Conv. Rate (A)': {{CTTT}}.getStatsFor('ALL_TIME').getConversionRate(),
          'Cost/Conv (T)': {{CTTT}}.getStatsFor('TODAY').getConversions() > 0 ? {{CTTT}}.getStatsFor('TODAY').getCost() / {{CTTT}}.getStatsFor('TODAY').getConversions() : 0,
          'Cost/Conv (A)': {{CTTT}}.getStatsFor('ALL_TIME').getConversions() > 0 ? {{CTTT}}.getStatsFor('ALL_TIME').getCost() / {{CTTT}}.getStatsFor('ALL_TIME').getConversions() : 0
         }
        }
       ]
    };
  
  var {{airSearchPattern}} = "AND({Account}='"+{{airName}}+"',{Group} ='"+{{airGroup}}+"')";
  
  var {{response}} = JSON.parse({{reqGet}}('GET', {{airUrl}}+"?filterByFormula="+encodeURI({{airSearchPattern}}), {{airKey}}).getContentText()); 

  if({{response}}['records'].length > 0){
    {{body}}['records'][0]['id'] = {{response}}['records'][0]['id']; 
    
    var {{fields}} = {{response}}['records'][0]['fields'];
    var {{dataDiff}} = {{dateComparison}}(({{fields}}['Last update'] || new Date().toISOString()), new Date().toISOString());
    var {{statusCurrent}} = {{fields}}['Status'];

    if({{body}}['records'][0]['fields']['Impr. (A)'] != ({{fields}}['Impr. (A)'] || 0) ){
      {{body}}['records'][0]['fields']['Status'] = 'Works';
      {{reqCustom}}('PATCH', {{airUrl}}, {{body}}, {{airKey}})
    } else 
      if({{dataDiff}} > {{statusBannedTerm}} && {{statusCurrent}} != 'Banned'){
        {{body}}['records'][0]['fields'] = 
        { 
          'Status': 'Banned',
          'Ad status': AdsApp.ads().get().hasNext() ? AdsApp.ads().get().next().getPolicyApprovalStatus() : 'N/A',
          'Disapproved reason': AdsApp.ads().get().hasNext() ? AdsApp.ads().get().next().getDisapprovalReasons().join(', ') : 'N/A',
          'Last status change': new Date().toISOString()
        };
        {{reqCustom}}('PATCH', {{airUrl}}, {{body}}, {{airKey}});
      } else 
        if({{dataDiff}} > {{statusFrozenTerm}} && {{statusCurrent}} != 'Frozen' && {{statusCurrent}} != 'Banned'){
          {{body}}['records'][0]['fields'] = 
          { 
            'Status': 'Frozen',
            'Ad status': AdsApp.ads().get().hasNext() ? AdsApp.ads().get().next().getPolicyApprovalStatus() : 'N/A',
            'Disapproved reason': AdsApp.ads().get().hasNext() ? AdsApp.ads().get().next().getDisapprovalReasons().join(', ') : 'N/A',
            'Last status change': new Date().toISOString()
          };
          {{reqCustom}}('PATCH', {{airUrl}}, {{body}}, {{airKey}});
        } else 
        if({{dataDiff}} > {{statusWarningTerm}} && {{statusCurrent}} != 'Warning' && {{statusCurrent}} != 'Frozen' && {{statusCurrent}} != 'Banned'){
          {{body}}['records'][0]['fields'] = 
          { 
            'Status': 'Warning',
            'Ad status': AdsApp.ads().get().hasNext() ? AdsApp.ads().get().next().getPolicyApprovalStatus() : 'N/A',
            'Disapproved reason': AdsApp.ads().get().hasNext() ? AdsApp.ads().get().next().getDisapprovalReasons().join(', ') : 'N/A',
            'Last status change': new Date().toISOString()
          };
          {{reqCustom}}('PATCH', {{airUrl}}, {{body}}, {{airKey}});
        }
  } else {
    var {{createBody}} = {
     'records':[ 
      {
       'fields':{
        'Account': {{airName}},
        'Group': {{airGroup}},
        'Theme': {{airTheme}},
        'Status': 'Works'
       }
      }
     ]
    };
  
  var createResponseVar = {{reqCustom}}('POST', {{airUrl}}, {{createBody}}, {{airKey}});
  if (createResponseVar.getResponseCode() >= 200 && createResponseVar.getResponseCode() < 300) {
    {{response}} = JSON.parse(createResponseVar.getContentText());
    if ({{response}} && {{response}}['records'] && {{response}}['records'].length > 0) {
      {{body}}['records'][0]['id'] = {{response}}['records'][0]['id'];
      {{reqCustom}}('PATCH', {{airUrl}}, {{body}}, {{airKey}});
    } else {
      Logger.log("Error: No records returned from create operation for " + {{airName}});
    }
  } else {
    Logger.log("Error creating record for " + {{airName}} + ": " + createResponseVar.getResponseCode() + " - " + createResponseVar.getContentText());
  }
  }
  
  Logger.log("Script executed successfully for account: " + {{airName}});
    } catch (e) {
        Logger.log('Error in main function: ' + e.toString() + ' at line ' + e.lineNumber);
        var {{airName}} = '{{ACC_NAME_OR_MACHINE_ID}}';
        var {{airGroup}} = '{{BUYER_ID_FROM_AIRTABLE}}';
        var {{airUrl}} = '{{URL_OF_AIRTABLE}}'; 
        var {{airKey}} = '{{API_OF_AIRTABLE}}';
        var {{errorBody}} = {
            'records': [
                {
                    'id': '',
                    'fields': {
                        'Status': 'ERROR',
                        'Error Message': e.toString(),
                        'Last status change': new Date().toISOString()
                    }
                }
            ]
        };
        var {{airSearchPatternError}} = "AND({Account}='"+{{airName}}+"',{Group} ='"+{{airGroup}}+"')";
        var {{responseError}} = JSON.parse({{reqGet}}('GET', {{airUrl}}+"?filterByFormula="+encodeURI({{airSearchPatternError}}), {{airKey}}).getContentText());
        if ({{responseError}}['records'].length > 0) {
            {{errorBody}}['records'][0]['id'] = {{responseError}}['records'][0]['id'];
            {{reqCustom}}('PATCH', {{airUrl}}, {{errorBody}}, {{airKey}});
        } else {
            var {{createErrorBody}} = {
                'records': [
                    {
                        'fields': {
                            'Account': {{airName}},
                            'Group': {{airGroup}},
                            'Status': 'ERROR',
                            'Error Message': e.toString(),
                            'Last status change': new Date().toISOString()
                        }
                    }
                ]
            };
            {{reqCustom}}('POST', {{airUrl}}, {{createErrorBody}}, {{airKey}});
        }
    }
}

function {{dateComparison}}(a,b){
  return Math.floor((Date.parse(b) - Date.parse(a)) / (1000 * 60 * 60 * 24));
}

function {{reqGet}}(method, url, token){
  return UrlFetchApp.fetch(url, {
    'method': method,
    'headers': { 'Authorization': 'Bearer '+token }
  });
}

function {{reqCustom}}(method, url, body, token){
  return UrlFetchApp.fetch(url, {
    'method': method,
    'contentType': 'application/json',
    'payload': JSON.stringify(body),
    'headers': { 'Authorization': 'Bearer '+token }
  });
}
`;

  const generateScript = (preview = false) => {
    setMessage(null);
    const { accountName, buyerId, niche, airtableUrl, apiKey, banDays, freezeDays, warningDays } = formData;
    if (!accountName || !buyerId || !niche || !airtableUrl || !apiKey || !banDays || !freezeDays || !warningDays) {
      setMessage({text: 'All fields are required to generate the script.', type: 'error'});
      return;
    }
    if (isNaN(parseInt(banDays)) || isNaN(parseInt(freezeDays)) || isNaN(parseInt(warningDays))) {
      setMessage({text: 'Days for status thresholds must be valid numbers.', type: 'error'});
      return;
    }

    let script = scriptTemplate;
    const placeholders = {
      airName: generateRandomString(),
      airGroup: generateRandomString(),
      airTheme: generateRandomString(),
      airUrl: generateRandomString(),
      airKey: generateRandomString(),
      statusBannedTerm: generateRandomString(),
      statusFrozenTerm: generateRandomString(),
      statusWarningTerm: generateRandomString(),
      CTTT: generateRandomString(),
      body: generateRandomString(),
      airSearchPattern: generateRandomString(),
      response: generateRandomString(),
      fields: generateRandomString(),
      dataDiff: generateRandomString(),
      statusCurrent: generateRandomString(),
      reqCustom: generateRandomString(),
      createBody: generateRandomString(),
      reqGet: generateRandomString(),
      dateComparison: generateRandomString(),
      errorBody: generateRandomString(),
      airSearchPatternError: generateRandomString(),
      responseError: generateRandomString(),
      createErrorBody: generateRandomString(),
    };

    for (const [key, value] of Object.entries(placeholders)) {
      script = script.replace(new RegExp(`{{${key}}}`, 'g'), value);
    }

    script = script
      .replace(new RegExp("'{{ACC_NAME_OR_MACHINE_ID}}'", 'g'), `'${accountName.replace(/'/g, "\\'")}'`)
      .replace(new RegExp("'{{BUYER_ID_FROM_AIRTABLE}}'", 'g'), `'${buyerId.replace(/'/g, "\\'")}'`)
      .replace(new RegExp("'{{NICHE_FROM_AIRTABLE}}'", 'g'), `'${niche.replace(/'/g, "\\'")}'`)
      .replace(new RegExp("'{{URL_OF_AIRTABLE}}'", 'g'), `'${airtableUrl.replace(/'/g, "\\'")}'`)
      .replace(new RegExp("'{{API_OF_AIRTABLE}}'", 'g'), `'${apiKey.replace(/'/g, "\\'")}'`)
      .replace(new RegExp('{{DAYS_TO_BAN}}', 'g'), banDays)
      .replace(new RegExp('{{DAYS_TO_FREEZE}}', 'g'), freezeDays)
      .replace(new RegExp('{{DAYS_TO_WARNING}}', 'g'), warningDays);

    setGeneratedScript(script);
    if (preview) {
      setShowPreview(true);
      setMessage({ text: 'Script preview generated. Review below.', type: 'info' });
    } else {
      setShowResult(true);
      setShowPreview(false);
      setMessage({ text: 'Script generated successfully!', type: 'success' });
    }
  };

  const handleGenerateScript = () => generateScript(false);
  const handlePreviewScript = () => generateScript(true);

  const howThisWorksContent = (
    <>
      <div className="mb-6">
        <h4 className="text-md font-semibold text-sky-200 mb-2 flex items-center"><ListChecks size={18} className="mr-2"/>How to Use the Script</h4>
        <ol className="list-decimal list-inside space-y-1.5 text-sm text-gray-300">
          <li>Copy the generated script</li>
          <li>Open Google Ads account: "Tools & Settings" &gt; "Bulk Actions" &gt; "Scripts"</li>
          <li>Create a new script, name it, and paste the code</li>
          <li>Authorize the script</li>
          <li>Set a schedule (e.g., hourly or daily)</li>
          <li>Save and run manually or wait for the schedule</li>
        </ol>
        <p className="mt-2 text-xs text-gray-400">
          <span className="font-semibold">Note:</span> Ensure your Airtable base has columns matching the fields the script updates (e.g., 'Status', 'Clicks (T)', etc.).
        </p>
      </div>
      <div>
        <h4 className="text-md font-semibold text-sky-200 mb-2 flex items-center"><BookOpen size={18} className="mr-2"/>How to Get Airtable Connection Details</h4>
        <div className="space-y-4 text-sm text-gray-300">
          <div>
            <h5 className="font-medium text-sky-300 mb-1">Airtable URL (Table specific):</h5>
            <ul className="list-disc list-inside space-y-1 ml-2">
              <li>Login to Airtable &gt; Open base &gt; Click "Help" (top right) &gt; "API documentation"</li>
              <li>Select target table</li>
              <li>URL format in examples: <code className="bg-gray-800 px-1 py-0.5 rounded text-xs">https://api.airtable.com/v0/YOUR_APP_ID/YOUR_TABLE_NAME_OR_ID</code></li>
              <li>Use the full URL from the examples</li>
            </ul>
          </div>
          <div>
            <h5 className="font-medium text-sky-300 mb-1">API Key:</h5>
            <ul className="list-disc list-inside space-y-1 ml-2">
              <li>In Airtable account settings (click profile icon &gt; Account) &gt; Generate API key (or use existing)</li>
              <li>Treat this key like a password for security</li>
            </ul>
          </div>
        </div>
      </div>
    </>
  );

  return (
    <ToolPageLayout
      title="Airtable P&L Script Generator"
      description="Generates a Google Ads script to synchronize campaign performance data with your Airtable base for P&L tracking."
      howThisWorksTitle="Setup and Instructions"
      howThisWorksContent={howThisWorksContent}
    >
      {!showResult && !showPreview && (
        <div className="space-y-8">
          <FormSection title="Airtable Account Details" icon={<Table />} theme="sky">
            <FormItem label="Account Name / Machine ID" htmlFor="accountName" tooltipText="Enter the specific Account Name or Machine ID as it appears or should appear in Airtable." required>
              <StyledInput type="text" id="accountName" name="accountName" value={formData.accountName} onChange={handleInputChange} onBlur={handleInputBlur} placeholder="e.g., My Google Ads Account" list={`${LOCAL_STORAGE_PREFIX}accountName_history`} />
              <datalist id={`${LOCAL_STORAGE_PREFIX}accountName_history`}>
                {formHistory.accountName.map(item => <option key={item} value={item} />)}
              </datalist>
            </FormItem>
            <FormItem label="Buyer ID (from Airtable)" htmlFor="buyerId" tooltipText="Enter the Buyer ID associated with this account in Airtable." required>
              <StyledInput type="text" id="buyerId" name="buyerId" value={formData.buyerId} onChange={handleInputChange} onBlur={handleInputBlur} placeholder="e.g., Buyer_001" list={`${LOCAL_STORAGE_PREFIX}buyerId_history`} />
              <datalist id={`${LOCAL_STORAGE_PREFIX}buyerId_history`}>
                {formHistory.buyerId.map(item => <option key={item} value={item} />)}
              </datalist>
            </FormItem>
            <FormItem label="Niche (from Airtable)" htmlFor="niche" tooltipText="Specify the Niche for this account as categorized in Airtable." required>
              <StyledInput type="text" id="niche" name="niche" value={formData.niche} onChange={handleInputChange} onBlur={handleInputBlur} placeholder="e.g., SaaS Software" list={`${LOCAL_STORAGE_PREFIX}niche_history`} />
              <datalist id={`${LOCAL_STORAGE_PREFIX}niche_history`}>
                {formHistory.niche.map(item => <option key={item} value={item} />)}
              </datalist>
            </FormItem>
          </FormSection>

          <FormSection title="Airtable Connection Details" icon={<Key />} theme="emerald">
            <FormItem label="Airtable URL (Table Specific)" htmlFor="airtableUrl" tooltipText="Full API URL for your specific Airtable table (see instructions below)." required>
              <StyledInput type="url" id="airtableUrl" name="airtableUrl" value={formData.airtableUrl} onChange={handleInputChange} onBlur={handleInputBlur} placeholder="https://api.airtable.com/v0/appXXXX/Table%201" list={`${LOCAL_STORAGE_PREFIX}airtableUrl_history`} />
              <datalist id={`${LOCAL_STORAGE_PREFIX}airtableUrl_history`}>
                {formHistory.airtableUrl.map(item => <option key={item} value={item} />)}
              </datalist>
            </FormItem>
            <FormItem label="API Key" htmlFor="apiKey" tooltipText="Your Airtable API key (see instructions below)." required>
              <StyledInput type="text" id="apiKey" name="apiKey" value={formData.apiKey} onChange={handleInputChange} onBlur={handleInputBlur} placeholder="keyXXXXXXXXXXXXXX" list={`${LOCAL_STORAGE_PREFIX}apiKey_history`} />
              <datalist id={`${LOCAL_STORAGE_PREFIX}apiKey_history`}>
                {formHistory.apiKey.map(item => <option key={item} value={item} />)}
              </datalist>
            </FormItem>
          </FormSection>

          <FormSection title="Status Thresholds (Days)" icon={<Settings />} theme="purple">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <FormItem label="Ban After" htmlFor="banDays" tooltipText="Days of no impressions before status becomes 'Banned'." required>
                <StyledInput type="number" id="banDays" name="banDays" value={formData.banDays} onChange={handleInputChange} onBlur={handleInputBlur} placeholder="30" />
              </FormItem>
              <FormItem label="Freeze After" htmlFor="freezeDays" tooltipText="Days of no impressions before status becomes 'Frozen'." required>
                <StyledInput type="number" id="freezeDays" name="freezeDays" value={formData.freezeDays} onChange={handleInputChange} onBlur={handleInputBlur} placeholder="15" />
              </FormItem>
              <FormItem label="Warning After" htmlFor="warningDays" tooltipText="Days of no impressions before status becomes 'Warning'." required>
                <StyledInput type="number" id="warningDays" name="warningDays" value={formData.warningDays} onChange={handleInputChange} onBlur={handleInputBlur} placeholder="7" />
              </FormItem>
            </div>
          </FormSection>

          <div className="flex flex-col sm:flex-row justify-start gap-4 mt-8">
            <StyledButton onClick={handleGenerateScript} leftIcon={<FileText className="mr-2 h-5 w-5" />} size="lg" themeColor="sky">
              Generate Script
            </StyledButton>
            <StyledButton onClick={handlePreviewScript} variant="outline" leftIcon={<Eye className="mr-2 h-5 w-5" />} size="lg" themeColor="slate">
              Preview Script
            </StyledButton>
          </div>
        </div>
      )}

      {message && (
        <div className="my-6">
          <NotificationMessage 
            type={message.type} 
            message={message.text} 
            onDismiss={() => setMessage(null)} 
          />
        </div>
      )}

      {(showResult || showPreview) && generatedScript && (
        <div className="mt-8">
          <ScriptDisplay 
            scriptContent={generatedScript} 
            title={showPreview ? "Script Preview" : "Generated Airtable P&L Script"} 
            language="javascript" 
          />
          <StyledButton 
            onClick={() => {
              setShowResult(false);
              setShowPreview(false);
              // setMessage(null); // Keep success/info message from generation if desired
            }}
            variant="outline"
            size="md"
            className="mt-6 w-full sm:w-auto"
            themeColor="slate"
            leftIcon={<PlayCircle className="mr-2 h-5 w-5" />}
          >
            Back to Form
          </StyledButton>
        </div>
      )}
    </ToolPageLayout>
  );
};

export default AirtablePnlScriptGenerator;
