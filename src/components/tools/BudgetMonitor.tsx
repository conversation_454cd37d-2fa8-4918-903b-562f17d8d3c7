import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, ListChecks, PlayCircle } from 'lucide-react';

import ToolPageLayout from '../ui/shared/ToolPageLayout';
import FormSection from '../ui/shared/FormSection';
import FormItem from '../ui/shared/FormItem';
import StyledInput from '../ui/shared/StyledInput';
import StyledButton from '../ui/shared/StyledButton';
import ScriptDisplay from '../ui/shared/ScriptDisplay';
import NotificationMessage from '../ui/shared/NotificationMessage';
import { useLanguage } from '../../contexts/LanguageContext';

const LOCAL_STORAGE_PREFIX = 'budgetMonitor_';

const BudgetMonitor: React.FC = () => {
  const { t } = useLanguage();
  const isUkrainian = t('language') === 'uk';

  // Form inputs
  const [campaignNamesInput, setCampaignNamesInput] = useState('');
  const [highPacingThreshold, setHighPacingThreshold] = useState('120');
  const [lowPacingThreshold, setLowPacingThreshold] = useState('80');

  // Telegram
  const [useTelegram, setUseTelegram] = useState(false);
  const [telegramBotToken, setTelegramBotToken] = useState('');
  const [telegramChatId, setTelegramChatId] = useState('');

  // UI State
  const [generatedScript, setGeneratedScript] = useState('');
  const [showResult, setShowResult] = useState(false);
  // const [copySuccess, setCopySuccess] = useState(false); // Handled by ScriptDisplay
  const [message, setMessage] = useState<{text: string, type: 'error' | 'success' | 'info'} | null>(null);

  // Load saved form data from localStorage
  useEffect(() => {
    const fields: string[] = ['campaignNamesInput', 'highPacingThreshold', 'lowPacingThreshold', 'useTelegram', 'telegramBotToken', 'telegramChatId'];
    fields.forEach(field => {
      const savedValue = localStorage.getItem(`${LOCAL_STORAGE_PREFIX}${field}`);
      if (savedValue !== null) {
        if (field === 'campaignNamesInput') setCampaignNamesInput(savedValue);
        else if (field === 'highPacingThreshold') setHighPacingThreshold(savedValue);
        else if (field === 'lowPacingThreshold') setLowPacingThreshold(savedValue);
        else if (field === 'useTelegram') setUseTelegram(savedValue === 'true');
        else if (field === 'telegramBotToken') setTelegramBotToken(savedValue);
        else if (field === 'telegramChatId') setTelegramChatId(savedValue);
      }
    });
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    setMessage(null);
    const { name, value, type } = e.target;
    const val = type === 'checkbox' ? (e.target as HTMLInputElement).checked : value;

    if (name === 'campaignNamesInput') setCampaignNamesInput(val as string);
    else if (name === 'highPacingThreshold') setHighPacingThreshold(val as string);
    else if (name === 'lowPacingThreshold') setLowPacingThreshold(val as string);
    else if (name === 'useTelegram') setUseTelegram(val as boolean);
    else if (name === 'telegramBotToken') setTelegramBotToken(val as string);
    else if (name === 'telegramChatId') setTelegramChatId(val as string);

    localStorage.setItem(`${LOCAL_STORAGE_PREFIX}${name}`, String(val));
  };

  const handleGenerateScript = () => {
    setMessage(null);
    const campaignNames = campaignNamesInput.split('\n').map(name => name.trim()).filter(name => name !== '');

    if (campaignNames.length === 0) {
      setMessage({ text: 'Please enter at least one campaign name to monitor.', type: 'error' });
      return;
    }
    if (useTelegram && (!telegramBotToken || !telegramChatId)) {
      setMessage({ text: 'Please enter both Telegram Bot Token and Chat ID for notifications.', type: 'error' });
      return;
    }
    if (isNaN(parseFloat(highPacingThreshold)) || isNaN(parseFloat(lowPacingThreshold))) {
        setMessage({ text: 'High and Low Pacing Thresholds must be valid numbers.', type: 'error' });
        return;
    }
    if (parseFloat(lowPacingThreshold) >= parseFloat(highPacingThreshold)){
        setMessage({ text: 'Low Pacing Threshold must be less than High Pacing Threshold.', type: 'error'});
        return;
    }

    const script = generateBudgetMonitorScript(
      campaignNames,
      parseFloat(highPacingThreshold),
      parseFloat(lowPacingThreshold),
      useTelegram,
      telegramBotToken,
      telegramChatId
    );
    setGeneratedScript(script);
    setShowResult(true);
    setTimeout(() => {
      document.getElementById('scriptDisplaySectionBudgetMon')?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  };

  // handleCopyScript removed

  const generateBudgetMonitorScript = (
    campaignNamesVal: string[], highThresholdVal: number, lowThresholdVal: number,
    useTelegramVal: boolean, botToken: string, chatId: string
  ): string => {
    const uniquePrefix = 'budgetMon' + Date.now().toString(36) + '_';
    const scriptName = 'Budget Pace Monitor'; // Added script name
    
    const escapeHtmlFunc = `
  /* @ts-ignore */
  function ${uniquePrefix}escapeHtml(unsafe) {
    if (typeof unsafe !== 'string') return unsafe;
    return unsafe
      .replace(/&/g, "&amp;")
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;")
      .replace(/"/g, "&quot;")
      .replace(/'/g, "&#039;");
  }
`;

    let telegramCode = '';
    if (useTelegramVal) {
      // Ensure botToken and chatId are cleaned and properly escaped for the script string literals
      const cleanBotToken = botToken.replace(/[^a-zA-Z0-9:_-]/g, '').trim();
      const cleanChatId = chatId.replace(/[^0-9-]/g, '').trim();

      telegramCode = `
${escapeHtmlFunc}
  /* @ts-ignore */
  function ${uniquePrefix}sendTelegramNotification(message) {
    var telegramUrl = 'https://api.telegram.org/bot${cleanBotToken}/sendMessage';
    var ${uniquePrefix}payload = {
      'chat_id': '${cleanChatId}',
      'text': message,
      'parse_mode': 'HTML',
      'disable_web_page_preview': true
    };
    var ${uniquePrefix}options = {
      'method': 'post',
      'contentType': 'application/json',
      'payload': JSON.stringify(${uniquePrefix}payload),
      'muteHttpExceptions': true
    };
    try {
      var response = UrlFetchApp.fetch(telegramUrl, ${uniquePrefix}options);
      Logger.log('Telegram notification sent. Response code: ' + response.getResponseCode());
      Logger.log('Response content: ' + response.getContentText().substring(0,500));
    } catch (e) { 
      Logger.log('Telegram Error: ' + e.toString() + ' | URL: ' + telegramUrl + ' | Payload: ' + JSON.stringify(${uniquePrefix}payload).substring(0,500) ); 
    }
  }
`;
    }

    const mainScript = `
/* @ts-ignore */
function main() {
  var ${uniquePrefix}CONFIG = {
    SCRIPT_NAME: '${scriptName}',
    CAMPAIGN_NAMES: ${JSON.stringify(campaignNamesVal)},
    HIGH_THRESHOLD_PERCENT: ${highThresholdVal},
    LOW_THRESHOLD_PERCENT: ${lowThresholdVal},
    ACCOUNT_TIMEZONE: AdsApp.currentAccount().getTimeZone(),
    USE_TELEGRAM: ${useTelegramVal}
  };

  var ${uniquePrefix}accountName = AdsApp.currentAccount().getName() || 'N/A';
  var ${uniquePrefix}accountId = AdsApp.currentAccount().getCustomerId() || 'N/A';
  var ${uniquePrefix}executionDate = new Date().toLocaleDateString('en-US', { timeZone: ${uniquePrefix}CONFIG.ACCOUNT_TIMEZONE });
  
  var ${uniquePrefix}resultsSummary = [];
  var ${uniquePrefix}errorOccurred = false;
  var ${uniquePrefix}errorMessage = '';
  var ${uniquePrefix}skippedDueToEarlyRun = false;

  var ${uniquePrefix}now = new Date();
  var ${uniquePrefix}formattedTime = Utilities.formatDate(${uniquePrefix}now, ${uniquePrefix}CONFIG.ACCOUNT_TIMEZONE, 'yyyy-MM-dd HH:mm:ss');
  Logger.log(${uniquePrefix}CONFIG.SCRIPT_NAME + ' Script Started at: ' + ${uniquePrefix}formattedTime + ' for account ' + ${uniquePrefix}accountName + ' (' + ${uniquePrefix}accountId + ')');
  
  // Send initial 'started' notification if Telegram is enabled
  if (${uniquePrefix}CONFIG.USE_TELEGRAM && typeof ${uniquePrefix}sendTelegramNotification === 'function') {
    var ${uniquePrefix}startMessage = 
        "⏳ Script Execution Started\n" +
        "------------------------------------\n" +
        "📊 Script: " + ${uniquePrefix}CONFIG.SCRIPT_NAME + "\n" +
        "🏭 Account: " + ${uniquePrefix}escapeHtml(${uniquePrefix}accountName) + " (" + ${uniquePrefix}escapeHtml(${uniquePrefix}accountId) + ")\n" +
        "📅 Date: " + ${uniquePrefix}executionDate + "\n" +
        "------------------------------------";
    ${uniquePrefix}sendTelegramNotification(${uniquePrefix}startMessage);
  }

  try {
    var ${uniquePrefix}hoursPassed = ${uniquePrefix}now.getHours();
    var ${uniquePrefix}minutesPassed = ${uniquePrefix}now.getMinutes();
    var ${uniquePrefix}fractionOfDayPassed = (${uniquePrefix}hoursPassed + (${uniquePrefix}minutesPassed / 60)) / 24;

    // Check if the script is run too early in the day (e.g., midnight before any spend data)
    // This condition checks if it's effectively the start of the current day in the account's timezone.
    if (${uniquePrefix}fractionOfDayPassed < (1/24) && ${uniquePrefix}now.getDate() === new Date(Date.now()).getDate()) { // Less than 1 hour into the day
      Logger.log('Script run very early in the day (fraction of day is ' + ${uniquePrefix}fractionOfDayPassed.toFixed(4) + '). Skipping pacing checks for today.');
      ${uniquePrefix}resultsSummary.push('Script run too early, checks skipped for today to ensure data accuracy.');
      ${uniquePrefix}skippedDueToEarlyRun = true;
    }

    if (!${uniquePrefix}skippedDueToEarlyRun) {
      for (var ${uniquePrefix}i = 0; ${uniquePrefix}i < ${uniquePrefix}CONFIG.CAMPAIGN_NAMES.length; ${uniquePrefix}i++) {
        var ${uniquePrefix}campaignName = ${uniquePrefix}CONFIG.CAMPAIGN_NAMES[${uniquePrefix}i];
        var ${uniquePrefix}campaignIterator = AdsApp.campaigns()
            .withCondition("Name = '" + ${uniquePrefix}campaignName.replace(/'/g, "\\'") + "'")
            .withCondition("Status = ENABLED")
            .get();

        if (${uniquePrefix}campaignIterator.hasNext()) {
          var ${uniquePrefix}campaign = ${uniquePrefix}campaignIterator.next();
          var ${uniquePrefix}budget = ${uniquePrefix}campaign.getBudget().getAmount();
          var ${uniquePrefix}stats = ${uniquePrefix}campaign.getStatsFor('TODAY');
          var ${uniquePrefix}spent = ${uniquePrefix}stats.getCost();
          var ${uniquePrefix}pacingPercentage = (${uniquePrefix}spent / (${uniquePrefix}budget * ${uniquePrefix}fractionOfDayPassed)) * 100;
          var ${uniquePrefix}status = '';

          if (isNaN(${uniquePrefix}pacingPercentage) || !isFinite(${uniquePrefix}pacingPercentage)) {
             ${uniquePrefix}pacingPercentage = 0; // Handle cases like 0 budget or 0 fraction of day passed early on
          }

          Logger.log("Campaign '" + ${uniquePrefix}campaignName + "', Budget: " + ${uniquePrefix}budget + ", Spent Today: " + ${uniquePrefix}spent + ", Ideal Spend: " + (${uniquePrefix}budget * ${uniquePrefix}fractionOfDayPassed).toFixed(2) + ", Pacing: " + ${uniquePrefix}pacingPercentage.toFixed(2) + "%");

          if (${uniquePrefix}pacingPercentage > ${uniquePrefix}CONFIG.HIGH_THRESHOLD_PERCENT) {
            ${uniquePrefix}status = '🔥 HIGH PACING';
            ${uniquePrefix}resultsSummary.push("Campaign '" + ${uniquePrefix}escapeHtml(${uniquePrefix}campaignName) + "': Pacing at " + ${uniquePrefix}pacingPercentage.toFixed(2) + "% (Spent: " + ${uniquePrefix}spent.toFixed(2) + ", Budget: " + ${uniquePrefix}budget.toFixed(2) + "). Status: " + ${uniquePrefix}status);
          } else if (${uniquePrefix}pacingPercentage < ${uniquePrefix}CONFIG.LOW_THRESHOLD_PERCENT && ${uniquePrefix}fractionOfDayPassed > 0.1) { // Only check low pacing if at least 10% of day passed
            ${uniquePrefix}status = '🐢 LOW PACING';
            ${uniquePrefix}resultsSummary.push("Campaign '" + ${uniquePrefix}escapeHtml(${uniquePrefix}campaignName) + "': Pacing at " + ${uniquePrefix}pacingPercentage.toFixed(2) + "% (Spent: " + ${uniquePrefix}spent.toFixed(2) + ", Budget: " + ${uniquePrefix}budget.toFixed(2) + "). Status: " + ${uniquePrefix}status);
          } else {
            // Optionally log campaigns that are pacing normally
             Logger.log("Campaign '" + ${uniquePrefix}campaignName + "' is pacing normally.");
          }
        } else {
          Logger.log('Campaign not found or not enabled: ' + ${uniquePrefix}campaignName);
          ${uniquePrefix}resultsSummary.push('Campaign not found or not enabled: ' + ${uniquePrefix}escapeHtml(${uniquePrefix}campaignName));
        }
      }
      if (!${uniquePrefix}skippedDueToEarlyRun && ${uniquePrefix}resultsSummary.length === 0 && ${uniquePrefix}CONFIG.CAMPAIGN_NAMES.length > 0) {
        ${uniquePrefix}resultsSummary.push('All monitored campaigns are pacing within defined thresholds.');
      } else if (${uniquePrefix}CONFIG.CAMPAIGN_NAMES.length === 0){
        ${uniquePrefix}resultsSummary.push('No campaigns were specified for monitoring.');
      }
    }

  } catch (e) {
    Logger.log('Error in ' + ${uniquePrefix}CONFIG.SCRIPT_NAME + ': ' + e.toString() + (e.stack ? '\nStack: ' + e.stack : ''));
    ${uniquePrefix}errorOccurred = true;
    ${uniquePrefix}errorMessage = e.toString() + (e.stack ? '\nStack ('+ AdsApp.currentAccount().getCustomerId() +'): ' + e.stack : '');
    // Add error to results summary for Telegram
    ${uniquePrefix}resultsSummary.push('Script Error: ' + ${uniquePrefix}escapeHtml(e.toString().substring(0,500))); 

  } finally {
    if (${uniquePrefix}CONFIG.USE_TELEGRAM && typeof ${uniquePrefix}sendTelegramNotification === 'function') {
      var ${uniquePrefix}statusEmoji = ${uniquePrefix}errorOccurred ? '❌' : '✅';
      var ${uniquePrefix}statusMessageText = ${uniquePrefix}errorOccurred ? 'Script Execution Failed' : 'Script Execution Finished Successfully';
      
      var ${uniquePrefix}finalTelegramSummaryText = '';
      if (${uniquePrefix}errorOccurred) {
        ${uniquePrefix}finalTelegramSummaryText = 'Error: ' + ${uniquePrefix}escapeHtml(${uniquePrefix}errorMessage.substring(0,1000)); // Limit error message length
      } else if (${uniquePrefix}resultsSummary.length > 0) {
        var ${uniquePrefix}summaryLines = [];
        for(var k=0; k < ${uniquePrefix}resultsSummary.length; k++){
            ${uniquePrefix}summaryLines.push(${uniquePrefix}resultsSummary[k]); // Already escaped if needed during push
        }
        ${uniquePrefix}finalTelegramSummaryText = ${uniquePrefix}summaryLines.join('\n');
      } else {
         // This case should ideally not be hit if logic above is correct (e.g. no campaigns, or all normal)
        ${uniquePrefix}finalTelegramSummaryText = 'No specific issues detected or no campaigns monitored.';
      }

      if (${uniquePrefix}finalTelegramSummaryText.length > 3500) { // Telegram message limit is 4096
        ${uniquePrefix}finalTelegramSummaryText = ${uniquePrefix}finalTelegramSummaryText.substring(0, 3500) + '\n... (summary truncated)';
      }
      if (${uniquePrefix}finalTelegramSummaryText.trim() === '') { // Ensure there's always some result text
          if(${uniquePrefix}skippedDueToEarlyRun) ${uniquePrefix}finalTelegramSummaryText = 'Script run too early, checks skipped.';
          else if(${uniquePrefix}CONFIG.CAMPAIGN_NAMES.length === 0) ${uniquePrefix}finalTelegramSummaryText = 'No campaigns were specified for monitoring.';
          else ${uniquePrefix}finalTelegramSummaryText = 'All monitored campaigns are pacing within defined thresholds.';
      }

      var ${uniquePrefix}telegramMessage = 
        ${uniquePrefix}statusEmoji + " " + ${uniquePrefix}statusMessageText + "\n" +
        "------------------------------------\n" +
        "📊 Script: " + ${uniquePrefix}CONFIG.SCRIPT_NAME + "\n" +
        "🏭 Account: " + ${uniquePrefix}escapeHtml(${uniquePrefix}accountName) + " (" + ${uniquePrefix}escapeHtml(${uniquePrefix}accountId) + ")\n" +
        "📅 Date: " + ${uniquePrefix}executionDate + "\n" +
        "📈 Results:\n" + ${uniquePrefix}finalTelegramSummaryText + "\n" +
        "------------------------------------";
      ${uniquePrefix}sendTelegramNotification(${uniquePrefix}telegramMessage);
    }
  }
  Logger.log(${uniquePrefix}CONFIG.SCRIPT_NAME + ' Script Finished at: ' + Utilities.formatDate(new Date(), ${uniquePrefix}CONFIG.ACCOUNT_TIMEZONE, 'yyyy-MM-dd HH:mm:ss'));
}
`;

    // Combine Telegram utility functions with the main script logic
    return telegramCode + mainScript;
  };

  const pageTitle = isUkrainian ? "Монітор темпу бюджету Google Ads" : "Google Ads Budget Pace Monitor";
  const pageDescription = isUkrainian ? "Відстежує темп щоденних витрат вказаних кампаній Google Ads і надсилає сповіщення, якщо вони значно перевищують або не досягають бюджету. Допомагає запобігти перевитратам і забезпечує ефективне використання виділеного бюджету." : "Monitors the daily spending pace of specified Google Ads campaigns and sends alerts if they are significantly over or under budget. Helps prevent overspending and ensures campaigns utilize their allocated budget effectively.";

  const howThisWorksContent = isUkrainian ? (
    <div className="space-y-4">
      <h3 className="text-xl font-semibold text-slate-200">Як це працює</h3>
      <ul className="list-disc list-inside space-y-1 text-slate-300">
        <li><strong>Введення кампаній:</strong> Ви надаєте список точних назв кампаній для моніторингу (по одній на рядок).</li>
        <li><strong>Розрахунок темпу:</strong> Скрипт запускається (в ідеалі щогодини) і розраховує очікувані витрати на поточний час дня на основі щоденного бюджету кампанії.</li>
        <li><strong>Пороги:</strong>
          <ul className="list-disc list-inside ml-6 mt-1">
            <li><strong>Високий поріг темпу:</strong> Якщо фактичні витрати перевищують очікувані витрати на цей відсоток (наприклад, 120% означає, що витрати на 20% перевищують темп), спрацьовує сповіщення.</li>
            <li><strong>Низький поріг темпу:</strong> Якщо фактичні витрати нижче очікуваних витрат на цей відсоток (наприклад, 80% означає, що витрати на 20% нижче темпу), спрацьовує сповіщення.</li>
          </ul>
        </li>
        <li><strong>Часовий пояс:</strong> Скрипт автоматично використовує часовий пояс вашого облікового запису Google Ads для точних розрахунків.</li>
        <li><strong>Сповіщення:</strong> Якщо будь-яка кампанія виходить за межі визначених порогів, реєструється зведення сповіщень.</li>
        <li><strong>Сповіщення Telegram:</strong> Якщо налаштовано, ці сповіщення (або повідомлення про те, що все добре) надсилаються через Telegram.</li>
      </ul>
      <p className="text-sm text-slate-400 mt-2"><strong>Важливо:</strong> Цей скрипт слід запланувати для частого запуску (наприклад, щогодини) у вашому обліковому записі Google Ads для своєчасного моніторингу. Переконайтеся, що назви кампаній точно збігаються.</p>
    </div>
  ) : (
    <div className="space-y-4">
      <h3 className="text-xl font-semibold text-slate-200">How It Works</h3>
      <ul className="list-disc list-inside space-y-1 text-slate-300">
        <li><strong>Campaign Input:</strong> You provide a list of exact campaign names to monitor (one per line).</li>
        <li><strong>Pacing Calculation:</strong> The script runs (ideally hourly) and calculates the expected spend for the current time of day based on the campaign's daily budget.</li>
        <li><strong>Thresholds:</strong>
          <ul className="list-disc list-inside ml-6 mt-1">
            <li><strong>High Pacing Threshold:</strong> If actual spend exceeds expected spend by this percentage (e.g., 120% means spend is 20% over pace), an alert is triggered.</li>
            <li><strong>Low Pacing Threshold:</strong> If actual spend is below expected spend by this percentage (e.g., 80% means spend is 20% under pace), an alert is triggered.</li>
          </ul>
        </li>
        <li><strong>Timezone:</strong> The script automatically uses your Google Ads account's timezone for accurate calculations.</li>
        <li><strong>Alerts:</strong> If any campaign is pacing outside the defined thresholds, a summary of alerts is logged.</li>
        <li><strong>Telegram Notifications:</strong> If configured, these alerts (or a message indicating all is well) are sent via Telegram.</li>
      </ul>
      <p className="text-sm text-slate-400 mt-2"><strong>Important:</strong> This script should be scheduled to run frequently (e.g., hourly) within your Google Ads account for timely monitoring. Ensure campaign names are exact matches.</p>
    </div>
  );

  if (showResult) {
    return (
      <ToolPageLayout title={pageTitle} description={pageDescription} howThisWorksContent={howThisWorksContent}>
        <div id="scriptDisplaySectionBudgetMon" className="mt-8">
          <h3 className="text-2xl font-semibold text-slate-100 mb-6">{isUkrainian ? 'Згенерований скрипт' : 'Generated Script'}</h3>
          {message && <NotificationMessage type={message.type} message={message.text} />}
          <ScriptDisplay scriptContent={generatedScript} />
          <StyledButton
            onClick={() => {
              setShowResult(false);
              setMessage(null);
            }}
            variant="outline"
            className="mt-8 w-full sm:w-auto"
            themeColor="slate"
            leftIcon={<PlayCircle className="mr-2 h-5 w-5" />}
          >
            Back to Form
          </StyledButton>
        </div>
      </ToolPageLayout>
    );
  }

  return (
    <ToolPageLayout title={pageTitle} description={pageDescription} howThisWorksContent={howThisWorksContent}>
      {message && <NotificationMessage type={message.type} message={message.text} className="mb-6" />}

      <form onSubmit={(e) => { e.preventDefault(); handleGenerateScript(); }} className="space-y-8 mt-8">
        <FormSection title={isUkrainian ? "Вибір кампаній" : "Campaign Selection"} icon={<ListChecks />} theme="sky">
          <FormItem
            label={isUkrainian ? "Назви кампаній для моніторингу" : "Campaign Names to Monitor"}
            htmlFor="campaignNamesInputBudgetMon"
            tooltipText={isUkrainian ? "Введіть одну точну назву кампанії на рядок. Скрипт буде відстежувати кожну з цих кампаній." : "Enter one exact campaign name per line. The script will monitor each of these campaigns."}
            required
          >
            <textarea 
              id="campaignNamesInputBudgetMon" 
              name="campaignNamesInput" 
              value={campaignNamesInput} 
              onChange={handleInputChange} 
              placeholder="Campaign A - Brand - Exact\nSearch - Alpha - US\nShopping - General - UK" 
              rows={4} 
              className={`
                form-textarea block w-full rounded-md 
                bg-slate-700/50 border-slate-600 
                text-gray-100 placeholder-gray-400 
                shadow-sm transition-colors duration-150 ease-in-out 
                focus:border-transparent focus:bg-slate-600/80 focus:outline-none focus:ring-2 focus:ring-sky-500
                px-3 py-2 text-sm
              `}
            />
          </FormItem>
        </FormSection>

        <FormSection title="Пороги темпу (% від очікуваних витрат)" icon={<Gauge />} theme="amber">
          <FormItem
            label="Високий поріг темпу (%) - Сповіщення при перевищенні"
            htmlFor="highPacingThresholdBudgetMon"
            tooltipText="Сповіщення, якщо поточні витрати БІЛЬШЕ цього відсотка від очікуваних витрат на час дня. Наприклад, 120 означає сповіщення, якщо темп на 20% вищий."
            required
          >
            <StyledInput 
              type="number" 
              id="highPacingThresholdBudgetMon" 
              name="highPacingThreshold" 
              value={highPacingThreshold} 
              onChange={handleInputChange} 
              placeholder="120" 
              min="100"
            />
          </FormItem>
          <FormItem 
            label="Низький поріг темпу (%) - Сповіщення при недостачі"
            htmlFor="lowPacingThresholdBudgetMon"
            tooltipText="Сповіщення, якщо поточні витрати МЕНШЕ цього відсотка від очікуваних витрат. Наприклад, 80 означає сповіщення, якщо темп на 20% нижчий."
            required
          >
            <StyledInput 
              type="number" 
              id="lowPacingThresholdBudgetMon" 
              name="lowPacingThreshold" 
              value={lowPacingThreshold} 
              onChange={handleInputChange} 
              placeholder="80" 
              max="100"
            />
          </FormItem>
        </FormSection>

        <FormSection title="Сповіщення Telegram" icon={<Bell />} theme="purple" collapsible initiallyCollapsed={!useTelegram && !telegramBotToken && !telegramChatId}>
          <div className="mb-4">
            <label className="flex items-center cursor-pointer group">
              <div className="relative">
                <input
                  type="checkbox"
                  id="useTelegramBudgetMon"
                  name="useTelegram"
                  checked={useTelegram}
                  onChange={handleInputChange}
                  className="sr-only"
                />
                <div className={`
                  w-5 h-5 rounded border-2 flex items-center justify-center transition-all duration-200
                  ${useTelegram 
                    ? 'bg-sky-600 border-sky-600' 
                    : 'bg-slate-700/50 border-slate-500 group-hover:border-slate-400'}
                  }`}
                >
                  {useTelegram && (
                    <svg className="w-3 h-3 text-white" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
              </div>
              <span className="ml-3 text-gray-300 text-sm font-medium">
                Enable Telegram Notifications
              </span>
            </label>
          </div>
          {useTelegram && (
            <>
              <FormItem label="Токен Telegram бота" htmlFor="telegramBotTokenBudgetMon" tooltipText="Ваш токен Telegram бота." required={useTelegram}>
                <StyledInput type="text" id="telegramBotTokenBudgetMon" name="telegramBotToken" value={telegramBotToken} onChange={handleInputChange} placeholder="123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11" />
              </FormItem>
              <FormItem label="ID чату Telegram" htmlFor="telegramChatIdBudgetMon" tooltipText="Ваш ID чату Telegram." required={useTelegram}>
                <StyledInput type="text" id="telegramChatIdBudgetMon" name="telegramChatId" value={telegramChatId} onChange={handleInputChange} placeholder="-1001234567890 or @channelname" />
              </FormItem>
            </>
          )}
        </FormSection>

        <div className="flex flex-col sm:flex-row gap-4 pt-4">
          <StyledButton type="submit" variant="primary" size="lg" themeColor="emerald" leftIcon={<PlayCircle className="mr-2 h-5 w-5" />}>
            Generate Script
          </StyledButton>
        </div>
      </form>
    </ToolPageLayout>
  );
};

export default BudgetMonitor;
