import React, { useState, useEffect, useCallback } from 'react';
import { Copy, Check, HelpCircle, DollarSign, MessageCircle } from 'lucide-react';
import BackButton from '../common/BackButton';

const MAX_HISTORY_ITEMS = 3;
const historyPrefix = 'gadsBudgetUpdaterTool_'; // Unique prefix for this tool's localStorage items

const GAdsBudgetUpdaterTool: React.FC = () => {
  // Removed: const navigate = useNavigate();
  const [campaignName, setCampaignName] = useState('');
  const [maxBudget, setMaxBudget] = useState('');
  const [maxTimes, setMaxTimes] = useState('');
  const [upPercent, setUpPercent] = useState('');
  const [telegramBotToken, setTelegramBotToken] = useState('');
  const [telegramChatId, setTelegramChatId] = useState('');
  const [campaignType, setCampaignType] = useState('');

  const [generatedScript, setGeneratedScript] = useState('');
  const [showScript, setShowScript] = useState(false);
  const [alert, setAlert] = useState<{ message: string; type: 'success' | 'error' | 'info' | null }>({
    message: '',
    type: null,
  });
  const [showTelegramHelp, setShowTelegramHelp] = useState(false);

  // --- LocalStorage History Functions ---
  const getSavedValues = useCallback((field: string): string[] => {
    try {
      const savedData = localStorage.getItem(`${historyPrefix}${field}_history`);
      return savedData ? JSON.parse(savedData) : [];
    } catch (error) {
      console.error("Error reading from localStorage:", error);
      return [];
    }
  }, []);

  const saveFieldValueToHistory = useCallback((field: string, value: string) => {
    if (!value || !value.trim()) return; // Don't save empty or whitespace-only values
    try {
      let values = getSavedValues(field);
      values = values.filter(v => v !== value);
      values.unshift(value);
      values = values.slice(0, MAX_HISTORY_ITEMS);
      localStorage.setItem(`${historyPrefix}${field}_history`, JSON.stringify(values));
    } catch (error) {
      console.error("Error writing to localStorage:", error);
    }
  }, [getSavedValues]);

  useEffect(() => {
    setCampaignName(getSavedValues('campaignName')[0] || 'Website traffic-Search-1');
    setMaxBudget(getSavedValues('maxBudget')[0] || '1300');
    setMaxTimes(getSavedValues('maxTimes')[0] || '10');
    setUpPercent(getSavedValues('upPercent')[0] || '0.1');
    setTelegramBotToken(getSavedValues('telegramBotToken')[0] || '');
    setTelegramChatId(getSavedValues('telegramChatId')[0] || '');
    setCampaignType(getSavedValues('campaignType')[0] || '');
  }, [getSavedValues]);

  const handleInputChange = (
    setter: React.Dispatch<React.SetStateAction<string>>,
    fieldName: string
  ) => (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setter(value);
    saveFieldValueToHistory(fieldName, value);
  };
  
  const generateRandomName = (prefix = 'customVar') => {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let result = prefix + '_';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  };

  const showAlertMessage = (message: string, type: 'success' | 'error' | 'info') => {
    setAlert({ message, type });
    setTimeout(() => setAlert({ message: '', type: null }), 5000);
  };

  const handleGenerateScript = () => {
    if (!campaignName || !maxBudget || !maxTimes || !upPercent) {
      showAlertMessage('Please fill in all required Google Ads fields.', 'error');
      return;
    }
    if (parseFloat(maxBudget) <= 0 || parseInt(maxTimes, 10) <= 0 || parseFloat(upPercent) <= 0) {
      showAlertMessage('Budget, number of increases, and percentage must be positive values.', 'error');
      return;
    }

    saveFieldValueToHistory('campaignName', campaignName);
    saveFieldValueToHistory('maxBudget', maxBudget);
    saveFieldValueToHistory('maxTimes', maxTimes);
    saveFieldValueToHistory('upPercent', upPercent);
    if (telegramBotToken) saveFieldValueToHistory('telegramBotToken', telegramBotToken);
    if (telegramChatId) saveFieldValueToHistory('telegramChatId', telegramChatId);
    if (campaignType) saveFieldValueToHistory('campaignType', campaignType);

    const campaignNameVar = generateRandomName('targetCampaignNameCfg');
    const maxBudgetVar = generateRandomName('maxBudgetCfg');
    const maxTimesVar = generateRandomName('maxIncrementsCfg');
    const upPercentVar = generateRandomName('incrementPercentCfg');
    const telegramBotTokenVar = generateRandomName('telegramTokenCfg');
    const telegramChatIdVar = generateRandomName('telegramChatIdCfg');
    const campaignTypeVar = generateRandomName('campaignTypeCfg');
    const sendTelegramFunc = generateRandomName('sendTelegramNotificationUtil');
    const findCampaignFunc = generateRandomName('findCampaignUtil');
    const campaignVar = generateRandomName('campaignObj');
    const initialBudgetVar = generateRandomName('initialBudgetVal');
    const incrementCounterVar = generateRandomName('incrementCounter');
    const currentBudgetVar = generateRandomName('currentBudgetVal');
    const newBudgetVar = generateRandomName('newBudgetTarget');
    const finalBudgetVar = generateRandomName('finalBudgetVal');
    const messageVar = generateRandomName('statusMessage');
    const logMsgVar = generateRandomName('logMsg');

    let script = `// Generated by Windsurf AI for GAds Budget Updater Tool
// IMPORTANT: The main() function below is required for Google Ads Scripts to run
function main() {
var ${campaignNameVar} = '${campaignName.replace(/'/g, "\\'")}';
var ${maxBudgetVar} = ${parseFloat(maxBudget)};
var ${maxTimesVar} = ${parseInt(maxTimes, 10)};
var ${upPercentVar} = ${parseFloat(upPercent)};
var ${telegramBotTokenVar} = '${telegramBotToken.replace(/'/g, "\\'")}';
var ${telegramChatIdVar} = '${telegramChatId.replace(/'/g, "\\'")}';
var ${campaignTypeVar} = '${campaignType.replace(/'/g, "\\'")}';

function ${sendTelegramFunc}(${logMsgVar}) {
  if (!${telegramBotTokenVar} || !${telegramChatIdVar}) {
    Logger.log("Telegram Bot Token or Chat ID not specified. Notification skipped.");
    return;
  }
  var apiUrlString = "MAGIC_PLACEHOLDER_api.telegram.org/bot"; 
  var actualTelegramUrl = apiUrlString.replace("MAGIC_PLACEHOLDER_", "https://") + ${telegramBotTokenVar} + "/sendMessage";
  
  var payload = {
    'chat_id': ${telegramChatIdVar},
    'text': ${logMsgVar},
    'parse_mode': 'HTML' 
  };
  var options = {
    'method': 'post',
    'contentType': 'application/json',
    'payload': JSON.stringify(payload),
    'muteHttpExceptions': true
  };
  try {
    var response = UrlFetchApp.fetch(actualTelegramUrl, options);
    Logger.log("Attempted to send Telegram notification. Response Code: " + response.getResponseCode() + ". Response: " + response.getContentText().substring(0, 500));
  } catch (e) {
    Logger.log("Error sending Telegram notification: " + e.toString());
  }
}

function ${findCampaignFunc}(name) {
  var selectors = [];
  if (${campaignTypeVar}) {
    Logger.log('Prioritizing search for campaign type: ' + ${campaignTypeVar});
    if (${campaignTypeVar} === 'PERFORMANCE_MAX') {
      try { selectors.push({ selector: AdsApp.performanceMaxCampaigns(), type: 'Performance Max' }); Logger.log('Successfully added Performance Max campaign selector'); } catch(e){ Logger.log('No access to Performance Max campaigns: ' + e); }
    } else if (${campaignTypeVar} === 'VIDEO') {
      try { selectors.push({ selector: AdsApp.videoCampaigns(), type: 'Video' }); Logger.log('Successfully added Video campaign selector'); } catch(e){ Logger.log('No access to Video campaigns: ' + e); }
    } else if (${campaignTypeVar} === 'SHOPPING') {
      try { selectors.push({ selector: AdsApp.shoppingCampaigns(), type: 'Shopping' }); Logger.log('Successfully added Shopping campaign selector'); } catch(e){ Logger.log('No access to Shopping campaigns: ' + e); }
    } else if (${campaignTypeVar} === 'DEMAND_GEN') {
      try { selectors.push({ selector: AdsApp.campaigns().withCondition("AdvertisingChannelSubType = DEMAND_GEN"), type: 'Demand Gen' }); Logger.log('Successfully added Demand Gen campaign selector'); } catch(e){ Logger.log('No access to Demand Gen campaigns: ' + e); }
    } else {
      try { selectors.push({ selector: AdsApp.campaigns().withCondition("CampaignType = " + ${campaignTypeVar}), type: ${campaignTypeVar} }); Logger.log('Successfully added ' + ${campaignTypeVar} + ' campaign selector'); } catch(e){ Logger.log('No access to ' + ${campaignTypeVar} + ' campaigns: ' + e); }
    }
  } else {
    Logger.log('No specific campaign type requested. Searching across all campaign types.');
    try { selectors.push({ selector: AdsApp.performanceMaxCampaigns(), type: 'Performance Max' }); } catch(e){ Logger.log('No access to PMax: ' + e); }
    try { selectors.push({ selector: AdsApp.videoCampaigns(), type: 'Video' }); } catch(e){  Logger.log('No access to Video: ' + e); }
    try { selectors.push({ selector: AdsApp.campaigns().withCondition("AdvertisingChannelSubType = DEMAND_GEN"), type: 'Demand Gen' }); } catch(e){ Logger.log('No access to DemandGen: ' + e); }
    try { selectors.push({ selector: AdsApp.shoppingCampaigns(), type: 'Shopping' }); } catch(e){ Logger.log('No access to Shopping: ' + e); }
    try { selectors.push({ selector: AdsApp.campaigns().withCondition("CampaignType = DISPLAY"), type: 'Display' }); } catch(e){ Logger.log('No access to Display: ' + e); }
    try { selectors.push({ selector: AdsApp.campaigns().withCondition("CampaignType = SEARCH"), type: 'Search' }); } catch(e){ Logger.log('No access to Search: ' + e); }
    try { selectors.push({ selector: AdsApp.campaigns().withCondition("CampaignType = SMART"), type: 'Smart' }); } catch(e){ Logger.log('No access to Smart: ' + e); }
    try { selectors.push({ selector: AdsApp.campaigns().withCondition("CampaignType = HOTEL"), type: 'Hotel' }); } catch(e){ Logger.log('No access to Hotel: ' + e); }
  }
  try { selectors.push({ selector: AdsApp.campaigns(), type: 'Other/General' }); } catch(e){ Logger.log('No access to AdsApp.campaigns(): ' + e); }

  var nameVariations = [name, name.replace(/-/g, ' '), name.replace(/\\"/g, ''), name.replace(/"/g, '')];
  nameVariations = nameVariations.filter(function(item, pos) { return nameVariations.indexOf(item) == pos; });
  Logger.log('Searching for campaign with name variations: ' + JSON.stringify(nameVariations));

  for (var v = 0; v < nameVariations.length; v++) {
    var currentName = nameVariations[v];
    Logger.log('Trying name variation: "' + currentName + '"');
    for (var i = 0; i < selectors.length; i++) {
      try {
        var campaignIterator = selectors[i].selector
          .withCondition('CampaignName = "' + currentName.replace(/"/g, '\\"') + '"')
          .withLimit(1).get();
        if (campaignIterator.hasNext()) {
          var foundCampaign = campaignIterator.next();
          Logger.log('Campaign "' + foundCampaign.getName() + '" found with type: ' + selectors[i].type);
          return foundCampaign;
        }
      } catch (e) { /* Logger.log('Error searching in type ' + selectors[i].type + ': ' + e); */ }
    }
  }
  
  Logger.log('Exact match failed. Trying case-insensitive and partial matching...');
  try {
    var allCampaignsIterator = AdsApp.campaigns().get();
    while (allCampaignsIterator.hasNext()) {
      var c = allCampaignsIterator.next();
      var cName = c.getName();
      for (var v_partial = 0; v_partial < nameVariations.length; v_partial++) {
        var variationLower = nameVariations[v_partial].toLowerCase();
        var cNameLower = cName.toLowerCase();
        if (cNameLower === variationLower) {
          Logger.log('Found campaign by case-insensitive match: "' + cName + '"');
          return c;
        }
        if (cNameLower.indexOf(variationLower) !== -1 || variationLower.indexOf(cNameLower) !== -1) {
           Logger.log('Found campaign by partial match (contains/contained by): "' + cName + '" with variation "' + nameVariations[v_partial] + '"');
           return c;
        }
      }
    }
  } catch (e) { Logger.log('Error during case-insensitive/partial search: ' + e.toString()); }

  var campaignList = 'Available campaigns (first 10): ';
  var count = 0;
  try {
    var allCamps = AdsApp.campaigns().get();
    while(allCamps.hasNext() && count < 10) {
      var camp = allCamps.next();
      campaignList += '\\n- "' + camp.getName() + '" (ID: ' + camp.getId() + ')';
      count++;
    }
    if (allCamps.hasNext()) campaignList += '\\n- ... and more.';
  } catch(e) { campaignList = "Could not retrieve campaign list.";}
  
  throw new Error('Campaign not found: "' + name + '". Please check the name and type. ' + campaignList);
}

function main() {
  var ${campaignVar};
  var campaignTypeDetected = "Unknown";
  
  try {
    ${campaignVar} = ${findCampaignFunc}(${campaignNameVar});
    try {
      if (${campaignVar}.getCampaignType) { campaignTypeDetected = ${campaignVar}.getCampaignType(); }
      else if (${campaignVar}.getEntityType && ${campaignVar}.getEntityType() === "PerformanceMaxCampaign") { campaignTypeDetected = "Performance Max"; }
      else if (${campaignVar}.getEntityType && ${campaignVar}.getEntityType().includes("Video")) { campaignTypeDetected = "Video"; }
    } catch (typeErr) { Logger.log("Could not determine campaign type: " + typeErr); }
  } catch (e) {
    Logger.log("Error finding campaign: " + e.toString());
    ${sendTelegramFunc}("❌ <b>Google Ads Script Error:</b>\\nFailed to find campaign: " + ${campaignNameVar} + "\\nError: " + e.toString().substring(0, 500));
    return;
  }

  var ${initialBudgetVar} = ${campaignVar}.getBudget().getAmount();
  var ${incrementCounterVar} = 0;
  Logger.log("Campaign: '" + ${campaignVar}.getName() + "'. Initial budget: " + ${initialBudgetVar}.toFixed(2));
  
  while (${incrementCounterVar} < ${maxTimesVar} && ${campaignVar}.getBudget().getAmount() < ${maxBudgetVar}) {
    var ${currentBudgetVar} = ${campaignVar}.getBudget().getAmount();
    var ${newBudgetVar} = ${currentBudgetVar} * (1 + ${upPercentVar});
    if (${newBudgetVar} > ${maxBudgetVar}) { ${newBudgetVar} = ${maxBudgetVar}; }

    if (parseFloat(${newBudgetVar}.toFixed(2)) <= parseFloat(${currentBudgetVar}.toFixed(2))) {
        Logger.log("New calculated budget (" + ${newBudgetVar}.toFixed(2) + ") is not greater than current budget (" + ${currentBudgetVar}.toFixed(2) + "). Stopping increase.");
        break;
    }
    
    ${campaignVar}.getBudget().setAmount(${newBudgetVar});
    ${incrementCounterVar}++;
    Logger.log("Iteration " + ${incrementCounterVar} + ": Budget set to " + ${campaignVar}.getBudget().getAmount().toFixed(2));
    
    if (${campaignVar}.getBudget().getAmount() >= ${maxBudgetVar}) {
      Logger.log("Maximum budget reached or exceeded.");
      break;
    }
  }
  
  var ${finalBudgetVar} = ${campaignVar}.getBudget().getAmount();
  var ${messageVar};
  
  if (${incrementCounterVar} > 0) {
    ${messageVar} = "✅ <b>Google Ads Script: Budget Updated</b>\\n" +
               "Campaign: <b>'" + ${campaignVar}.getName() + "'</b> (Target: '"+${campaignNameVar}+"')\\n" +
               "Type: " + campaignTypeDetected + "\\n" +
               "Old Budget: " + ${initialBudgetVar}.toFixed(2) + "\\n" +
               "New Budget: " + ${finalBudgetVar}.toFixed(2) + "\\n" +
               "Increments: " + ${incrementCounterVar};
    if (${incrementCounterVar} >= ${maxTimesVar} && ${finalBudgetVar} < ${maxBudgetVar}) {
      ${messageVar} += "\\nReached max increments limit (" + ${maxTimesVar} + ").";
    }
    if (${finalBudgetVar} >= ${maxBudgetVar}) {
      ${messageVar} += "\\nReached max budget limit (" + ${maxBudgetVar}.toFixed(2) + ").";
    }
  } else if (${initialBudgetVar} >= ${maxBudgetVar}) {
     ${messageVar} = "ℹ️ <b>Google Ads Script: Budget Not Changed</b>\\n" +
               "Campaign: <b>'" + ${campaignVar}.getName() + "'</b>\\n" +
               "Type: " + campaignTypeDetected + "\\n" +
               "Current Budget: " + ${finalBudgetVar}.toFixed(2) + "\\n" +
               "Reason: Budget is already at or above max target (" + ${maxBudgetVar}.toFixed(2) + ").";
  } else {
     ${messageVar} = "ℹ️ <b>Google Ads Script: Budget Not Changed</b>\\n" +
               "Campaign: <b>'" + ${campaignVar}.getName() + "'</b>\\n" +
               "Type: " + campaignTypeDetected + "\\n" +
               "Current Budget: " + ${finalBudgetVar}.toFixed(2) + "\\n" +
               "Reason: No increase was made. Check logs.";
  }
  
  Logger.log(${messageVar}.replace(/<b>/g, "").replace(/<b>/g, ""));
  ${sendTelegramFunc}(${messageVar});
}
}
`;

    // Remove comments but preserve the main function and structure
    script = script.replace(/[/][/][^\n]*/g, '');          // For single-line comments only (not removing line breaks)
    script = script.replace(/[/]\*[\s\S]*?\*[/]/g, ''); // For multi-line comments
    script = script.replace(/^\s*$(?:\r\n?|\n)/gm, ''); // Remove empty lines
    
    // Ensure the main function is present and properly formatted
    if (!script.includes('function main()')) {
      console.error('Main function is missing from the generated script!');
    }

    setGeneratedScript(script);
    setShowScript(true);
    showAlertMessage('Script generated successfully!', 'success');
  };

  const handleCopyScript = async () => {
    if (!generatedScript) return;
    const copyButton = document.querySelector('#copyBudgetScriptButton'); // Unique ID for this tool's button
    if (copyButton) {
      copyButton.classList.add('animate-pulse', 'bg-green-500');
      setTimeout(() => {
        copyButton.classList.remove('animate-pulse', 'bg-green-500');
      }, 1000);
    }
    try {
      await navigator.clipboard.writeText(generatedScript);
      showAlertMessage('Script copied to clipboard!', 'success');
    } catch (err) {
      showAlertMessage('Failed to copy script.', 'error');
      console.error('Failed to copy script: ', err);
    }
  };

  const renderInputWithHistory = (
    id: string,
    label: string,
    value: string,
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void,
    type = 'text',
    placeholder = '',
    required = false,
    min?: string,
    max?: string,
    step?: string
  ) => {
    const historyValues = getSavedValues(id);
    return (
      <div className="mb-4">
        <label htmlFor={id} className="block text-sm font-medium text-gray-700 mb-1">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
        <div className="relative">
          <input
            type={type}
            id={id}
            name={id}
            value={value}
            onChange={onChange}
            placeholder={placeholder}
            required={required}
            min={min}
            max={max}
            step={step}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
          />
          {historyValues.length > 0 && (
            <select
              onChange={(e) => {
                const selectedValue = e.target.value;
                if (selectedValue) {
                  if (id === 'campaignName') setCampaignName(selectedValue);
                  else if (id === 'maxBudget') setMaxBudget(selectedValue);
                  else if (id === 'maxTimes') setMaxTimes(selectedValue);
                  else if (id === 'upPercent') setUpPercent(selectedValue);
                  else if (id === 'telegramBotToken') setTelegramBotToken(selectedValue);
                  else if (id === 'telegramChatId') setTelegramChatId(selectedValue);
                }
              }}
              className="absolute right-0 top-0 bottom-0 w-auto bg-gray-50 border-l border-gray-300 text-gray-700 text-xs rounded-r-md hover:bg-gray-100 focus:outline-none p-2 cursor-pointer"
              title="Load from history"
              defaultValue=""
            >
              <option value="" disabled>History</option>
              {historyValues.map((histVal, index) => (
                <option key={index} value={histVal}>{histVal.length > 20 ? histVal.substring(0,17) + '...' : histVal}</option>
              ))}
            </select>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="w-full">
      <BackButton />
      {alert?.type && (
        <div className={`p-4 mb-4 text-sm rounded-lg ${
          alert.type === 'success' ? 'bg-green-100 border border-green-400 text-green-700' : 
          alert.type === 'error' ? 'bg-red-100 border border-red-400 text-red-700' : 
          'bg-blue-100 border border-blue-400 text-blue-700'
        }`} role="alert">
          <span className="font-medium">{alert.type ? `${alert.type.charAt(0).toUpperCase()}${alert.type.slice(1)}` : ''}:</span> {alert.message}
        </div>
      )}

      {!showScript ? (
        <form onSubmit={(e) => e.preventDefault()}>
          <div className="bg-blue-50 p-5 rounded-lg shadow-inner border-2 border-blue-200 mb-6">
            <h4 className="text-lg font-semibold text-blue-700 mb-4 flex items-center">
              <DollarSign size={22} className="mr-2" />
              Google Ads Settings
            </h4>
            {renderInputWithHistory('campaignName', 'Campaign Name (Exact match from Google Ads)', campaignName, handleInputChange(setCampaignName, 'campaignName'), 'text', 'e.g., My Awesome Campaign - Search', true)}
            
            <div className="mb-4">
              <label htmlFor="campaignType" className="block text-sm font-medium text-gray-700 mb-1">
                Campaign Type (Optional)
              </label>
              <select
                id="campaignType"
                name="campaignType"
                value={campaignType}
                onChange={(e) => {
                  setCampaignType(e.target.value);
                  saveFieldValueToHistory('campaignType', e.target.value);
                }}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="">Any Type</option>
                <option value="PERFORMANCE_MAX">Performance Max</option>
                <option value="SEARCH">Search</option>
                <option value="DISPLAY">Display</option>
                <option value="VIDEO">Video</option>
                <option value="SHOPPING">Shopping</option>
                <option value="DEMAND_GEN">Demand Gen</option>
              </select>
              <p className="mt-1 text-xs text-gray-500">
                Optional: Filter to only update campaigns of this type. Leave empty to update any campaign type.
              </p>
            </div>
            
            {renderInputWithHistory('maxBudget', 'Max Daily Budget (in account currency)', maxBudget, handleInputChange(setMaxBudget, 'maxBudget'), 'number', 'e.g., 1000', true, "1", undefined, "any")}
            {renderInputWithHistory('maxTimes', 'Max Budget Increases (per script run)', maxTimes, handleInputChange(setMaxTimes, 'maxTimes'), 'number', 'e.g., 5', true, "1", undefined, "1")}
            {renderInputWithHistory('upPercent', 'Budget Increase Percentage (e.g., 0.1 for 10%)', upPercent, handleInputChange(setUpPercent, 'upPercent'), 'number', 'e.g., 0.1', true, "0.01", "2", "0.01")}
          </div>

          <div className="bg-purple-50 p-5 rounded-lg shadow-inner border-2 border-purple-200 mb-6">
            <h4 className="text-lg font-semibold text-purple-700 mb-4 flex items-center">
              <MessageCircle size={22} className="mr-2" />
              Telegram Settings (Optional)
            </h4>
            <p className="text-sm text-purple-600 mb-3">Leave fields empty if you do not want Telegram notifications.</p>
            {renderInputWithHistory('telegramBotToken', 'Telegram Bot Token', telegramBotToken, handleInputChange(setTelegramBotToken, 'telegramBotToken'), 'text', 'e.g., 123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11')}
            {renderInputWithHistory('telegramChatId', 'Telegram Chat ID', telegramChatId, handleInputChange(setTelegramChatId, 'telegramChatId'), 'text', 'e.g., -100123456789 or 987654321')}
            
            <div className="mt-4">
                <button
                    type="button"
                    onClick={() => setShowTelegramHelp(!showTelegramHelp)}
                    className="text-sm text-primary-600 hover:text-primary-700 focus:outline-none"
                >
                    {showTelegramHelp ? 'Hide' : 'Show'} How to get Telegram Bot Token and Chat ID
                </button>
                {showTelegramHelp && (
                    <div className="mt-2 p-3 bg-gray-50 rounded-md text-xs text-gray-700 border">
                        <h5 className="font-semibold mb-1">To get Bot Token:</h5>
                        <ol className="list-decimal list-inside ml-4 mb-2">
                            <li>In Telegram, search for BotFather.</li>
                            <li>Send it the command <code>/newbot</code>.</li>
                            <li>Follow instructions to create a new bot.</li>
                            <li>You will receive a token for your bot.</li>
                        </ol>
                        <h5 className="font-semibold mb-1">To get Chat ID:</h5>
                        <ol className="list-decimal list-inside ml-4">
                            <li><strong>For personal chat:</strong> Find @userinfobot (or @myidbot), send <code>/start</code>. It will reply with your ID.</li>
                            <li><strong>For group chat:</strong>
                                <ul className="list-disc list-inside ml-4">
                                    <li>Add your newly created bot to the group.</li>
                                    <li>Send any message to the group.</li>
                                    <li>Open in browser (replace <code>&lt;YourBotToken&gt;</code>):<br /><code>https://api.telegram.org/bot&lt;YourBotToken&gt;/getUpdates</code></li>
                                    <li>Find the JSON object for your message. Chat ID is <code>chat.id</code> (usually negative for groups).</li>
                                </ul>
                            </li>
                        </ol>
                    </div>
                )}
            </div>
          </div>
          
          <button
            type="button"
            onClick={handleGenerateScript}
            className="w-full bg-sky-600 text-white font-semibold py-3 px-4 rounded-md hover:bg-sky-700 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:ring-offset-2 transition-colors"
          >
            Generate Script
          </button>
        </form>
      ) : (
        <div id="resultSectionBudgetUpdater">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-neutral-800">Your Google Ads Budget Updater Script</h2>
            <button 
              onClick={handleCopyScript}
              id="copyBudgetScriptButton"
              className={`px-3 py-1.5 text-sm font-medium rounded-md transition-colors flex items-center 
                          ${showScript && generatedScript ? 'bg-green-100 text-green-700 focus:ring-green-500' : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200 focus:ring-sky-500'}`}
            >
              {showScript && generatedScript ? <Check size={16} className="mr-1.5" /> : <Copy size={16} className="mr-1.5" />}
              {showScript && generatedScript ? 'Copied!' : 'Copy Script'}
            </button>
          </div>
          <pre className="bg-neutral-800 text-neutral-200 p-4 rounded-md overflow-auto h-[400px] text-xs">
            <code>{generatedScript}</code>
          </pre>
          <button 
            onClick={() => {setShowScript(false); setAlert({ message: '', type: null }); }}
            className="mt-4 w-full bg-sky-600 text-white font-semibold py-2 px-4 rounded-md hover:bg-sky-700 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:ring-offset-2 transition-colors"
            > 
            Back to Form
          </button>
        </div>
      )}
      
      {/* Instructions and Help Sections */} 
      <div className="mt-8 space-y-6">
        <div className="bg-white p-5 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-neutral-800 mb-3 flex items-center"><HelpCircle size={20} className="mr-2 text-sky-600"/>How it works</h3>
          <p className="text-neutral-600 mb-2">
            This tool generates a unique Google Ads script to automatically increase the budget of a specified campaign and (optionally) send Telegram notifications.
          </p>
        </div>
        
        <div className="bg-white p-5 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-neutral-800 mb-2">Supported Campaign Types</h3>
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-neutral-700 mb-1">Campaign Types:</h4>
              <ul className="list-disc list-inside text-neutral-600 text-sm space-y-1">
                <li>Performance Max (including PMax for retail)</li>
                <li>Video Campaigns</li>
                <li>Shopping Campaigns (standard and PMax for retail)</li>
                <li>Display Campaigns (including Smart Display)</li>
                <li>Search Campaigns</li>
                <li>Smart Campaigns</li>
                <li>Hotel Campaigns</li>
                <li>Demand Gen Campaigns</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-neutral-700 mb-1">Additional Information:</h4>
              <p className="text-neutral-600 text-sm">
                The script automatically detects campaign types. For best results, use the exact campaign name. The script will try variations if an exact match isn't found.
              </p>
              <p className="text-neutral-600 text-sm mt-2">
                App Campaigns might be found if accessible via <code>AdsApp.campaigns()</code>, but no specific selector is used for them.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GAdsBudgetUpdaterTool;
