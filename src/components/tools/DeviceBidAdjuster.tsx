import React, { useState, useEffect } from 'react';
import { Smartphone, Monitor, Bell } from 'lucide-react';

import ToolPageLayout from '../ui/shared/ToolPageLayout';
import FormSection from '../ui/shared/FormSection';
import FormItem from '../ui/shared/FormItem';
import StyledInput from '../ui/shared/StyledInput';
import StyledSelect from '../ui/shared/StyledSelect';
import StyledButton from '../ui/shared/StyledButton';
import ScriptDisplay from '../ui/shared/ScriptDisplay';
import NotificationMessage from '../ui/shared/NotificationMessage';

const LOCAL_STORAGE_PREFIX = 'deviceBidAdjuster_';

const GOOGLE_ADS_DATE_RANGES = [
  { value: 'TODAY', label: 'Today' },
  { value: 'YESTERDAY', label: 'Yesterday' },
  { value: 'LAST_7_DAYS', label: 'Last 7 Days' },
  { value: 'LAST_WEEK_SUN_SAT', label: 'Last Week (Sun-Sat)' },
  { value: 'LAST_BUSINESS_WEEK', label: 'Last Business Week (Mon-Fri)' },
  { value: 'THIS_MONTH', label: 'This Month' },
  { value: 'LAST_MONTH', label: 'Last Month' },
  { value: 'LAST_14_DAYS', label: 'Last 14 Days' },
  { value: 'LAST_30_DAYS', label: 'Last 30 Days' },
  { value: 'LAST_90_DAYS', label: 'Last 90 Days' },
  { value: 'THIS_WEEK_SUN_TODAY', label: 'This Week (Sun-Today)' },
  { value: 'THIS_WEEK_MON_TODAY', label: 'This Week (Mon-Today)' },
  { value: 'LAST_WEEK_MON_SUN', label: 'Last Week (Mon-Sun)' },
];

const DeviceBidAdjuster: React.FC = () => {
  // Form inputs
  const [campaignNamePattern, setCampaignNamePattern] = useState('');
  const [mobileCpcThreshold, setMobileCpcThreshold] = useState('1.5');
  const [desktopCpcThreshold, setDesktopCpcThreshold] = useState('2.0');
  const [tabletCpcThreshold, setTabletCpcThreshold] = useState('1.8');
  const [adjustmentPercentage, setAdjustmentPercentage] = useState('10');
  const [dateRange, setDateRange] = useState('LAST_30_DAYS');

  // Telegram
  const [useTelegram, setUseTelegram] = useState(false);
  const [telegramBotToken, setTelegramBotToken] = useState('');
  const [telegramChatId, setTelegramChatId] = useState('');

  // UI State
  const [generatedScript, setGeneratedScript] = useState('');
  const [showResult, setShowResult] = useState(false);
  // const [copySuccess, setCopySuccess] = useState(false); // Handled by ScriptDisplay
  const [message, setMessage] = useState<{text: string, type: 'error' | 'success' | 'info'} | null>(null);

  // Load saved form data from localStorage
  useEffect(() => {
    const fields = [
      { key: 'campaignNamePattern', setter: setCampaignNamePattern, type: 'string' },
      { key: 'mobileCpcThreshold', setter: setMobileCpcThreshold, type: 'string' },
      { key: 'desktopCpcThreshold', setter: setDesktopCpcThreshold, type: 'string' },
      { key: 'tabletCpcThreshold', setter: setTabletCpcThreshold, type: 'string' },
      { key: 'adjustmentPercentage', setter: setAdjustmentPercentage, type: 'string' },
      { key: 'dateRange', setter: setDateRange, type: 'string' },
      { key: 'useTelegram', setter: setUseTelegram, type: 'boolean' },
      { key: 'telegramBotToken', setter: setTelegramBotToken, type: 'string' },
      { key: 'telegramChatId', setter: setTelegramChatId, type: 'string' },
    ];

    fields.forEach(field => {
      const savedValue = localStorage.getItem(`${LOCAL_STORAGE_PREFIX}${field.key}`);
      if (savedValue !== null) {
        if (field.type === 'boolean') {
          (field.setter as React.Dispatch<React.SetStateAction<boolean>>)(savedValue === 'true');
        } else {
          (field.setter as React.Dispatch<React.SetStateAction<string>>)(savedValue);
        }
      }
    });
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    setMessage(null);
    const { name, value, type } = e.target;
    const val = type === 'checkbox' ? (e.target as HTMLInputElement).checked : value;

    const fieldSetters: Record<string,
      React.Dispatch<React.SetStateAction<string>> | React.Dispatch<React.SetStateAction<boolean>>
    > = {
      campaignNamePattern: setCampaignNamePattern,
      mobileCpcThreshold: setMobileCpcThreshold,
      desktopCpcThreshold: setDesktopCpcThreshold,
      tabletCpcThreshold: setTabletCpcThreshold,
      adjustmentPercentage: setAdjustmentPercentage,
      dateRange: setDateRange,
      useTelegram: setUseTelegram,
      telegramBotToken: setTelegramBotToken,
      telegramChatId: setTelegramChatId,
    };

    if (fieldSetters[name]) {
      const setter = fieldSetters[name];
      if (type === 'checkbox') {
        (setter as React.Dispatch<React.SetStateAction<boolean>>)((e.target as HTMLInputElement).checked);
      } else {
        (setter as React.Dispatch<React.SetStateAction<string>>)(value);
      }
      localStorage.setItem(`${LOCAL_STORAGE_PREFIX}${name}`, String(val));
    }
  };

  const handleGenerateScript = () => {
    setMessage(null);

    if (useTelegram && (!telegramBotToken || !telegramChatId)) {
      setMessage({ text: 'Please enter both Telegram Bot Token and Chat ID for notifications.', type: 'error' });
      return;
    }
    const mobileTh = parseFloat(mobileCpcThreshold);
    const desktopTh = parseFloat(desktopCpcThreshold);
    const tabletTh = parseFloat(tabletCpcThreshold);
    const adjPercent = parseFloat(adjustmentPercentage);

    if (isNaN(mobileTh) || mobileTh <=0 ||
        isNaN(desktopTh) || desktopTh <=0 ||
        isNaN(tabletTh) || tabletTh <=0 ) {
        setMessage({ text: 'Device CPC Thresholds must be positive numbers.', type: 'error' });
        return;
    }
    if (isNaN(adjPercent) || adjPercent <=0 || adjPercent > 90) {
        setMessage({ text: 'Adjustment Percentage must be between 1 and 90 (e.g., enter 10 for 10%).', type: 'error' });
        return;
    }

    const script = generateDeviceBidAdjusterScript(
      campaignNamePattern,
      mobileTh,
      desktopTh,
      tabletTh,
      adjPercent,
      dateRange,
      useTelegram,
      telegramBotToken,
      telegramChatId
    );
    setGeneratedScript(script);
    setShowResult(true);
    setTimeout(() => {
      document.getElementById('scriptDisplaySectionDeviceBid')?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  };

  // handleCopyScript removed - handled by ScriptDisplay

  const generateDeviceBidAdjusterScript = (
    campaignPattern: string, mobileTh: number, desktopTh: number, tabletTh: number,
    adjPercent: number, dateRangeVal: string, useTelegramVal: boolean, botToken: string, chatId: string
  ): string => {
    const uniquePrefix = 'devBidAdj' + Date.now().toString(36) + '_';
    const scriptName = 'Device Bid Adjuster';

    let telegramCode = '';
    if (useTelegramVal) {
      const cleanBotToken = botToken.replace(/[^a-zA-Z0-9:_-]/g, '').trim();
      const cleanChatId = chatId.replace(/[^0-9-]/g, '').trim();
      
      telegramCode = `
function ${uniquePrefix}sendTelegramNotification(message) {
  var telegramUrl = 'https://api.telegram.org/bot${cleanBotToken}/sendMessage';
  var ${uniquePrefix}payload = {
    'chat_id': '${cleanChatId}',
    'text': message,
    'parse_mode': 'HTML',
    'disable_web_page_preview': true
  };
  var ${uniquePrefix}options = {
    'method': 'post',
    'contentType': 'application/json',
    'payload': JSON.stringify(${uniquePrefix}payload),
    'muteHttpExceptions': true
  };
  try {
    var response = UrlFetchApp.fetch(telegramUrl, ${uniquePrefix}options);
    Logger.log('Telegram notification sent. Response code: ' + response.getResponseCode());
    Logger.log('Response content: ' + response.getContentText().substring(0, 500));
  } catch (e) {
    Logger.log('Telegram Error: ' + e.toString() + ' | URL: ' + telegramUrl + ' | Payload: ' + JSON.stringify(${uniquePrefix}payload).substring(0,500));
  }
}
`;
    }

    const mainScript = `
/* @ts-ignore */
function main() {
  var ${uniquePrefix}CONFIG = {
    SCRIPT_NAME: '${scriptName}',
    CAMPAIGN_NAME_CONTAINS: "${campaignPattern.replace(/'/g, "\\'")}",
    MOBILE_CPC_THRESHOLD: ${mobileTh},
    DESKTOP_CPC_THRESHOLD: ${desktopTh},
    TABLET_CPC_THRESHOLD: ${tabletTh},
    ADJUSTMENT_PERCENTAGE: ${adjPercent / 100},
    DATE_RANGE: "${dateRangeVal}",
    BID_INCREASE_THRESHOLD_FACTOR: 0.70,
    MIN_BID_MODIFIER: 0.1,
    MAX_BID_MODIFIER: 10.0,
    MIN_CLICKS_FOR_ACTION: 1,
    USE_TELEGRAM: ${useTelegramVal}
  };

  var ${uniquePrefix}accountName = AdsApp.currentAccount().getName();
  var ${uniquePrefix}accountId = AdsApp.currentAccount().getCustomerId();
  var ${uniquePrefix}executionDate = new Date().toLocaleDateString('en-US', { timeZone: AdsApp.currentAccount().getTimeZone() });
  var ${uniquePrefix}resultsSummary = [];
  var ${uniquePrefix}errorOccurred = false;
  var ${uniquePrefix}errorMessage = '';

  Logger.log('Starting ${uniquePrefix}CONFIG.SCRIPT_NAME for account: ' + ${uniquePrefix}accountName + ' (' + ${uniquePrefix}accountId + ')');
  Logger.log('Date Range for Stats: ' + ${uniquePrefix}CONFIG.DATE_RANGE);
  Logger.log('Campaign Name Pattern: ' + ${uniquePrefix}CONFIG.CAMPAIGN_NAME_CONTAINS);
  Logger.log('Mobile CPC Threshold: ' + ${uniquePrefix}CONFIG.MOBILE_CPC_THRESHOLD);
  Logger.log('Desktop CPC Threshold: ' + ${uniquePrefix}CONFIG.DESKTOP_CPC_THRESHOLD);
  Logger.log('Tablet CPC Threshold: ' + ${uniquePrefix}CONFIG.TABLET_CPC_THRESHOLD);
  Logger.log('Adjustment Percentage: ' + (${uniquePrefix}CONFIG.ADJUSTMENT_PERCENTAGE * 100) + '%');

  try {
    var ${uniquePrefix}campaignIterator = AdsApp.campaigns()
      .withCondition("Name CONTAINS '" + ${uniquePrefix}CONFIG.CAMPAIGN_NAME_CONTAINS + "'")
      .withCondition("Status = ENABLED")
      .get();

    if (!${uniquePrefix}campaignIterator.hasNext()) {
      ${uniquePrefix}resultsSummary.push('No campaigns found matching the pattern: "' + ${uniquePrefix}CONFIG.CAMPAIGN_NAME_CONTAINS + '"');
    } else {
      var ${uniquePrefix}campaignsProcessed = 0;
      var ${uniquePrefix}adjustmentsMade = 0;

      while (${uniquePrefix}campaignIterator.hasNext()) {
        var ${uniquePrefix}campaign = ${uniquePrefix}campaignIterator.next();
        ${uniquePrefix}campaignsProcessed++;
        Logger.log('Processing campaign: ' + ${uniquePrefix}campaign.getName());
        ${uniquePrefix}resultsSummary.push('Campaign: ' + ${uniquePrefix}campaign.getName());

        var ${uniquePrefix}stats = ${uniquePrefix}campaign.getStatsFor(${uniquePrefix}CONFIG.DATE_RANGE);
        var ${uniquePrefix}devices = [
          { name: 'HighEndMobile', platformName: 'HighEndMobile', cpcThreshold: ${uniquePrefix}CONFIG.MOBILE_CPC_THRESHOLD, displayName: 'Mobile' },
          { name: 'Desktop', platformName: 'Desktop', cpcThreshold: ${uniquePrefix}CONFIG.DESKTOP_CPC_THRESHOLD, displayName: 'Desktop' }, 
          { name: 'Tablet', platformName: 'Tablet', cpcThreshold: ${uniquePrefix}CONFIG.TABLET_CPC_THRESHOLD, displayName: 'Tablet' }
        ];

        for (var i = 0; i < ${uniquePrefix}devices.length; i++) {
          var ${uniquePrefix}device = ${uniquePrefix}devices[i];
          var ${uniquePrefix}deviceStats = ${uniquePrefix}campaign.getStatsFor(${uniquePrefix}CONFIG.DATE_RANGE, { device: ${uniquePrefix}device.name }); // Note: 'device' segmentation might be deprecated for 'platform'
          var ${uniquePrefix}cpc = ${uniquePrefix}deviceStats.getCpc();
          var ${uniquePrefix}clicks = ${uniquePrefix}deviceStats.getClicks();
          var ${uniquePrefix}currentBidModifier = 1.0; // Default if no specific targeting

          var ${uniquePrefix}platformTargetingIterator = ${uniquePrefix}campaign.targeting().platforms()
            .withCondition("PlatformName = '" + ${uniquePrefix}device.platformName + "'")
            .get();

          var ${uniquePrefix}platformTargeting = null;
          if (${uniquePrefix}platformTargetingIterator.hasNext()) {
            ${uniquePrefix}platformTargeting = ${uniquePrefix}platformTargetingIterator.next();
            ${uniquePrefix}currentBidModifier = ${uniquePrefix}platformTargeting.getBidModifier();
          }

          Logger.log(${uniquePrefix}device.displayName + ' CPC: ' + ${uniquePrefix}cpc + ', Clicks: ' + ${uniquePrefix}clicks + ', Current Bid Modifier: ' + ${uniquePrefix}currentBidModifier);

          if (${uniquePrefix}clicks >= ${uniquePrefix}CONFIG.MIN_CLICKS_FOR_ACTION) {
            var ${uniquePrefix}newBidModifier = ${uniquePrefix}currentBidModifier;
            if (${uniquePrefix}cpc > ${uniquePrefix}device.cpcThreshold) {
              ${uniquePrefix}newBidModifier = Math.max(${uniquePrefix}CONFIG.MIN_BID_MODIFIER, ${uniquePrefix}currentBidModifier * (1 - ${uniquePrefix}CONFIG.ADJUSTMENT_PERCENTAGE));
              ${uniquePrefix}resultsSummary.push('  └─ ' + ${uniquePrefix}device.displayName + ': CPC (' + ${uniquePrefix}cpc.toFixed(2) + ') > Threshold (' + ${uniquePrefix}device.cpcThreshold.toFixed(2) + '). Reducing bid by ' + (${uniquePrefix}CONFIG.ADJUSTMENT_PERCENTAGE * 100) + '%. Old: ' + ${uniquePrefix}currentBidModifier.toFixed(2) + ', New: ' + ${uniquePrefix}newBidModifier.toFixed(2));
            } else if (${uniquePrefix}cpc < ${uniquePrefix}device.cpcThreshold * ${uniquePrefix}CONFIG.BID_INCREASE_THRESHOLD_FACTOR) {
              ${uniquePrefix}newBidModifier = Math.min(${uniquePrefix}CONFIG.MAX_BID_MODIFIER, ${uniquePrefix}currentBidModifier * (1 + ${uniquePrefix}CONFIG.ADJUSTMENT_PERCENTAGE));
              ${uniquePrefix}resultsSummary.push('  └─ ' + ${uniquePrefix}device.displayName + ': CPC (' + ${uniquePrefix}cpc.toFixed(2) + ') < Threshold Factor (' + (${uniquePrefix}device.cpcThreshold * ${uniquePrefix}CONFIG.BID_INCREASE_THRESHOLD_FACTOR).toFixed(2) + '). Increasing bid by ' + (${uniquePrefix}CONFIG.ADJUSTMENT_PERCENTAGE * 100) + '%. Old: ' + ${uniquePrefix}currentBidModifier.toFixed(2) + ', New: ' + ${uniquePrefix}newBidModifier.toFixed(2));
            } else {
              ${uniquePrefix}resultsSummary.push('  └─ ' + ${uniquePrefix}device.displayName + ': CPC (' + ${uniquePrefix}cpc.toFixed(2) + ') within thresholds. No change.');
              continue;
            }

            if (${uniquePrefix}newBidModifier !== ${uniquePrefix}currentBidModifier) {
              if (${uniquePrefix}platformTargeting) {
                ${uniquePrefix}platformTargeting.setBidModifier(${uniquePrefix}newBidModifier);
              } else {
                // Create new platform targeting if it doesn't exist
                switch(${uniquePrefix}device.platformName) {
                    case 'HighEndMobile': ${uniquePrefix}campaign.targeting().newPlatformBuilder().withPlatformName('HighEndMobile').withBidModifier(${uniquePrefix}newBidModifier).build(); break;
                    case 'Desktop': ${uniquePrefix}campaign.targeting().newPlatformBuilder().withPlatformName('Desktop').withBidModifier(${uniquePrefix}newBidModifier).build(); break;
                    case 'Tablet': ${uniquePrefix}campaign.targeting().newPlatformBuilder().withPlatformName('Tablet').withBidModifier(${uniquePrefix}newBidModifier).build(); break;
                }
              }
              Logger.log('Set ' + ${uniquePrefix}device.displayName + ' bid modifier to ' + ${uniquePrefix}newBidModifier);
              ${uniquePrefix}adjustmentsMade++;
            }
          } else {
            ${uniquePrefix}resultsSummary.push('  └─ ' + ${uniquePrefix}device.displayName + ': Not enough clicks (' + ${uniquePrefix}clicks + ') for action.');
          }
        }
      }
      if (${uniquePrefix}campaignsProcessed > 0 && ${uniquePrefix}resultsSummary.length === ${uniquePrefix}campaignsProcessed) { // only campaign names were added
        ${uniquePrefix}resultsSummary.push('No devices met criteria for adjustments in processed campaigns.');
      } else if (${uniquePrefix}campaignsProcessed > 0 && ${uniquePrefix}adjustmentsMade === 0) {
        ${uniquePrefix}resultsSummary.push('All devices were within CPC thresholds or had insufficient data. No bid adjustments made.');
      }
    }
    Logger.log('Device Bid Adjuster script finished.');

  } catch (e) {
    Logger.log('Error during script execution: ' + e.toString());
    ${uniquePrefix}resultsSummary.push('Error: ' + e.toString() + (e.stack ? '\nStack: ' + e.stack : ''));
    ${uniquePrefix}errorOccurred = true;
    ${uniquePrefix}errorMessage = e.toString();
  } finally {
    if (${uniquePrefix}CONFIG.USE_TELEGRAM) {
      var ${uniquePrefix}statusEmoji = ${uniquePrefix}errorOccurred ? '❌' : '✅';
      var ${uniquePrefix}statusMessage = ${uniquePrefix}errorOccurred ? 'Script Execution Failed' : 'Script Execution Finished Successfully';
      
      var ${uniquePrefix}finalSummary = '';
      if (${uniquePrefix}errorOccurred) {
        ${uniquePrefix}finalSummary = 'Error: ' + ${uniquePrefix}errorMessage;
      } else if (${uniquePrefix}resultsSummary.length === 0) {
        ${uniquePrefix}finalSummary = 'No campaigns found or no actions taken.';
      } else {
        // Limit summary length for Telegram
        var ${uniquePrefix}summaryString = ${uniquePrefix}resultsSummary.join('\n');
        if (${uniquePrefix}summaryString.length > 3500) { // Telegram message limit is 4096, keep some buffer
            ${uniquePrefix}summaryString = ${uniquePrefix}summaryString.substring(0, 3500) + '\n... (summary truncated)';
        }
        ${uniquePrefix}finalSummary = ${uniquePrefix}summaryString;
      }

      var ${uniquePrefix}telegramMessage = 
        ${uniquePrefix}statusEmoji + " " + ${uniquePrefix}statusMessage + "\n" +
        "------------------------------------\n" +
        "📊 Script: " + ${uniquePrefix}CONFIG.SCRIPT_NAME + "\n" +
        "🏭 Account: " + ${uniquePrefix}accountName + " (" + ${uniquePrefix}accountId + ")\n" +
        "📅 Date: " + ${uniquePrefix}executionDate + "\n" +
        "📈 Results:\n" + ${uniquePrefix}finalSummary + "\n" +
        "------------------------------------";
      ${uniquePrefix}sendTelegramNotification(${uniquePrefix}telegramMessage);
    }
  }
}
`;

    return telegramCode + mainScript;
  };

  const pageTitle = "Google Ads Device Bid Adjuster Script Generator";
  const pageDescription = "This tool generates a Google Ads script to automatically adjust device bid modifiers (Mobile, Desktop, Tablet) based on their Cost-Per-Click (CPC) performance relative to your defined thresholds. It helps optimize bids to improve campaign efficiency.";

  const howThisWorksContent = (
    <div className="space-y-4">
      <h3 className="text-xl font-semibold text-slate-200">How It Works</h3>
      <ul className="list-disc list-inside space-y-2 text-slate-300">
        <li><strong>Campaign Selection:</strong> Targets active Search campaigns. You can specify a pattern to filter campaigns by name (e.g., "Brand"). Leaving the pattern empty targets all Search campaigns.</li>
        <li><strong>Device Metrics:</strong> For each targeted campaign, the script fetches performance statistics (Clicks, Cost) for Mobile, Desktop, and Tablet devices over the selected date range.</li>
        <li><strong>CPC Calculation:</strong> It calculates the average CPC for each device.</li>
        <li><strong>Bid Adjustment Logic:</strong>
          <ul className="list-disc list-inside ml-6 mt-1 space-y-1">
            <li>If a device's CPC is <strong>higher</strong> than its specified threshold, its bid modifier is <strong>decreased</strong> by the set percentage.</li>
            <li>If a device's CPC is <strong>lower</strong> than 70% of its threshold (indicating good efficiency with room to bid more), its bid modifier is <strong>increased</strong> by the set percentage.</li>
          </ul>
        </li>
        <li><strong>Safeguards:</strong>
          <ul className="list-disc list-inside ml-6 mt-1 space-y-1">
            <li>Adjustments are only made if a device has a minimum number of clicks (default: 1).</li>
            <li>Bid modifiers are clamped between -90% (0.1) and +900% (10.0).</li>
            <li>Adjustments are only applied if the change is significant enough (more than 0.5% difference from the current modifier).</li>
          </ul>
        </li>
        <li><strong>Logging & Notifications:</strong> The script logs all actions. If Telegram notifications are enabled, a summary is sent.</li>
      </ul>
      <p className="text-sm text-slate-400 mt-2">
        <strong>Note:</strong> This script focuses on Search campaigns. Ensure that device targeting is enabled for your campaigns. Test thoroughly in preview mode before applying changes to live campaigns.
      </p>
    </div>
  );

  if (showResult) {
    return (
      <ToolPageLayout title={pageTitle} description={pageDescription} howThisWorksContent={howThisWorksContent}>
        <div id="scriptDisplaySectionDeviceBid" className="mt-8">
          <h3 className="text-2xl font-semibold text-slate-100 mb-6">Generated Script</h3>
          {message && <NotificationMessage type={message.type} message={message.text} />}
          <ScriptDisplay scriptContent={generatedScript} />
          <StyledButton
            onClick={() => { 
              setShowResult(false); 
              setMessage(null); 
            }}
            variant="outline"
            className="mt-8 w-full sm:w-auto"
            themeColor="slate"
          >
            Back to Form
          </StyledButton>
        </div>
      </ToolPageLayout>
    );
  }

  return (
    <ToolPageLayout title={pageTitle} description={pageDescription} howThisWorksContent={howThisWorksContent}>
      {message && <NotificationMessage type={message.type} message={message.text} className="mb-6" />}

      <form onSubmit={(e) => { e.preventDefault(); handleGenerateScript(); }} className="space-y-8 mt-8">
        <FormSection title="Campaign Targeting & Date Range" icon={<Monitor />} theme="sky">
          <FormItem 
            label="Campaign Name Pattern (Optional)" 
            htmlFor="campaignNamePatternDBA"
            tooltipText="Enter a part of the campaign name to target specific campaigns (e.g., 'Brand'). Leave empty to consider all Search campaigns."
          >
            <StyledInput 
              type="text" 
              id="campaignNamePatternDBA" 
              name="campaignNamePattern" 
              value={campaignNamePattern} 
              onChange={handleInputChange} 
              placeholder="e.g., Brand - US - Exact"
            />
          </FormItem>
          <FormItem 
            label="Select Date Range" 
            htmlFor="dateRangeDBA" 
            tooltipText="The period for which device performance data will be analyzed."
            required
          >
            <StyledSelect
              id="dateRangeDBA"
              name="dateRange"
              value={dateRange}
              onChange={handleInputChange}
            >
              {GOOGLE_ADS_DATE_RANGES.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </StyledSelect>
          </FormItem>
        </FormSection>

        <FormSection title="Device CPC Thresholds & Adjustment" icon={<Smartphone />} theme="indigo">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
            <FormItem label="Mobile CPC Threshold" htmlFor="mobileCpcThresholdDBA" tooltipText="If Mobile CPC exceeds this, bid will be decreased." required>
              <StyledInput type="number" id="mobileCpcThresholdDBA" name="mobileCpcThreshold" value={mobileCpcThreshold} onChange={handleInputChange} placeholder="e.g., 1.50" min="0.01" step="0.01" />
            </FormItem>
            <FormItem label="Desktop CPC Threshold" htmlFor="desktopCpcThresholdDBA" tooltipText="If Desktop CPC exceeds this, bid will be decreased." required>
              <StyledInput type="number" id="desktopCpcThresholdDBA" name="desktopCpcThreshold" value={desktopCpcThreshold} onChange={handleInputChange} placeholder="e.g., 2.00" min="0.01" step="0.01" />
            </FormItem>
            <FormItem label="Tablet CPC Threshold" htmlFor="tabletCpcThresholdDBA" tooltipText="If Tablet CPC exceeds this, bid will be decreased." required>
              <StyledInput type="number" id="tabletCpcThresholdDBA" name="tabletCpcThreshold" value={tabletCpcThreshold} onChange={handleInputChange} placeholder="e.g., 1.80" min="0.01" step="0.01" />
            </FormItem>
            <FormItem label="Adjustment Percentage (%)" htmlFor="adjustmentPercentageDBA" tooltipText="Percentage to increase/decrease bids by (1-90). E.g., enter 10 for 10%." required>
              <StyledInput type="number" id="adjustmentPercentageDBA" name="adjustmentPercentage" value={adjustmentPercentage} onChange={handleInputChange} placeholder="e.g., 10" min="1" max="90" step="1" />
            </FormItem>
          </div>
        </FormSection>

        <FormSection title="Telegram Notifications" icon={<Bell />} theme="purple" collapsible initiallyCollapsed={!useTelegram && !telegramBotToken && !telegramChatId}>
          <div className="mb-4">
            <label className="flex items-center cursor-pointer group">
              <div className="relative">
                <input
                  type="checkbox"
                  id="useTelegramDBA"
                  name="useTelegram"
                  checked={useTelegram}
                  onChange={handleInputChange}
                  className="sr-only"
                />
                <div className={`
                  w-5 h-5 rounded border-2 flex items-center justify-center transition-all duration-200
                  ${useTelegram 
                    ? 'bg-sky-600 border-sky-600' 
                    : 'bg-slate-700/50 border-slate-500 group-hover:border-slate-400'}
                  }`}
                >
                  {useTelegram && (
                    <svg className="w-3 h-3 text-white" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
              </div>
              <span className="ml-3 text-gray-300 text-sm font-medium">
                Enable Telegram Notifications
              </span>
            </label>
          </div>
          {useTelegram && (
            <>
              <FormItem label="Telegram Bot Token" htmlFor="telegramBotTokenDBA" tooltipText="Your Telegram Bot Token." required={useTelegram}>
                <StyledInput type="text" id="telegramBotTokenDBA" name="telegramBotToken" value={telegramBotToken} onChange={handleInputChange} placeholder="123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11" />
              </FormItem>
              <FormItem label="Telegram Chat ID" htmlFor="telegramChatIdDBA" tooltipText="Your Telegram Chat ID." required={useTelegram}>
                <StyledInput type="text" id="telegramChatIdDBA" name="telegramChatId" value={telegramChatId} onChange={handleInputChange} placeholder="-1001234567890 or @channelname" />
              </FormItem>
            </>
          )}
        </FormSection>

        <div className="flex flex-col sm:flex-row gap-4 pt-4">
          <StyledButton type="submit" variant="primary" size="lg" themeColor="emerald">
            Generate Script
          </StyledButton>
        </div>
      </form>
    </ToolPageLayout>
  );
};

export default DeviceBidAdjuster;
