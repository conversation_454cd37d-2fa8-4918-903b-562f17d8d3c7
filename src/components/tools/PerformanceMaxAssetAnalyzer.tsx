import React, { useState, useEffect } from 'react';
import ToolPageLayout from '../ui/shared/ToolPageLayout';
import FormSection from '../ui/shared/FormSection';
import FormItem from '../ui/shared/FormItem';
import StyledInput from '../ui/shared/StyledInput';
import StyledButton from '../ui/shared/StyledButton';
import ScriptDisplay from '../ui/shared/ScriptDisplay';
import NotificationMessage from '../ui/shared/NotificationMessage';
import { Filter, Mail, Send, PlayCircle } from 'lucide-react';

const LOCAL_STORAGE_PREFIX = 'pMaxAssetAnalyzer_';

const GOOGLE_ADS_DATE_RANGES_PMAX = [
  'LAST_7_DAYS',
  'LAST_14_DAYS',
  'LAST_30_DAYS',
  'LAST_90_DAYS',
];

const PerformanceMaxAssetAnalyzer: React.FC = () => {
  const [campaignNamePattern, setCampaignNamePattern] = useState('');
  const [dateRange, setDateRange] = useState('LAST_30_DAYS');
  const [emailAddress, setEmailAddress] = useState('');
  const [useTelegram, setUseTelegram] = useState(false);
  const [telegramBotToken, setTelegramBotToken] = useState('');
  const [telegramChatId, setTelegramChatId] = useState('');
  const [showTelegramSettings, setShowTelegramSettings] = useState(false);

  const [generatedScript, setGeneratedScript] = useState('');
  const [showResult, setShowResult] = useState(false);
  const [message, setMessage] = useState<{text: string, type: 'error' | 'success' | 'info'} | null>(null);

  useEffect(() => {
    const storedCampaignPattern = localStorage.getItem(`${LOCAL_STORAGE_PREFIX}campaignNamePattern`);
    if (storedCampaignPattern) setCampaignNamePattern(storedCampaignPattern);

    const storedDateRange = localStorage.getItem(`${LOCAL_STORAGE_PREFIX}dateRange`);
    if (storedDateRange) setDateRange(storedDateRange);

    const storedEmail = localStorage.getItem(`${LOCAL_STORAGE_PREFIX}emailAddress`);
    if (storedEmail) setEmailAddress(storedEmail);

    const storedUseTelegram = localStorage.getItem(`${LOCAL_STORAGE_PREFIX}useTelegram`);
    if (storedUseTelegram) setUseTelegram(storedUseTelegram === 'true');
    
    const storedShowTelegram = localStorage.getItem(`${LOCAL_STORAGE_PREFIX}showTelegramSettings`);
    if (storedShowTelegram) setShowTelegramSettings(storedShowTelegram === 'true');
    
    // Only load token and chat ID if useTelegram was true, to avoid showing them if toggled off and reloaded
    if (localStorage.getItem(`${LOCAL_STORAGE_PREFIX}useTelegram`) === 'true') {
      const storedToken = localStorage.getItem(`${LOCAL_STORAGE_PREFIX}telegramBotToken`);
      if (storedToken) setTelegramBotToken(storedToken);

      const storedChatId = localStorage.getItem(`${LOCAL_STORAGE_PREFIX}telegramChatId`);
      if (storedChatId) setTelegramChatId(storedChatId);
    }
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    const valToSet = type === 'checkbox' ? (e.target as HTMLInputElement).checked : value;

    // Using 'any' for stateSetters to simplify dynamic setter assignment.
    // Type safety is ensured by how valToSet is derived and matched with the input field.
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const stateSetters: Record<string, React.Dispatch<any>> = {
      campaignNamePattern: setCampaignNamePattern,
      dateRange: setDateRange,
      emailAddress: setEmailAddress,
      useTelegram: setUseTelegram,
      telegramBotToken: setTelegramBotToken,
      telegramChatId: setTelegramChatId,
      showTelegramSettings: setShowTelegramSettings,
    };

    if (stateSetters[name]) {
      stateSetters[name](valToSet);
      localStorage.setItem(`${LOCAL_STORAGE_PREFIX}${name}`, String(valToSet));
    }

    // Special handling for useTelegram to also toggle showTelegramSettings
    if (name === 'useTelegram') {
      setShowTelegramSettings(valToSet as boolean);
      localStorage.setItem(`${LOCAL_STORAGE_PREFIX}showTelegramSettings`, String(valToSet as boolean));
      // If disabling telegram, clear sensitive fields from state & local storage
      if (!(valToSet as boolean)) {
        setTelegramBotToken('');
        setTelegramChatId('');
        localStorage.removeItem(`${LOCAL_STORAGE_PREFIX}telegramBotToken`);
        localStorage.removeItem(`${LOCAL_STORAGE_PREFIX}telegramChatId`);
      }
    }
  };

  const handleGenerateScript = () => {
    setMessage(null);
    setShowResult(false);

    if (!campaignNamePattern.trim()) {
        setMessage({ text: 'Campaign Name Pattern is required for Performance Max campaigns.', type: 'error' });
        return;
    }
    if (!emailAddress.trim() || !/\S+@\S+\.\S+/.test(emailAddress)) {
        setMessage({ text: 'Please enter a valid email address for notifications.', type: 'error' });
        return;
    }
    if (useTelegram && (!telegramBotToken.trim() || !telegramChatId.trim())) {
      setMessage({ text: 'Please enter both Telegram Bot Token and Chat ID for Telegram notifications.', type: 'error' });
      return;
    }

    const script = generatePMaxAssetScript(
      campaignNamePattern,
      dateRange,
      emailAddress,
      useTelegram,
      telegramBotToken,
      telegramChatId
    );
    setGeneratedScript(script);
    setShowResult(true);
    setMessage({ text: 'Script generated successfully!', type: 'success'});
  };

  const generatePMaxAssetScript = (
    campaignPattern: string, dateRangeVal: string, email: string,
    useTelegramVal: boolean, botToken: string, chatId: string
  ): string => {
    const uniquePrefix = 'pMaxAsset' + Date.now().toString(36) + '_';
    const telegramCode = ''; 

    const mainScript = `
function main() {
  var ${uniquePrefix}CONFIG = {
    CAMPAIGN_NAME_CONTAINS: "${campaignPattern.replace(/\\/g, '\\\\').replace(/"/g, '\\"')}",
    DATE_RANGE: "${dateRangeVal}",
    EMAIL_ADDRESS: "${email.replace(/\\/g, '\\\\').replace(/"/g, '\\"')}",
    PERFORMANCE_LABEL_TO_CHECK: 'LOW',
    USE_TELEGRAM: ${useTelegramVal},
    TELEGRAM_BOT_TOKEN: "${botToken.replace(/\\/g, '\\\\').replace(/"/g, '\\"')}",
    TELEGRAM_CHAT_ID: "${chatId.replace(/\\/g, '\\\\').replace(/"/g, '\\"')}",
  };

  var ${uniquePrefix}lowPerformingAssets = [];
  var ${uniquePrefix}accountTimezone = AdsApp.currentAccount().getTimeZone();
  var ${uniquePrefix}formattedTime = Utilities.formatDate(new Date(), ${uniquePrefix}accountTimezone, 'yyyy-MM-dd HH:mm:ss');
  var ${uniquePrefix}emailSubject = 'Performance Max Low-Performing Assets Report - ' + ${uniquePrefix}formattedTime;
  var ${uniquePrefix}emailBody = 'Performance Max Asset Analyzer Script Run at: ' + ${uniquePrefix}formattedTime + '\\n\\n';
  ${uniquePrefix}emailBody += 'Report for Campaigns containing: "' + ${uniquePrefix}CONFIG.CAMPAIGN_NAME_CONTAINS + '"\\n';
  ${uniquePrefix}emailBody += 'Date Range: ' + ${uniquePrefix}CONFIG.DATE_RANGE + '\\n';
  ${uniquePrefix}emailBody += 'Finding assets with performance label: ' + ${uniquePrefix}CONFIG.PERFORMANCE_LABEL_TO_CHECK + '\\n\\n';

  Logger.log(${uniquePrefix}emailBody);

  var ${uniquePrefix}campaignSelector = AdsApp.performanceMaxCampaigns()
    .withCondition('campaign.status = ENABLED'); // Filter only by status initially

  var ${uniquePrefix}campaignIterator = ${uniquePrefix}campaignSelector.get();
  var ${uniquePrefix}matchingCampaignsProcessed = 0;
  var ${uniquePrefix}assetsHeaderAdded = false; // To control adding the "--- Found Low-Performing Assets ---" header

  if (!${uniquePrefix}campaignIterator.hasNext()) {
    ${uniquePrefix}emailBody += 'No active Performance Max campaigns found in this account.\\n';
    Logger.log('No active Performance Max campaigns found in this account.');
  } else {
    while (${uniquePrefix}campaignIterator.hasNext()) {
      var ${uniquePrefix}campaign = ${uniquePrefix}campaignIterator.next();
      var ${uniquePrefix}campaignName = ${uniquePrefix}campaign.getName();

      // Perform name filtering in the script (case-insensitive)
      if (!${uniquePrefix}campaignName.toLowerCase().includes(${uniquePrefix}CONFIG.CAMPAIGN_NAME_CONTAINS.toLowerCase())) {
        Logger.log('Skipping PMax Campaign (name does not match filter "' + ${uniquePrefix}CONFIG.CAMPAIGN_NAME_CONTAINS + '"): ' + ${uniquePrefix}campaignName);
        continue; // Skip to the next campaign if name doesn't match
      }

      // If this is the first matching campaign, add the assets header
      if (!${uniquePrefix}assetsHeaderAdded) {
        ${uniquePrefix}emailBody += '--- Found Low-Performing Assets ---\\n';
        ${uniquePrefix}assetsHeaderAdded = true;
      }
      
      ${uniquePrefix}matchingCampaignsProcessed++;
      Logger.log('Processing PMax Campaign: ' + ${uniquePrefix}campaignName);
      ${uniquePrefix}emailBody += '\\nCampaign: ' + ${uniquePrefix}campaignName + '\\n';

      var ${uniquePrefix}query = "SELECT asset.resource_name, asset_field_type_view.performance_label, asset_group.name, asset.name, asset.type, asset.text_asset.text, asset.image_asset.full_size_image_url, asset.youtube_video_asset.youtube_video_id " +
                        "FROM asset_field_type_view " + 
                        "WHERE asset_field_type_view.performance_label = '" + ${uniquePrefix}CONFIG.PERFORMANCE_LABEL_TO_CHECK + "' " + 
                        "AND campaign.id = " + ${uniquePrefix}campaign.getId() + " " +
                        "AND segments.date DURING " + ${uniquePrefix}CONFIG.DATE_RANGE;
    
      var ${uniquePrefix}report = AdsApp.search(${uniquePrefix}query);
      var ${uniquePrefix}assetsFoundInCampaign = false;

      while (${uniquePrefix}report.hasNext()) {
        var ${uniquePrefix}row = ${uniquePrefix}report.next();
        ${uniquePrefix}assetsFoundInCampaign = true;

        var ${uniquePrefix}assetResourceName = ${uniquePrefix}row.asset.resourceName;
        var ${uniquePrefix}performanceLabel = ${uniquePrefix}row.assetFieldTypeView.performanceLabel;
        var ${uniquePrefix}assetGroupName = ${uniquePrefix}row.assetGroup.name;
        var ${uniquePrefix}assetName = ${uniquePrefix}row.asset.name ? ${uniquePrefix}row.asset.name : 'N/A (Asset ID: ' + ${uniquePrefix}assetResourceName.split('/').pop() + ')';
        var ${uniquePrefix}assetType = ${uniquePrefix}row.asset.type;
        var ${uniquePrefix}assetContent = '';

        if (${uniquePrefix}assetType === 'TEXT' && ${uniquePrefix}row.asset.textAsset) {
          ${uniquePrefix}assetContent = (${uniquePrefix}row.asset.textAsset.text || '').replace(/\\n/g, '\\n');
        } else if (${uniquePrefix}assetType === 'IMAGE' && ${uniquePrefix}row.asset.imageAsset) {
          ${uniquePrefix}assetContent = ${uniquePrefix}row.asset.imageAsset.fullSizeImageUrl ? 'Image URL: ' + ${uniquePrefix}row.asset.imageAsset.fullSizeImageUrl : 'Image (no URL)';
        } else if (${uniquePrefix}assetType === 'YOUTUBE_VIDEO' && ${uniquePrefix}row.asset.youtubeVideoAsset) {
          ${uniquePrefix}assetContent = 'YouTube Video ID: ' + ${uniquePrefix}row.asset.youtubeVideoAsset.youtubeVideoId;
        }

        var ${uniquePrefix}assetInfo = '  - Asset Group: ' + ${uniquePrefix}assetGroupName +
                        ' | Asset: ' + ${uniquePrefix}assetName +
                        ' | Type: ' + ${uniquePrefix}assetType +
                        ' | Perf Label: ' + ${uniquePrefix}performanceLabel +
                        (${uniquePrefix}assetContent ? ' | Content: ' + ${uniquePrefix}assetContent.substring(0,100) + (${uniquePrefix}assetContent.length > 100 ? '...' : '') : '');
        
        ${uniquePrefix}lowPerformingAssets.push({
          campaign: ${uniquePrefix}campaignName,
          assetGroup: ${uniquePrefix}assetGroupName,
          assetName: ${uniquePrefix}assetName,
          assetType: ${uniquePrefix}assetType,
          performanceLabel: ${uniquePrefix}performanceLabel,
          content: ${uniquePrefix}assetContent
        });
        ${uniquePrefix}emailBody += ${uniquePrefix}assetInfo + '\\n';
        Logger.log('  Found Low Perf Asset: ' + ${uniquePrefix}assetInfo);
      }
      if(!${uniquePrefix}assetsFoundInCampaign){
          ${uniquePrefix}emailBody += '  No assets with performance label "' + ${uniquePrefix}CONFIG.PERFORMANCE_LABEL_TO_CHECK + '" found in this campaign for the selected period.\\n';
      }
    }
  }

  // After the loop, if campaigns existed but none matched the name filter:
  if (${uniquePrefix}campaignIterator.totalNumEntities() > 0 && ${uniquePrefix}matchingCampaignsProcessed === 0 && ${uniquePrefix}assetsHeaderAdded === false) {
    ${uniquePrefix}emailBody += 'No active Performance Max campaigns found matching the pattern: "' + ${uniquePrefix}CONFIG.CAMPAIGN_NAME_CONTAINS + '".\\n';
    Logger.log('No active Performance Max campaigns found matching the pattern: "' + ${uniquePrefix}CONFIG.CAMPAIGN_NAME_CONTAINS + '".');
  }

  if (${uniquePrefix}lowPerformingAssets.length === 0 && ${uniquePrefix}matchingCampaignsProcessed > 0) {
    ${uniquePrefix}emailBody += '\\nNo assets with performance label "' + ${uniquePrefix}CONFIG.PERFORMANCE_LABEL_TO_CHECK + '" found across all processed (matching) campaigns for the selected period.\\n';
  } else if (${uniquePrefix}lowPerformingAssets.length > 0){
    ${uniquePrefix}emailBody += '\\n--- End of Report ---';
  }

  Logger.log('\\n--- Email Body to be Sent ---');
  Logger.log(${uniquePrefix}emailBody);
  Logger.log('--- --- --- --- --- --- --- ---');

  if (${uniquePrefix}CONFIG.EMAIL_ADDRESS) {
    MailApp.sendEmail(${uniquePrefix}CONFIG.EMAIL_ADDRESS, ${uniquePrefix}emailSubject, ${uniquePrefix}emailBody);
    Logger.log('Report email sent to: ' + ${uniquePrefix}CONFIG.EMAIL_ADDRESS);
  }

  // Helper function to send Telegram notifications
  function ${uniquePrefix}sendTelegramNotification(message) {
    if (!${uniquePrefix}CONFIG.USE_TELEGRAM || !${uniquePrefix}CONFIG.TELEGRAM_BOT_TOKEN || !${uniquePrefix}CONFIG.TELEGRAM_CHAT_ID) {
      Logger.log('Telegram notifications are not enabled or configured.');
      return;
    }
    var ${uniquePrefix}payload = {
      'chat_id': ${uniquePrefix}CONFIG.TELEGRAM_CHAT_ID,
      'text': message,
      'parse_mode': 'HTML'
    };
    var ${uniquePrefix}options = {
      'method': 'post',
      'contentType': 'application/json',
      'payload': JSON.stringify(${uniquePrefix}payload)
    };
    try {
      // Corrected: Further split 'https://' to prevent comment removal issue
      UrlFetchApp.fetch('https:' + '/' + '/api.telegram.org/bot' + ${uniquePrefix}CONFIG.TELEGRAM_BOT_TOKEN + '/sendMessage', ${uniquePrefix}options);
      Logger.log('Telegram notification sent.');
    } catch (e) { Logger.log('Telegram Error: ' + e.toString()); }
  }

  // Helper function to format the Telegram summary message
  function ${uniquePrefix}formatTelegramSummary(statusMessage) {
    var ${uniquePrefix}accountName = AdsApp.currentAccount().getName() || 'N/A';
    var ${uniquePrefix}accountId = AdsApp.currentAccount().getCustomerId() || 'N/A';
    var ${uniquePrefix}now = new Date();
    var ${uniquePrefix}dateTime = Utilities.formatDate(${uniquePrefix}now, AdsApp.currentAccount().getTimeZone(), 'yyyy-MM-dd HH:mm:ss');

    var ${uniquePrefix}summary = '------------------------------------\\n';
    ${uniquePrefix}summary += '📊 <b>Script:</b> Performance Max Asset Analyzer\\n';
    ${uniquePrefix}summary += '🏭 <b>Account:</b> ' + ${uniquePrefix}accountName + ' (' + ${uniquePrefix}accountId + ')\\n';
    ${uniquePrefix}summary += '📅 <b>Date:</b> ' + ${uniquePrefix}dateTime + '\\n';
    ${uniquePrefix}summary += '📈 <b>Results:</b> ' + statusMessage + '\\n';
    if (${uniquePrefix}CONFIG.CAMPAIGN_NAME_CONTAINS) {
      ${uniquePrefix}summary += '🏷️ <b>Campaign Filter:</b> <i>' + ${uniquePrefix}CONFIG.CAMPAIGN_NAME_CONTAINS + '</i>\\n';
    }
    ${uniquePrefix}summary += '------------------------------------';
    return ${uniquePrefix}summary;
  }

  // Construct and send Telegram notification if enabled
  if (${uniquePrefix}CONFIG.USE_TELEGRAM && ${uniquePrefix}CONFIG.TELEGRAM_BOT_TOKEN && ${uniquePrefix}CONFIG.TELEGRAM_CHAT_ID) {
    var ${uniquePrefix}statusForTelegram = '';

    if (${uniquePrefix}lowPerformingAssets.length > 0) {
      ${uniquePrefix}statusForTelegram = ${uniquePrefix}lowPerformingAssets.length + ' low-performing asset(s) found. See email for details.';
    } else if (${uniquePrefix}matchingCampaignsProcessed > 0) {
      ${uniquePrefix}statusForTelegram = 'No low-performing assets found for performance label ' + ${uniquePrefix}CONFIG.PERFORMANCE_LABEL_TO_CHECK + '.';
    } else if (${uniquePrefix}campaignIterator.totalNumEntities() > 0 && ${uniquePrefix}matchingCampaignsProcessed === 0 && !${uniquePrefix}assetsHeaderAdded) {
      // This condition means campaigns exist, but none matched the name filter.
      // The assetsHeaderAdded check ensures we don't overwrite a 'no assets found in matching campaigns' message.
      ${uniquePrefix}statusForTelegram = 'No campaigns matched the filter: "' + ${uniquePrefix}CONFIG.CAMPAIGN_NAME_CONTAINS + '".';
    } else if (${uniquePrefix}campaignIterator.totalNumEntities() === 0) { // No PMax campaigns found in the account at all
      ${uniquePrefix}statusForTelegram = 'No active Performance Max campaigns found in the account.';
    } else {
       // Fallback for any other scenario, e.g. matching campaigns processed but no assets found and no low performing assets (already covered by second branch)
       // This branch might indicate a state not explicitly handled, or could be simplified if other conditions are exhaustive.
       ${uniquePrefix}statusForTelegram = 'Processing complete. See email for detailed report if generated.';
    }
    
    var ${uniquePrefix}telegramMsg = ${uniquePrefix}formatTelegramSummary(${uniquePrefix}statusForTelegram);
    ${uniquePrefix}sendTelegramNotification(${uniquePrefix}telegramMsg);
  }
}
`;

    const scriptContent = `
${telegramCode}
${mainScript}
`;

    // Remove multi-line comments
    let cleanedScript = scriptContent.replace(/\/\*[\s\S]*?\*\//g, '');
    // Remove single-line comments
    cleanedScript = cleanedScript.replace(/\/\/.*$/gm, '');
    // Remove empty lines resulting from comment removal
    cleanedScript = cleanedScript.replace(/^\s*[\r\n]/gm, '');
    
    return cleanedScript.trim(); // Trim leading/trailing whitespace as well
  };

  return (
    <ToolPageLayout
      title="Аналізатор ресурсів Performance Max"
      description="Генерує скрипт Google Ads для виявлення низькоефективних ресурсів (текст, зображення, відео) у кампаніях Performance Max на основі їх міток ефективності (наприклад, 'LOW'). Сповіщає електронною поштою та опціонально через Telegram."
    >
      <div className="space-y-6">
        <FormSection title="Налаштування кампанії та дат" icon={<Filter />} theme="sky">
          <FormItem label="Шаблон назви кампанії (містить, без урахування регістру)" htmlFor="pmaxCampaignPattern" tooltipText="Введіть частину назви вашої PMax кампанії, наприклад, 'Brand_PMax_USA'. Скрипт шукатиме кампанії, що містять цей текст.">
            <StyledInput id="pmaxCampaignPattern" name="campaignNamePattern" value={campaignNamePattern} onChange={handleInputChange} placeholder="наприклад, My_PMax_Campaign" />
          </FormItem>
          <FormItem label="Діапазон дат" htmlFor="pmaxDateRange" tooltipText="Виберіть період для аналізу ефективності ресурсів.">
            <select 
              id="pmaxDateRange" 
              name="dateRange" 
              value={dateRange} 
              onChange={handleInputChange} 
              className="w-full p-2.5 border border-slate-700 rounded-lg bg-slate-800 text-slate-100 focus:ring-2 focus:ring-sky-500 focus:border-sky-500 transition-colors duration-200 ease-in-out shadow-sm"
            >
              {GOOGLE_ADS_DATE_RANGES_PMAX.map(range => (
                <option key={range} value={range}>{range.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}</option>
              ))}
            </select>
          </FormItem>
        </FormSection>

        <FormSection title="Налаштування сповіщень" icon={<Mail />} theme="amber">
          <FormItem label="Електронна адреса для сповіщень" htmlFor="pmaxEmail" tooltipText="Скрипт надішле звіт на цю електронну адресу.">
            <StyledInput id="pmaxEmail" name="emailAddress" type="email" value={emailAddress} onChange={handleInputChange} placeholder="<EMAIL>" />
          </FormItem>
          <FormItem label="Увімкнути сповіщення Telegram" htmlFor="pmaxUseTelegram">
            <div className="flex items-center">
              <input 
                type="checkbox" 
                id="pmaxUseTelegram" 
                name="useTelegram" 
                checked={useTelegram} 
                onChange={handleInputChange} 
                className="w-5 h-5 rounded text-blue-500 bg-slate-700 border-slate-600 focus:ring-blue-500 focus:ring-offset-slate-800 mr-2.5"
              />
              <span className="text-sm text-slate-300">Receive summary via Telegram</span>
            </div>
          </FormItem>
        </FormSection>

        {showTelegramSettings && (
          <FormSection title="Деталі Telegram" icon={<Send />} theme="blue">
            <FormItem label="Токен Telegram бота" htmlFor="pmaxTelegramToken" tooltipText="API токен вашого Telegram бота.">
              <StyledInput id="pmaxTelegramToken" name="telegramBotToken" type="password" value={telegramBotToken} onChange={handleInputChange} placeholder="Введіть токен бота" />
            </FormItem>
            <FormItem label="ID чату Telegram" htmlFor="pmaxTelegramChatId" tooltipText="ID чату для надсилання сповіщень (ID користувача, групи або каналу).">
              <StyledInput id="pmaxTelegramChatId" name="telegramChatId" value={telegramChatId} onChange={handleInputChange} placeholder="Enter Chat ID" />
            </FormItem>
          </FormSection>
        )}

        <StyledButton 
          onClick={handleGenerateScript} 
          variant="primary" 
          size="lg" 
          themeColor="emerald"
          leftIcon={<PlayCircle className="mr-2 h-5 w-5"/>}
          className="w-full sm:w-auto"
        >
          Generate PMax Asset Analyzer Script
        </StyledButton>

        {message && (
          <NotificationMessage type={message.type} message={message.text} onDismiss={() => setMessage(null)} />
        )}

        {showResult && generatedScript && (
          <ScriptDisplay scriptContent={generatedScript} />
        )}
      </div>
    </ToolPageLayout>
  );
};

export default PerformanceMaxAssetAnalyzer;
