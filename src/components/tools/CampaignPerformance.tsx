import React, { useState, useEffect } from 'react';
import { Calendar, Bell, PlayCircle } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';

import ToolPageLayout from '../ui/shared/ToolPageLayout';
import FormSection from '../ui/shared/FormSection';
import FormItem from '../ui/shared/FormItem';
import StyledInput from '../ui/shared/StyledInput';
import StyledButton from '../ui/shared/StyledButton';
import ScriptDisplay from '../ui/shared/ScriptDisplay';
import NotificationMessage from '../ui/shared/NotificationMessage';

const LOCAL_STORAGE_PREFIX = 'campaignPerformance_';

const dateRangeOptions = [
  { value: 'TODAY', label: 'Today' },
  { value: 'YESTERDAY', label: 'Yesterday' },
  { value: 'LAST_7_DAYS', label: 'Last 7 Days' },
  { value: 'LAST_WEEK', label: 'Last Week (Mon-Sun)' },
  { value: 'LAST_BUSINESS_WEEK', label: 'Last Business Week (Mon-Fri)' },
  { value: 'THIS_MONTH', label: 'This Month' },
  { value: 'LAST_MONTH', label: 'Last Month' },
  { value: 'LAST_14_DAYS', label: 'Last 14 Days' },
  { value: 'LAST_30_DAYS', label: 'Last 30 Days' },
  { value: 'THIS_WEEK_SUN_TODAY', label: 'This Week (Sun-Today)' },
  { value: 'THIS_WEEK_MON_TODAY', label: 'This Week (Mon-Today)' },
  { value: 'LAST_WEEK_SUN_SAT', label: 'Last Week (Sun-Sat)' },
];

const CampaignPerformance: React.FC = () => {
  const { t } = useLanguage();

  // State declarations
  const [dateRange, setDateRange] = useState('LAST_30_DAYS');
  const [reportName, setReportName] = useState('Campaign Performance Overview');
  const [useTelegram, setUseTelegram] = useState(false);
  const [telegramBotToken, setTelegramBotToken] = useState('');
  const [telegramChatId, setTelegramChatId] = useState('');
  
  // State for script generation and UI
  const [generatedScript, setGeneratedScript] = useState('');
  const [showResult, setShowResult] = useState(false);
  const [message, setMessage] = useState<{text: string, type: 'error' | 'success' | 'info'} | null>(null);

  // Load saved form data from localStorage
  useEffect(() => {
    const fields: string[] = ['dateRange', 'reportName', 'useTelegram', 'telegramBotToken', 'telegramChatId'];
    fields.forEach(field => {
      const savedValue = localStorage.getItem(`${LOCAL_STORAGE_PREFIX}${field}`);
      if (savedValue !== null) {
        if (field === 'dateRange') setDateRange(savedValue);
        else if (field === 'reportName') setReportName(savedValue);
        else if (field === 'useTelegram') setUseTelegram(savedValue === 'true');
        else if (field === 'telegramBotToken') setTelegramBotToken(savedValue);
        else if (field === 'telegramChatId') setTelegramChatId(savedValue);
      }
    });
  }, []);
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    setMessage(null);
    const { name, value, type } = e.target;
    const val = type === 'checkbox' ? (e.target as HTMLInputElement).checked : value;

    if (name === 'dateRange') setDateRange(val as string);
    else if (name === 'reportName') setReportName(val as string);
    else if (name === 'useTelegram') setUseTelegram(val as boolean);
    else if (name === 'telegramBotToken') setTelegramBotToken(val as string);
    else if (name === 'telegramChatId') setTelegramChatId(val as string);

    localStorage.setItem(`${LOCAL_STORAGE_PREFIX}${name}`, String(val));
  };

  const handleGenerateScript = () => {
    setMessage(null); 
    if (useTelegram && (!telegramBotToken || !telegramChatId)) {
      setMessage({ text: 'Please enter both Telegram Bot Token and Chat ID for notifications.', type: 'error' });
      return;
    }
    if (!reportName.trim()) {
      setMessage({ text: 'Report Name cannot be empty.', type: 'error' });
      return;
    }
    const script = generateScript(dateRange, reportName, useTelegram, telegramBotToken, telegramChatId);
    setGeneratedScript(script);
    setShowResult(true);
    setTimeout(() => {
      document.getElementById('scriptDisplaySectionCampPerf')?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  };

  const generateScript = (dateRangeValue: string, reportNameValue: string, useTelegramValue: boolean, botToken: string, chatId: string): string => {
    const uniquePrefix = 'perf' + Date.now().toString(36) + '_';
    const telegramCode = useTelegramValue ? `
function sendTelegramNotification(message) {
  var ${uniquePrefix}_payload = {
    'chat_id': '${chatId.replace(/'/g, "\\'")}',
    'text': message,
    'parse_mode': 'HTML'
  };
  var ${uniquePrefix}_options = {
    'method': 'post',
    'contentType': 'application/json',
    'payload': JSON.stringify(${uniquePrefix}_payload)
  };
  try {
    UrlFetchApp.fetch('https://api.telegram.org/bot${botToken.replace(/'/g, "\\'")}/sendMessage', ${uniquePrefix}_options);
    return true;
  } catch (e) {
    Logger.log('Error sending Telegram notification: ' + e);
    return false;
  }
}
` : '';

    // Telegram notification is handled in the script body

    const mainScript = `function main() {
  var ${uniquePrefix}dateRange = '${dateRangeValue}';
  var ${uniquePrefix}reportName = '${reportNameValue.replace(/'/g, "\\'")}';

  var ${uniquePrefix}campaignReport = AdsApp.report(
    "SELECT CampaignName, AdvertisingChannelType, AdvertisingChannelSubType, " +
    "Impressions, Clicks, Cost, Conversions, ConversionValue " +
    "FROM CAMPAIGN_PERFORMANCE_REPORT " +
    "WHERE CampaignStatus = 'ENABLED' " +
    "DURING " + ${uniquePrefix}dateRange
  );

  var ${uniquePrefix}rows = ${uniquePrefix}campaignReport.rows();
  var ${uniquePrefix}performanceByType = {
    'DEMAND_GEN': { impressions: 0, clicks: 0, cost: 0, conversions: 0, value: 0 },
    'PERFORMANCE_MAX': { impressions: 0, clicks: 0, cost: 0, conversions: 0, value: 0 },
    'SEARCH': { impressions: 0, clicks: 0, cost: 0, conversions: 0, value: 0 },
    'DISPLAY': { impressions: 0, clicks: 0, cost: 0, conversions: 0, value: 0 },
    'VIDEO': { impressions: 0, clicks: 0, cost: 0, conversions: 0, value: 0 },
    'APP_CAMPAIGN': { impressions: 0, clicks: 0, cost: 0, conversions: 0, value: 0 },
    'HOTEL': { impressions: 0, clicks: 0, cost: 0, conversions: 0, value: 0 }
  };

  while (${uniquePrefix}rows.hasNext()) {
    var ${uniquePrefix}row = ${uniquePrefix}rows.next();
    var ${uniquePrefix}type = ${uniquePrefix}row['AdvertisingChannelType'];
    var ${uniquePrefix}subType = ${uniquePrefix}row['AdvertisingChannelSubType'];

    if (${uniquePrefix}subType === 'DEMAND_GEN') ${uniquePrefix}type = 'DEMAND_GEN';
    else if (${uniquePrefix}subType === 'APP_CAMPAIGN') ${uniquePrefix}type = 'APP_CAMPAIGN';
    else if (${uniquePrefix}type === 'HOTEL') ${uniquePrefix}type = 'HOTEL';

    if (${uniquePrefix}performanceByType[${uniquePrefix}type]) {
      ${uniquePrefix}performanceByType[${uniquePrefix}type].impressions += parseInt(${uniquePrefix}row['Impressions'], 10) || 0;
      ${uniquePrefix}performanceByType[${uniquePrefix}type].clicks += parseInt(${uniquePrefix}row['Clicks'], 10) || 0;
      ${uniquePrefix}performanceByType[${uniquePrefix}type].cost += parseFloat(${uniquePrefix}row['Cost']) || 0;
      ${uniquePrefix}performanceByType[${uniquePrefix}type].conversions += parseFloat(${uniquePrefix}row['Conversions']) || 0;
      ${uniquePrefix}performanceByType[${uniquePrefix}type].value += parseFloat(${uniquePrefix}row['ConversionValue']) || 0;
    }
  }

  // Create output string with proper newline escaping
  var ${uniquePrefix}output = 'Campaign Performance by Type Report - ' + ${uniquePrefix}reportName + ' (' + ${uniquePrefix}dateRange + ')' + '\\n';
  ${uniquePrefix}output += '--------------------------------------------------------------------' + '\\n';
  ${uniquePrefix}output += 'Type              | Impr.   | Clicks  | Cost    | Conv.   | Value     | CPA     | ROAS    ' + '\\n';
  ${uniquePrefix}output += '--------------------------------------------------------------------' + '\\n';

  for (var ${uniquePrefix}key in ${uniquePrefix}performanceByType) {
    var ${uniquePrefix}data = ${uniquePrefix}performanceByType[${uniquePrefix}key];
    if (${uniquePrefix}data.impressions > 0 || ${uniquePrefix}data.clicks > 0) {
      var ${uniquePrefix}cpa = (${uniquePrefix}data.conversions > 0) ? (${uniquePrefix}data.cost / ${uniquePrefix}data.conversions).toFixed(2) : 'N/A';
      var ${uniquePrefix}roas = (${uniquePrefix}data.value > 0 && ${uniquePrefix}data.cost > 0) ? (${uniquePrefix}data.value / ${uniquePrefix}data.cost).toFixed(2) : 'N/A';
      ${uniquePrefix}output += ${uniquePrefix}key.padEnd(18) + '| ' +
                 ${uniquePrefix}data.impressions.toString().padStart(7) + ' | ' +
                 ${uniquePrefix}data.clicks.toString().padStart(7) + ' | ' +
                 ${uniquePrefix}data.cost.toFixed(2).padStart(7) + ' | ' +
                 ${uniquePrefix}data.conversions.toFixed(2).padStart(7) + ' | ' +
                 ${uniquePrefix}data.value.toFixed(2).padStart(9) + ' | ' +
                 ${uniquePrefix}cpa.toString().padStart(7) + ' | ' +
                 ${uniquePrefix}roas.toString().padStart(7) + '\\n';
    }
  }
  ${uniquePrefix}output += '--------------------------------------------------------------------' + '\\n';
  Logger.log(${uniquePrefix}output);

  // Create a new Google Sheet and paste the report
  var ${uniquePrefix}spreadsheet = SpreadsheetApp.create(${uniquePrefix}reportName + ' - ' + Utilities.formatDate(new Date(), AdsApp.currentAccount().getTimeZone(), 'yyyy-MM-dd'));
  var ${uniquePrefix}sheet = ${uniquePrefix}spreadsheet.getSheets()[0];
  
  // Create header row
  ${uniquePrefix}sheet.appendRow(['Campaign Type', 'Impressions', 'Clicks', 'Cost', 'Conversions', 'Conversion Value', 'CPA', 'ROAS']);
  
  // Append data rows
  for (var ${uniquePrefix}key in ${uniquePrefix}performanceByType) {
    var ${uniquePrefix}data = ${uniquePrefix}performanceByType[${uniquePrefix}key];
    if (${uniquePrefix}data.impressions > 0 || ${uniquePrefix}data.clicks > 0) {
      var ${uniquePrefix}cpa = (${uniquePrefix}data.conversions > 0) ? (${uniquePrefix}data.cost / ${uniquePrefix}data.conversions) : null;
      var ${uniquePrefix}roas = (${uniquePrefix}data.value > 0 && ${uniquePrefix}data.cost > 0) ? (${uniquePrefix}data.value / ${uniquePrefix}data.cost) : null;
      ${uniquePrefix}sheet.appendRow([
        ${uniquePrefix}key,
        ${uniquePrefix}data.impressions,
        ${uniquePrefix}data.clicks,
        ${uniquePrefix}data.cost,
        ${uniquePrefix}data.conversions,
        ${uniquePrefix}data.value,
        ${uniquePrefix}cpa,
        ${uniquePrefix}roas
      ]);
    }
  }
  
  // Auto-resize columns
  for (var i = 1; i <= ${uniquePrefix}sheet.getLastColumn(); i++) {
    ${uniquePrefix}sheet.autoResizeColumn(i);
  }

  var ${uniquePrefix}reportUrl = ${uniquePrefix}spreadsheet.getUrl();
  Logger.log('Report URL: ' + ${uniquePrefix}reportUrl);
  
  // Send comprehensive notification with account info and report URL
  if (true) {
    var ${uniquePrefix}account = AdsApp.currentAccount();
    var ${uniquePrefix}accountName = ${uniquePrefix}account.getName();
    var ${uniquePrefix}accountId = ${uniquePrefix}account.getCustomerId();
    var ${uniquePrefix}timestamp = Utilities.formatDate(new Date(), ${uniquePrefix}account.getTimeZone(), 'yyyy-MM-dd HH:mm:ss z');
    
    var ${uniquePrefix}message = '✅ Script Execution Finished Successfully' + '\\n' +
                   '------------------------------------' + '\\n' +
                   '📊 Script: Campaign Performance Report' + '\\n' +
                   '🏭 Account: ' + ${uniquePrefix}accountName + ' (' + ${uniquePrefix}accountId + ')' + '\\n' +
                   '📅 Date: ' + ${uniquePrefix}timestamp + '\\n' +
                   '🔍 Report Period: ' + ${uniquePrefix}dateRange + '\\n' +
                   '------------------------------------' + '\\n' +
                   '📋 <a href="' + ${uniquePrefix}reportUrl + '">Click here to open the Google Sheet report</a>';
    
    sendTelegramNotification(${uniquePrefix}message);
  }
}
`; 

    return `${telegramCode}

${mainScript}`;
  };

  const pageTitle = t('tools.campaignPerformance.title');
  const pageDescription = t('tools.campaignPerformance.description');

  const howThisWorksContent = (
    <div className="space-y-4">
      <h3 className="text-xl font-semibold text-slate-200">{t('tools.campaignPerformance.howItWorks')}</h3>
      <ul className="list-disc list-inside space-y-1 text-slate-300">
        <li><strong>Data Aggregation:</strong> The script fetches data from the `CAMPAIGN_PERFORMANCE_REPORT` for all enabled campaigns within the selected date range.</li>
        <li><strong>Categorization:</strong> It groups performance metrics (Impressions, Clicks, Cost, Conversions, Conversion Value) by `AdvertisingChannelType` (e.g., SEARCH, DISPLAY, PERFORMANCE_MAX, DEMAND_GEN).</li>
        <li><strong>Calculations:</strong> CPA (Cost Per Acquisition) and ROAS (Return On Ad Spend) are calculated for each channel type.</li>
        <li><strong>Logging:</strong> A summary of the performance by type is logged within Google Ads scripts.</li>
        <li><strong>Google Sheet Output:</strong> A new Google Sheet is created with the report name and date. The aggregated data is written to this sheet, and columns are auto-resized for readability. The URL of the sheet is logged.</li>
        <li><strong>Telegram Notifications (Optional):</strong> If enabled, a summary of the report and the Google Sheet URL are sent to the specified Telegram chat.</li>
      </ul>
      <p className="text-sm text-slate-400 mt-2"><strong>Tip:</strong> Schedule this script to run periodically (e.g., daily, weekly, or monthly) to track campaign performance trends.</p>
    </div>
  );

  if (showResult) {
    return (
      <ToolPageLayout title={pageTitle} description={pageDescription} howThisWorksContent={howThisWorksContent}>
        <div id="scriptDisplaySectionCampPerf" className="mt-8">
          <h3 className="text-2xl font-semibold text-slate-100 mb-6">{t('tools.campaignPerformance.generated')}</h3>
          {message && <NotificationMessage type={message.type} message={message.text} />}
          <ScriptDisplay scriptContent={generatedScript} />
          <StyledButton
            onClick={() => {
              setShowResult(false);
              setMessage(null);
            }}
            variant="outline"
            className="mt-8 w-full sm:w-auto"
            themeColor="slate"
            leftIcon={<PlayCircle className="mr-2 h-5 w-5" />}
          >
            {t('common.backToForm')}
          </StyledButton>
        </div>
      </ToolPageLayout>
    );
  }

  return (
    <ToolPageLayout title={pageTitle} description={pageDescription} howThisWorksContent={howThisWorksContent}>
      {message && <NotificationMessage type={message.type} message={message.text} className="mb-6" />}

      <form onSubmit={(e) => { e.preventDefault(); handleGenerateScript(); }} className="space-y-8 mt-8">
        <FormSection title={t('tools.campaignPerformance.sections.report')} icon={<Calendar />} theme="sky">
          <FormItem
            label={t('tools.campaignPerformance.fields.dateRange')}
            htmlFor="dateRangeCampPerf"
            tooltipText={t('tools.campaignPerformance.fields.dateRange')}
            required
          >
            <select 
              id="dateRangeCampPerf" 
              name="dateRange" 
              value={dateRange} 
              onChange={handleInputChange}
              className={`
                form-select block w-full rounded-md 
                bg-slate-700/50 border-slate-600 
                text-gray-100 placeholder-gray-400 
                shadow-sm transition-colors duration-150 ease-in-out 
                focus:border-transparent focus:bg-slate-600/80 focus:outline-none focus:ring-2 focus:ring-sky-500
                px-3 py-2 text-sm
              `}
            >
              {dateRangeOptions.map(option => (
                <option key={option.value} value={option.value} className="bg-slate-800 text-slate-100">{option.label}</option>
              ))}
            </select>
          </FormItem>
          <FormItem
            label={t('tools.campaignPerformance.fields.reportName')}
            htmlFor="reportNameCampPerf"
            tooltipText={t('tools.campaignPerformance.fields.reportName')}
            required
          >
            <StyledInput 
              type="text" 
              id="reportNameCampPerf" 
              name="reportName" 
              value={reportName} 
              onChange={handleInputChange} 
              placeholder="e.g., Q1 Campaign Review"
            />
          </FormItem>
        </FormSection>

        <FormSection title={t('tools.campaignPerformance.sections.notifications')} icon={<Bell />} theme="purple" collapsible initiallyCollapsed={!useTelegram && !telegramBotToken && !telegramChatId}>
          <div className="mb-4">
            <label className="flex items-center cursor-pointer group">
              <div className="relative">
                <input
                  type="checkbox"
                  id="useTelegramCampPerf"
                  name="useTelegram"
                  checked={useTelegram}
                  onChange={handleInputChange}
                  className="sr-only"
                />
                <div className={`
                  w-5 h-5 rounded border-2 flex items-center justify-center transition-all duration-200
                  ${useTelegram 
                    ? 'bg-sky-600 border-sky-600' 
                    : 'bg-slate-700/50 border-slate-500 group-hover:border-slate-400'}
                  }`}
                >
                  {useTelegram && (
                    <svg className="w-3 h-3 text-white" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
              </div>
              <span className="ml-3 text-gray-300 text-sm font-medium">
                {t('common.enableTelegram')}
              </span>
            </label>
          </div>
          {useTelegram && (
            <>
              <FormItem label={t('tools.campaignPerformance.fields.telegramToken')} htmlFor="telegramBotTokenCampPerf" tooltipText={t('tools.campaignPerformance.fields.telegramToken')} required={useTelegram}>
                <StyledInput type="text" id="telegramBotTokenCampPerf" name="telegramBotToken" value={telegramBotToken} onChange={handleInputChange} placeholder="123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11" />
              </FormItem>
              <FormItem label={t('tools.campaignPerformance.fields.telegramChatId')} htmlFor="telegramChatIdCampPerf" tooltipText={t('tools.campaignPerformance.fields.telegramChatId')} required={useTelegram}>
                <StyledInput type="text" id="telegramChatIdCampPerf" name="telegramChatId" value={telegramChatId} onChange={handleInputChange} placeholder="-1001234567890 or @channelname" />
              </FormItem>
            </>
          )}
        </FormSection>

        <div className="flex flex-col sm:flex-row gap-4 pt-4">
          <StyledButton type="submit" variant="primary" size="lg" themeColor="emerald" leftIcon={<PlayCircle className="mr-2 h-5 w-5" />}>
            {t('common.generateScript')}
          </StyledButton>
        </div>
      </form>
    </ToolPageLayout>
  );
};

export default CampaignPerformance;
