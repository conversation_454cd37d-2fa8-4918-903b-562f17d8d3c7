import React, { useState, useEffect, useCallback } from 'react';
import { ListChecks, DollarSign, MessageSquare, Lightbulb, Hotel } from 'lucide-react'; 

import BackButton from '../common/BackButton';
import ToolPageLayout from '../ui/shared/ToolPageLayout';
import FormSection from '../ui/shared/FormSection';
import FormItem from '../ui/shared/FormItem';
import StyledInput from '../ui/shared/StyledInput';
import StyledSelect from '../ui/shared/StyledSelect';
import StyledButton from '../ui/shared/StyledButton';
import ScriptDisplay from '../ui/shared/ScriptDisplay';
import NotificationMessage from '../ui/shared/NotificationMessage';
import { TooltipProvider } from '@/components/ui/tooltip'; 

// eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any
declare let AdsApp: any;
// eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any
declare let Logger: any;
// eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any
declare let Utilities: any;
// eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any
declare let UrlFetchApp: any;

const MAX_HISTORY_ITEMS = 3;
const historyPrefix = 'gadsBudgetUpdaterTool_'; 

const campaignTypeOptions = [
  { value: '', label: 'Всі типи (Рекомендовано, якщо не впевнені)' },
  { value: 'SEARCH', label: 'Пошук' },
  { value: 'PERFORMANCE_MAX', label: 'Performance Max' },
  { value: 'SHOPPING', label: 'Торгівля' },
  { value: 'DISPLAY', label: 'Медійна' },
  { value: 'VIDEO', label: 'Відео' },
  { value: 'APP', label: 'Додаток' },
  { value: 'DEMAND_GEN', label: 'Demand Gen' },
  { value: 'SMART', label: 'Розумна', icon: Lightbulb },
  { value: 'HOTEL', label: 'Готель', icon: Hotel },
];

const GAdsBudgetUpdaterTool: React.FC = () => {
  const [campaignName, setCampaignName] = useState('');
  const [maxBudget, setMaxBudget] = useState('1000');
  const [minBudget, setMinBudget] = useState('10'); 
  const [maxTimes, setMaxTimes] = useState('5');
  const [upPercent, setUpPercent] = useState('10');
  const [maxBudgetIncrease, setMaxBudgetIncrease] = useState('50'); 
  const [telegramBotToken, setTelegramBotToken] = useState('');
  const [telegramChatId, setTelegramChatId] = useState('');
  const [campaignType, setCampaignType] = useState('');

  const [script, setScript] = useState('');
  const [message, setMessage] = useState<{ text: string; type: 'success' | 'error' } | null>(null);

  const getSavedValues = useCallback((field: string): string[] => {
    try {
      const savedData = localStorage.getItem(`${historyPrefix}${field}_history`);
      return savedData ? JSON.parse(savedData) : [];
    } catch (error) {
      console.error("Error reading from localStorage:", error);
      return [];
    }
  }, []);

  const saveFieldValueToHistory = useCallback((field: string, value: string) => {
    if (!value || !value.trim()) return; 
    try {
      let values = getSavedValues(field);
      values = values.filter(v => v !== value);
      values.unshift(value);
      values = values.slice(0, MAX_HISTORY_ITEMS);
      localStorage.setItem(`${historyPrefix}${field}_history`, JSON.stringify(values));
    } catch (error) {
      console.error("Error writing to localStorage:", error);
    }
  }, [getSavedValues]);

  useEffect(() => {
    setCampaignName(getSavedValues('campaignName')[0] || 'Website traffic-Search-1');
    setMaxBudget(getSavedValues('maxBudget')[0] || '1300');
    setMaxTimes(getSavedValues('maxTimes')[0] || '10');
    setUpPercent(getSavedValues('upPercent')[0] || '0.1');
    setTelegramBotToken(getSavedValues('telegramBotToken')[0] || '');
    setTelegramChatId(getSavedValues('telegramChatId')[0] || '');
    setCampaignType(getSavedValues('campaignType')[0] || '');
  }, [getSavedValues]);

  const handleInputChange = (
    setter: React.Dispatch<React.SetStateAction<string>>,
    fieldName: string
  ) => (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const value = e.target.value;
    setter(value);
    saveFieldValueToHistory(fieldName, value);
  };

  const handleGenerateScript = () => {
    setMessage(null);
    if (!campaignName || !maxBudget || !maxTimes || !upPercent) {
      setMessage({ text: 'Please fill in all required Google Ads fields.', type: 'error' });
      return;
    }
    if (parseFloat(maxBudget) <= 0 || parseInt(maxTimes, 10) <= 0 || parseFloat(upPercent) <= 0) {
      setMessage({ text: 'Budget, number of increases, and percentage must be positive values.', type: 'error' });
      return;
    }

    saveFieldValueToHistory('campaignName', campaignName);
    saveFieldValueToHistory('maxBudget', maxBudget);
    saveFieldValueToHistory('maxTimes', maxTimes);
    saveFieldValueToHistory('upPercent', upPercent);
    if (telegramBotToken) saveFieldValueToHistory('telegramBotToken', telegramBotToken);
    if (telegramChatId) saveFieldValueToHistory('telegramChatId', telegramChatId);
    if (campaignType) saveFieldValueToHistory('campaignType', campaignType);

    const uniqueSuffix = '_' + Date.now().toString(36) + Math.random().toString(36).substring(2, 5);

    const mainFunc = `main`;
    const configVar = `config${uniqueSuffix}`;
    const campaignVar = `campaign${uniqueSuffix}`;
    const resultVar = `result${uniqueSuffix}`;
    const errorVar = `error${uniqueSuffix}`;
    const msgVar = `msg${uniqueSuffix}`;

    const adjustBudgetFunc = `adjustBudget${uniqueSuffix}`;
    const reportResultFunc = `reportResult${uniqueSuffix}`;
    const findCampaignFunc = `findCampaign${uniqueSuffix}`;
    const getAccountInfoFunc = `getAccountInfo${uniqueSuffix}`;
    const getDateTimeFunc = `getDateTime${uniqueSuffix}`;
    const sendTelegramFunc = `sendTelegram${uniqueSuffix}`;

    const generatedScriptContent: any = `function ${mainFunc}() {

function ${getAccountInfoFunc}() {
  const account = AdsApp.currentAccount();
  return '🏭 Account: ' + account.getName() + ' (' + account.getCustomerId() + ')';
}

function ${getDateTimeFunc}() {
  const now = new Date();
  const timeZone = AdsApp.currentAccount().getTimeZone();
  const formattedDate = Utilities.formatDate(now, timeZone, 'yyyy-MM-dd HH:mm:ss z');
  return '📅 Date: ' + formattedDate;
}

function ${sendTelegramFunc}(message) {
  const token = '${telegramBotToken.replace(/'/g, "\\'")}';
  const chatId = '${telegramChatId.replace(/'/g, "\\'")}';
  if (!token || !chatId) return;
  const url = 'https://api.telegram.org/bot' + token + '/sendMessage?chat_id=' + chatId + '&text=' + encodeURIComponent(message) + '&parse_mode=Markdown';
  try {
    UrlFetchApp.fetch(url);
  } catch (e) {
    Logger.log('Telegram notification failed: ' + e.message);
  }
}

function ${findCampaignFunc}(campaignName, campaignType) {
  let campaignIterator;
  let selector;

  if (campaignType === 'PERFORMANCE_MAX') {
    selector = AdsApp.performanceMaxCampaigns();
  } else if (campaignType === 'SHOPPING') {
    selector = AdsApp.shoppingCampaigns();
  } else if (campaignType === 'DISPLAY') {
    selector = AdsApp.displayCampaigns();
  } else if (campaignType === 'VIDEO') {
    selector = AdsApp.videoCampaigns();
  } else if (campaignType === 'APP') {
    selector = AdsApp.appCampaigns();
  } else if (campaignType === 'SEARCH') {
    selector = AdsApp.campaigns().withCondition("AdvertisingChannelType = SEARCH");
  } else if (campaignType === 'DEMAND_GEN') { 
    selector = AdsApp.campaigns().withCondition("AdvertisingChannelType = 'DEMAND_GEN'");
  } else if (campaignType === '' || !campaignType) { // Handle 'All Types' or undefined/empty
    selector = AdsApp.campaigns();
  } else {
    const errMsg_ = 'Unsupported campaign type: ' + campaignType + '. Please specify a valid type or leave empty for all types.';
    Logger.log(errMsg_);
    ${sendTelegramFunc}(errMsg_); 
    return null;
  }

  // The campaignName parameter is expected to have single quotes already escaped if they were present in the original input.
  // The .withCondition builds the query string using this (potentially escaped) campaignName.
  campaignIterator = selector
    .withCondition("Name = '" + campaignName + "'")
    .get();

  if (campaignIterator.hasNext()) {
    return campaignIterator.next();
  }
  
  // Logging uses the campaignName as is (it might contain an escaped single quote e.g. \\').
  // Example for unescaping if needed for display: someString.replace(/\\'/g, "'"); 
  const notFoundMsg_ = "Campaign with name '" + campaignName + "' and type '" + (campaignType || 'Any') + "' not found.";
  Logger.log(notFoundMsg_);
  // Optionally, notify if not found: ${sendTelegramFunc}(notFoundMsg_);
  return null;
}

function ${adjustBudgetFunc}(campaign, config) {
  const currentBudget = campaign.getBudget().getAmount();
  let newBudget = currentBudget;
  const changes = [];
  let incrementsApplied = 0;

  if (currentBudget >= config.MAX_BUDGET) {
    return { campaignName: campaign.getName(), campaignId: campaign.getId(), currentBudget: currentBudget, newBudget: currentBudget, changes: [] };
  }

  if (currentBudget < config.MIN_BUDGET) {
    throw new Error('Current budget (' + currentBudget + ') is below minimum allowed (' + config.MIN_BUDGET + ').');
  }

  for (let i = 0; i < config.MAX_INCREMENTS; i++) {
    const potentialNextBudget = newBudget * (1 + config.INCREMENT_PERCENT);
    if (potentialNextBudget <= config.MAX_BUDGET) {
      const oldBudgetValue = newBudget;
      newBudget = potentialNextBudget;
      incrementsApplied++;
      changes.push({
        increment: incrementsApplied,
        oldBudget: oldBudgetValue,
        newBudget: newBudget,
        changePercent: (config.INCREMENT_PERCENT * 100).toFixed(1) + '%'
      });
    } else {
      if (newBudget < config.MAX_BUDGET) {
        const oldBudgetValue = newBudget;
        newBudget = config.MAX_BUDGET;
        incrementsApplied++;
        changes.push({
          increment: incrementsApplied,
          oldBudget: oldBudgetValue,
          newBudget: newBudget,
          changePercent: (((config.MAX_BUDGET - oldBudgetValue) / oldBudgetValue) * 100).toFixed(1) + '%'
        });
      }
      break;
    }
  }

  const totalIncreasePercent = ((newBudget - currentBudget) / currentBudget) * 100;
  if (totalIncreasePercent > config.MAX_BUDGET_INCREASE_PERCENT) {
    throw new Error('Proposed budget increase (' + totalIncreasePercent.toFixed(1) + '%) exceeds maximum allowed (' + config.MAX_BUDGET_INCREASE_PERCENT + '%).');
  }

  if (changes.length > 0) {
    campaign.getBudget().setAmount(newBudget);
  }

  return {
    campaignName: campaign.getName(),
    campaignId: campaign.getId(),
    currentBudget: currentBudget,
    newBudget: newBudget,
    changes: changes
  };
}

function ${reportResultFunc}(result, config) {
  const accountInfo = ${getAccountInfoFunc}();
  const dateTimeInfo = ${getDateTimeFunc}();

  let message = '📊 *Budget Update Report*\\n' + accountInfo + '\\n' + dateTimeInfo + '\\n\\n';
  message += 'Campaign: ' + result.campaignName + ' (ID: ' + result.campaignId + ')\\n';
  message += '🔹 Initial Budget: $' + result.currentBudget.toFixed(2) + '\\n';
  
  if (result.changes.length > 0) {
    message += '🆕 Final Budget: $' + result.newBudget.toFixed(2) + '\\n';
    message += '📈 Total Increase: $' + (result.newBudget - result.currentBudget).toFixed(2) + ' (' + ((result.newBudget / result.currentBudget - 1) * 100).toFixed(1) + '%)\\n\\n';
    message += '🔄 Changes Applied:\\n';
    result.changes.forEach(change => {
      message += '  • Increment ' + change.increment + ': $' + change.oldBudget.toFixed(2) + ' → $' + change.newBudget.toFixed(2) + ' (' + change.changePercent + ')\\n';
    });
  } else {
    message += '✅ Budget already at or above target of $' + config.MAX_BUDGET + '. No changes made.\\n';
  }

  message += '\\nℹ️ *Configuration*\\n';
  message += '• Max Budget: $' + config.MAX_BUDGET + '\\n';
  message += '• Min Budget: $' + config.MIN_BUDGET + '\\n'; 
  message += '• Max Increments: ' + config.MAX_INCREMENTS + '\\n';
  message += '• Increment: ' + (config.INCREMENT_PERCENT * 100) + '%\\n';
  message += '• Max Budget Increase: ' + config.MAX_BUDGET_INCREASE_PERCENT + '%\\n';

  Logger.log(message);
  ${sendTelegramFunc}(message);
}

  const ${configVar} = {
    TARGET_CAMPAIGN_NAME: '${campaignName.replace(/'/g, "\\'")}',
    CAMPAIGN_TYPE: '${campaignType}',
    MAX_BUDGET: parseFloat('${maxBudget}'),
    MIN_BUDGET: parseFloat('${minBudget}'),
    MAX_INCREMENTS: parseInt('${maxTimes}'),
    INCREMENT_PERCENT: parseFloat('${upPercent}') / 100,
    MAX_BUDGET_INCREASE_PERCENT: parseFloat('${maxBudgetIncrease}')
  };

  if (isNaN(${configVar}.MAX_BUDGET) || ${configVar}.MAX_BUDGET <= 0) throw new Error('Invalid MAX_BUDGET: ' + ${configVar}.MAX_BUDGET);
  if (isNaN(${configVar}.MIN_BUDGET) || ${configVar}.MIN_BUDGET < 0) throw new Error('Invalid MIN_BUDGET: ' + ${configVar}.MIN_BUDGET);
  if (isNaN(${configVar}.MAX_INCREMENTS) || ${configVar}.MAX_INCREMENTS <= 0) throw new Error('Invalid MAX_INCREMENTS: ' + ${configVar}.MAX_INCREMENTS);
  if (isNaN(${configVar}.INCREMENT_PERCENT) || ${configVar}.INCREMENT_PERCENT <= 0) throw new Error('Invalid INCREMENT_PERCENT: ' + ${configVar}.INCREMENT_PERCENT);
  if (isNaN(${configVar}.MAX_BUDGET_INCREASE_PERCENT) || ${configVar}.MAX_BUDGET_INCREASE_PERCENT <= 0) throw new Error('Invalid MAX_BUDGET_INCREASE_PERCENT: ' + ${configVar}.MAX_BUDGET_INCREASE_PERCENT);
  if (${configVar}.MIN_BUDGET > ${configVar}.MAX_BUDGET) throw new Error('MIN_BUDGET ('+${configVar}.MIN_BUDGET+') cannot be greater than MAX_BUDGET ('+${configVar}.MAX_BUDGET+').');

  try {
    const ${campaignVar} = ${findCampaignFunc}(${configVar}.TARGET_CAMPAIGN_NAME, ${configVar}.CAMPAIGN_TYPE);
    if (!${campaignVar}) {
      const ${msgVar} = '❌ Campaign "' + ${configVar}.TARGET_CAMPAIGN_NAME + '" not found. Please check the name and type.';
      Logger.log(${msgVar});
      ${sendTelegramFunc}(${msgVar});
      return;
    }

    const ${resultVar} = ${adjustBudgetFunc}(${campaignVar}, ${configVar});
    ${reportResultFunc}(${resultVar}, ${configVar});

  } catch (${errorVar}) {
    const errorMsg = '❌ Script execution failed: ' + ${errorVar}.message;
    Logger.log(errorMsg);
    Logger.log(${errorVar}.stack);
    ${sendTelegramFunc}(errorMsg);
  }
}
/* eslint-enable no-undef */
// @ts-ignore
`;

    setScript(generatedScriptContent);
    setMessage({ text: 'Script generated successfully!', type: 'success'});
    setTimeout(() => {
        const resultSection = document.getElementById('budgetUpdaterResultSection');
        resultSection?.scrollIntoView({ behavior: 'smooth' });
    }, 100);

  };

  return (
    <TooltipProvider>
      <ToolPageLayout
        title="Оновлювач бюджету Google Ads"
        description="Автоматично збільшує бюджети кампаній до вказаного максимуму з опціональними сповіщеннями в Telegram."
      >
        <BackButton />
        <div className="space-y-6">
          <FormSection icon={<ListChecks />} title="Налаштування кампанії" theme="blue">
            <FormItem label="Шаблон назви кампанії" tooltipText="Введіть точну назву або шаблон кампанії(й). Враховується регістр.">
              <StyledInput 
                name="campaignName"
                value={campaignName}
                onChange={handleInputChange(setCampaignName, 'campaignName')}
                placeholder="e.g., Brand - Search - Exact"
                list={`${historyPrefix}campaignName_history_list`}
              />
              <datalist id={`${historyPrefix}campaignName_history_list`}>
                {getSavedValues('campaignName').map(val => <option key={val} value={val} />)}
              </datalist>
            </FormItem>
            <FormItem
                label="Тип кампанії (Опціонально)"
                tooltipText="Вкажіть тип кампанії, якщо хочете переконатися, що скрипт націлений на конкретний тип (наприклад, PERFORMANCE_MAX). Залиште порожнім для пошуку серед усіх типів."
            >
              <StyledSelect
                name="campaignType"
                value={campaignType}
                onChange={handleInputChange(setCampaignType, 'campaignType')}
                className="mt-1"
              >
                {campaignTypeOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </StyledSelect>
            </FormItem>
          </FormSection>

          <FormSection icon={<DollarSign />} title="Параметри бюджету" theme="emerald">
            <FormItem label="Максимальний бюджет" tooltipText="Абсолютний максимальний бюджет, який кампанія не повинна перевищувати.">
              <StyledInput 
                type="number"
                name="maxBudget"
                value={maxBudget}
                onChange={handleInputChange(setMaxBudget, 'maxBudget')}
                placeholder="e.g., 1500"
                list={`${historyPrefix}maxBudget_history_list`}
              />
              <datalist id={`${historyPrefix}maxBudget_history_list`}>
                {getSavedValues('maxBudget').map(val => <option key={val} value={val} />)}
              </datalist>
            </FormItem>
            <FormItem label="Мінімальний бюджет" tooltipText="Мінімальний бюджет, нижче якого кампанія не повинна опускатися.">
              <StyledInput 
                type="number"
                name="minBudget"
                value={minBudget}
                onChange={handleInputChange(setMinBudget, 'minBudget')}
                placeholder="e.g., 10"
                list={`${historyPrefix}minBudget_history_list`}
              />
              <datalist id={`${historyPrefix}minBudget_history_list`}>
                {getSavedValues('minBudget').map(val => <option key={val} value={val} />)}
              </datalist>
            </FormItem>
            <FormItem label="Макс. збільшень" tooltipText="Максимальна кількість разів, коли бюджет може бути збільшений.">
              <StyledInput 
                type="number"
                name="maxTimes"
                value={maxTimes}
                onChange={handleInputChange(setMaxTimes, 'maxTimes')}
                placeholder="e.g., 10"
                list={`${historyPrefix}maxTimes_history_list`}
              />
              <datalist id={`${historyPrefix}maxTimes_history_list`}>
                {getSavedValues('maxTimes').map(val => <option key={val} value={val} />)}
              </datalist>
            </FormItem>
            <FormItem label="Відсоток збільшення" tooltipText="Відсоток (наприклад, 0.1 для 10%) на який збільшувати бюджет. Застосовується до поточного бюджету.">
              <StyledInput 
                type="number"
                name="upPercent"
                value={upPercent}
                onChange={handleInputChange(setUpPercent, 'upPercent')}
                placeholder="e.g., 0.1 for 10%"
                step="0.01"
                list={`${historyPrefix}upPercent_history_list`}
              />
              <datalist id={`${historyPrefix}upPercent_history_list`}>
                {getSavedValues('upPercent').map(val => <option key={val} value={val} />)}
              </datalist>
            </FormItem>
            <FormItem label="Макс. збільшення бюджету" tooltipText="Максимальний відсоток, на який може бути збільшений бюджет.">
              <StyledInput 
                type="number"
                name="maxBudgetIncrease"
                value={maxBudgetIncrease}
                onChange={handleInputChange(setMaxBudgetIncrease, 'maxBudgetIncrease')}
                placeholder="e.g., 50"
                list={`${historyPrefix}maxBudgetIncrease_history_list`}
              />
              <datalist id={`${historyPrefix}maxBudgetIncrease_history_list`}>
                {getSavedValues('maxBudgetIncrease').map(val => <option key={val} value={val} />)}
              </datalist>
            </FormItem>
          </FormSection>

          <FormSection icon={<MessageSquare />} title="Налаштування сповіщень (Опціонально)" theme="purple">
            <FormItem
                label="Токен Telegram бота"
                tooltipText="Ваш токен Telegram бота для сповіщень. Зберігається локально."
            >
              <StyledInput 
                name="telegramBotToken"
                value={telegramBotToken}
                onChange={handleInputChange(setTelegramBotToken, 'telegramBotToken')}
                placeholder="Your Telegram Bot Token"
                type="password"
                list={`${historyPrefix}telegramBotToken_history_list`}
              />
              <datalist id={`${historyPrefix}telegramBotToken_history_list`}>
                {getSavedValues('telegramBotToken').map(val => <option key={val} value={val} />)}
              </datalist>
            </FormItem>
            <FormItem
                label="ID чату Telegram"
                tooltipText="Ваш ID чату Telegram для сповіщень. Зберігається локально."
            >
              <StyledInput 
                name="telegramChatId"
                value={telegramChatId}
                onChange={handleInputChange(setTelegramChatId, 'telegramChatId')}
                placeholder="Your Telegram Chat ID"
                type="text" 
                list={`${historyPrefix}telegramChatId_history_list`}
              />
               <datalist id={`${historyPrefix}telegramChatId_history_list`}>
                {getSavedValues('telegramChatId').map(val => <option key={val} value={val} />)}
              </datalist>
            </FormItem>
          </FormSection>

          {message && (
            <NotificationMessage
              type={message.type} 
              message={message.text} 
              onDismiss={() => setMessage(null)}
              className="mb-6"
            />
          )}

          <StyledButton onClick={handleGenerateScript} className="w-full">
            Generate Script
          </StyledButton>

          {script && (
            <div id="budgetUpdaterResultSection" className="mt-6">
              <ScriptDisplay
                scriptContent={script}
                title="Згенерований скрипт оновлення бюджету"
                language="javascript"
              />
            </div>
          )}
        </div>
      </ToolPageLayout>
    </TooltipProvider>
  );
};

export default GAdsBudgetUpdaterTool;
