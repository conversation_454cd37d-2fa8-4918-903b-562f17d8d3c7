import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>2, <PERSON><PERSON><PERSON>, <PERSON><PERSON>Triangle, MessageSquare, PlayCircle } from 'lucide-react'; 

import ToolPageLayout from '../ui/shared/ToolPageLayout';
import FormSection from '../ui/shared/FormSection';
import FormItem from '../ui/shared/FormItem';
import StyledInput from '../ui/shared/StyledInput';
import StyledButton from '../ui/shared/StyledButton';
import ScriptDisplay from '../ui/shared/ScriptDisplay';
import NotificationMessage from '../ui/shared/NotificationMessage';

const LOCAL_STORAGE_PREFIX = 'adPerformance_';

const AdPerformance: React.FC = () => {
  const [campaignName, setCampaignName] = useState('');
  const [confidenceLevel, setConfidenceLevel] = useState('0.95');
  const [minClicks, setMinClicks] = useState('100');
  const [minImpressions, setMinImpressions] = useState('1000');
  const [dateRange, setDateRange] = useState('LAST_30_DAYS');
  const [maxCpcDifference, setMaxCpcDifference] = useState('20');
  const [adLabel, setAdLabel] = useState('Paused by Ad Performance Script');

  const [useTelegram, setUseTelegram] = useState(false);
  const [telegramBotToken, setTelegramBotToken] = useState('');
  const [telegramChatId, setTelegramChatId] = useState('');

  const [generatedScript, setGeneratedScript] = useState('');
  const [showResult, setShowResult] = useState(false);
  const [message, setMessage] = useState<{text: string, type: 'error' | 'success' | 'info'} | null>(null);

  useEffect(() => {
    const fields: string[] = ['campaignName', 'confidenceLevel', 'minClicks', 'minImpressions', 'dateRange', 'maxCpcDifference', 'adLabel', 'useTelegram', 'telegramBotToken', 'telegramChatId'];
    fields.forEach(field => {
      const savedValue = localStorage.getItem(`${LOCAL_STORAGE_PREFIX}${field}`);
      if (savedValue !== null) {
        if (field === 'useTelegram') setUseTelegram(savedValue === 'true');
        else if (field === 'campaignName') setCampaignName(savedValue);
        else if (field === 'confidenceLevel') setConfidenceLevel(savedValue);
        else if (field === 'minClicks') setMinClicks(savedValue);
        else if (field === 'minImpressions') setMinImpressions(savedValue);
        else if (field === 'dateRange') setDateRange(savedValue);
        else if (field === 'maxCpcDifference') setMaxCpcDifference(savedValue);
        else if (field === 'adLabel') setAdLabel(savedValue);
        else if (field === 'telegramBotToken') setTelegramBotToken(savedValue);
        else if (field === 'telegramChatId') setTelegramChatId(savedValue);
      }
    });
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    setMessage(null);
    const { name, value, type } = e.target;
    const val = type === 'checkbox' ? (e.target as HTMLInputElement).checked : value;

    if (name === 'campaignName') setCampaignName(val as string);
    else if (name === 'confidenceLevel') setConfidenceLevel(val as string);
    else if (name === 'minClicks') setMinClicks(val as string);
    else if (name === 'minImpressions') setMinImpressions(val as string);
    else if (name === 'dateRange') setDateRange(val as string);
    else if (name === 'maxCpcDifference') setMaxCpcDifference(val as string);
    else if (name === 'adLabel') setAdLabel(val as string);
    else if (name === 'useTelegram') setUseTelegram(val as boolean);
    else if (name === 'telegramBotToken') setTelegramBotToken(val as string);
    else if (name === 'telegramChatId') setTelegramChatId(val as string);

    localStorage.setItem(`${LOCAL_STORAGE_PREFIX}${name}`, String(val));
  };

  const handleGenerateScript = () => {
    setMessage(null);
    if (useTelegram && (!telegramBotToken || !telegramChatId)) {
      setMessage({ text: 'Please enter both Telegram Bot Token and Chat ID for notifications.', type: 'error' });
      return;
    }
    if (isNaN(parseFloat(minClicks)) || isNaN(parseFloat(minImpressions)) || isNaN(parseFloat(maxCpcDifference))) {
        setMessage({ text: 'Min Clicks, Min Impressions, and Max CPC Difference must be valid numbers.', type: 'error' });
        return;
    }

    const script = generateAdPerformanceScript(
      campaignName,
      confidenceLevel,
      parseInt(minClicks, 10),
      parseInt(minImpressions, 10),
      dateRange,
      parseFloat(maxCpcDifference) / 100, 
      adLabel,
      useTelegram,
      telegramBotToken,
      telegramChatId
    );
    setGeneratedScript(script);
    setShowResult(true);
    setTimeout(() => {
      document.getElementById('scriptDisplaySectionAdPerf')?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  };

  const generateAdPerformanceScript = (
    campaignNameVal: string,
    confidenceLevelVal: string,
    minClicksVal: number,
    minImpressionsVal: number,
    dateRangeVal: string,
    maxCpcDifferenceVal: number,
    adLabelVal: string,
    useTelegramVal: boolean,
    botToken: string,
    chatId: string
  ): string => {
    const uniquePrefix = 'adPerf' + Date.now().toString(36) + '_';
    let telegramCode = '';
    if (useTelegramVal) {
      telegramCode = `
function ${uniquePrefix}sendTelegramNotification(message) {
  var ${uniquePrefix}payload = {
    'chat_id': '${chatId.replace(/'/g, "\\'")}',
    'text': message,
    'parse_mode': 'HTML'
  };
  var ${uniquePrefix}options = {
    'method': 'post',
    'contentType': 'application/json',
    'payload': JSON.stringify(${uniquePrefix}payload)
  };
  try {
    UrlFetchApp.fetch('https://api.telegram.org/bot${botToken.replace(/'/g, "\\'")}/sendMessage', ${uniquePrefix}options);
    return true;
  } catch (e) { 
    Logger.log('Error sending Telegram notification: ' + e); 
    return false;
  }
}
`;
    }

    const mainScript = `
function main() {
  var ${uniquePrefix}CONFIG = {
    CAMPAIGN_NAME_CONTAINS: '${campaignNameVal.replace(/'/g, "\\'")}',
    CONFIDENCE_LEVEL: ${confidenceLevelVal},
    MIN_CLICKS: ${minClicksVal},
    MIN_IMPRESSIONS: ${minImpressionsVal},
    DATE_RANGE: '${dateRangeVal}',
    MAX_CPC_DIFFERENCE_PERCENTAGE: ${maxCpcDifferenceVal},
    AD_LABEL_NAME: '${adLabelVal.replace(/'/g, "\\'")}'
  };

  Logger.log('Script starting with configuration: ' + JSON.stringify(${uniquePrefix}CONFIG));
  ${useTelegramVal ? `${uniquePrefix}sendTelegramNotification('<b>📊 Ad Performance Script Started</b>' + '\\n' + 'Campaign filter: ' + (${uniquePrefix}CONFIG.CAMPAIGN_NAME_CONTAINS === '""' ? 'ALL CAMPAIGNS' : ${uniquePrefix}CONFIG.CAMPAIGN_NAME_CONTAINS));` : ''}

  var ${uniquePrefix}campaignSelector = AdsApp.campaigns();
  if (${uniquePrefix}CONFIG.CAMPAIGN_NAME_CONTAINS !== '' && ${uniquePrefix}CONFIG.CAMPAIGN_NAME_CONTAINS !== '""') {
    ${uniquePrefix}campaignSelector = ${uniquePrefix}campaignSelector.withCondition(
      "CampaignName CONTAINS_IGNORE_CASE " + ${uniquePrefix}CONFIG.CAMPAIGN_NAME_CONTAINS
    );
  }
  var ${uniquePrefix}campaignIterator = ${uniquePrefix}campaignSelector.get();

  var ${uniquePrefix}totalAdsPaused = 0;
  var ${uniquePrefix}adGroupsProcessed = 0;

  while (${uniquePrefix}campaignIterator.hasNext()) {
    var ${uniquePrefix}campaign = ${uniquePrefix}campaignIterator.next();
    Logger.log('Processing Campaign: ' + ${uniquePrefix}campaign.getName());

    var ${uniquePrefix}adGroupIterator = ${uniquePrefix}campaign.adGroups()
      .withCondition('Status = ENABLED')
      .get();

    while (${uniquePrefix}adGroupIterator.hasNext()) {
      var ${uniquePrefix}adGroup = ${uniquePrefix}adGroupIterator.next();
      ${uniquePrefix}adGroupsProcessed++;
      var ${uniquePrefix}ads = [];
      var ${uniquePrefix}adIterator = ${uniquePrefix}adGroup.ads()
        .withCondition('Status = ENABLED')
        .withCondition("AdType IN ['EXPANDED_TEXT_AD', 'RESPONSIVE_SEARCH_AD']") 
        .forDateRange(${uniquePrefix}CONFIG.DATE_RANGE)
        .get();

      while (${uniquePrefix}adIterator.hasNext()) {
        var ${uniquePrefix}ad = ${uniquePrefix}adIterator.next();
        var ${uniquePrefix}stats = ${uniquePrefix}ad.getStatsFor(${uniquePrefix}CONFIG.DATE_RANGE);
        if (${uniquePrefix}stats.getClicks() >= ${uniquePrefix}CONFIG.MIN_CLICKS && ${uniquePrefix}stats.getImpressions() >= ${uniquePrefix}CONFIG.MIN_IMPRESSIONS) {
          ${uniquePrefix}ads.push({
            adObject: ${uniquePrefix}ad,
            id: ${uniquePrefix}ad.getId(),
            clicks: ${uniquePrefix}stats.getClicks(),
            impressions: ${uniquePrefix}stats.getImpressions(),
            cost: ${uniquePrefix}stats.getCost(),
            cpc: ${uniquePrefix}stats.getAverageCpc()
          });
        }
      }

      if (${uniquePrefix}ads.length < 2) {
        Logger.log('Ad Group ' + ${uniquePrefix}adGroup.getName() + ' has less than 2 eligible ads. Skipping.');
        continue;
      }

      ${uniquePrefix}ads.sort(function(a, b) { return a.cpc - b.cpc; });

      var ${uniquePrefix}winnerAd = ${uniquePrefix}ads[0];
      var ${uniquePrefix}loserAdsFound = 0;

      for (var i = 1; i < ${uniquePrefix}ads.length; i++) {
        var ${uniquePrefix}currentAd = ${uniquePrefix}ads[i];
        var ${uniquePrefix}cpcDifference = (${uniquePrefix}currentAd.cpc - ${uniquePrefix}winnerAd.cpc) / ${uniquePrefix}winnerAd.cpc;
        
        var ${uniquePrefix}p1 = ${uniquePrefix}winnerAd.clicks / (${uniquePrefix}winnerAd.clicks + ${uniquePrefix}currentAd.clicks); 
        var ${uniquePrefix}p2 = ${uniquePrefix}currentAd.clicks / (${uniquePrefix}winnerAd.clicks + ${uniquePrefix}currentAd.clicks); 
        var ${uniquePrefix}n1 = ${uniquePrefix}winnerAd.impressions;
        var ${uniquePrefix}n2 = ${uniquePrefix}currentAd.impressions;

        var ${uniquePrefix}pPool = (${uniquePrefix}winnerAd.clicks + ${uniquePrefix}currentAd.clicks) / (${uniquePrefix}n1 + ${uniquePrefix}n2);
        var ${uniquePrefix}standardError = Math.sqrt(${uniquePrefix}pPool * (1 - ${uniquePrefix}pPool) * (1/${uniquePrefix}n1 + 1/${uniquePrefix}n2));
        var ${uniquePrefix}zScore = (${uniquePrefix}p1 - ${uniquePrefix}p2) / ${uniquePrefix}standardError; 
        
        var ${uniquePrefix}criticalZ = 0;
        if (${uniquePrefix}CONFIG.CONFIDENCE_LEVEL === 0.90) ${uniquePrefix}criticalZ = 1.645;
        else if (${uniquePrefix}CONFIG.CONFIDENCE_LEVEL === 0.95) ${uniquePrefix}criticalZ = 1.96;
        else if (${uniquePrefix}CONFIG.CONFIDENCE_LEVEL === 0.99) ${uniquePrefix}criticalZ = 2.576;

        if (${uniquePrefix}winnerAd.cpc > 0 && ${uniquePrefix}cpcDifference > ${uniquePrefix}CONFIG.MAX_CPC_DIFFERENCE_PERCENTAGE && Math.abs(${uniquePrefix}zScore) > ${uniquePrefix}criticalZ) {
          Logger.log('Pausing Ad ID: ' + ${uniquePrefix}currentAd.id + 
                     ' in Ad Group: ' + ${uniquePrefix}adGroup.getName() + 
                     '. CPC: ' + ${uniquePrefix}currentAd.cpc.toFixed(2) + 
                     ' vs Winner CPC: ' + ${uniquePrefix}winnerAd.cpc.toFixed(2) + 
                     '. Z-score: ' + ${uniquePrefix}zScore.toFixed(2) + ' (Critical Z: ' + ${uniquePrefix}criticalZ + ')');
          ${uniquePrefix}currentAd.adObject.pause();
          if (${uniquePrefix}CONFIG.AD_LABEL_NAME !== '') {
             var ${uniquePrefix}adLabel = AdsApp.adLabels().withCondition("Name = " + ${uniquePrefix}CONFIG.AD_LABEL_NAME).get().next();
             ${uniquePrefix}currentAd.adObject.applyLabel(${uniquePrefix}adLabel);
          }
          ${uniquePrefix}totalAdsPaused++;
          ${uniquePrefix}loserAdsFound++;
        }
      }
      if (${uniquePrefix}loserAdsFound > 0) {
         Logger.log('Paused ' + ${uniquePrefix}loserAdsFound + ' ad(s) in Ad Group: ' + ${uniquePrefix}adGroup.getName());
      }
    }
  }
  Logger.log('Script finished. Total Ad Groups Processed: ' + ${uniquePrefix}adGroupsProcessed + '. Total Ads Paused: ' + ${uniquePrefix}totalAdsPaused);
  if (${useTelegramVal}) {
    var ${uniquePrefix}account = AdsApp.currentAccount();
    var ${uniquePrefix}accountName = ${uniquePrefix}account.getName();
    var ${uniquePrefix}accountId = ${uniquePrefix}account.getCustomerId();
    var ${uniquePrefix}timestamp = Utilities.formatDate(new Date(), ${uniquePrefix}account.getTimeZone(), 'yyyy-MM-dd HH:mm:ss z');
    
    var ${uniquePrefix}message = '✅ Script Execution Finished Successfully' + '\\n' +
                   '------------------------------------' + '\\n' +
                   '📊 Script: Ad Performance Optimizer' + '\\n' +
                   '🏭 Account: ' + ${uniquePrefix}accountName + ' (' + ${uniquePrefix}accountId + ')' + '\\n' +
                   '📅 Date: ' + ${uniquePrefix}timestamp + '\\n' +
                   '📈 Results: ' + ${uniquePrefix}totalAdsPaused + ' ads paused across ' + ${uniquePrefix}adGroupsProcessed + ' ad groups' + '\\n' +
                   '------------------------------------';
    
    ${uniquePrefix}sendTelegramNotification(${uniquePrefix}message);
  }
}
`;
    return `${telegramCode}\n\n${mainScript}`;
  };

  const pageTitle = "Ad Performance Optimizer";
  const pageDescriptionString = "Automatically pause underperforming ads based on statistical significance. This script analyzes ad performance, identifies underperformers, and pauses them. Optional Telegram notifications available.";

  const howThisWorksContent = (
    <div className="space-y-4">
      <h3 className="text-xl font-semibold text-slate-200">How It Works</h3>
      <p>The script performs the following actions:</p>
      <ul className="list-disc list-inside space-y-1 text-slate-300">
        <li>Selects campaigns, optionally filtered by name.</li>
        <li>For each ad group, it fetches all enabled ads (text ads, expanded text ads, responsive search ads).</li>
        <li>Analyzes ads with sufficient data (min clicks & impressions) over the specified date range.</li>
        <li>Compares ads based on CTR and Conversion Rate. If Conversion Rate is the primary metric and one ad has zero conversions, it defaults to CTR.</li>
        <li>Uses statistical significance (Bayesian A/B testing approach) to determine if one ad is outperforming others.</li>
        <li>If a statistically significant underperformer is found, and its CPC isn't more than X% lower than the top performer's CPC, it's paused.</li>
        <li>Applies a specified label to paused ads for easy tracking.</li>
        <li>Optionally sends notifications via Telegram.</li>
      </ul>
      <h4 className="text-lg font-semibold text-slate-200 mt-3">Key Metrics for Comparison:</h4>
      <ul className="list-disc list-inside space-y-1 text-slate-300">
        <li><strong>Primary Metric:</strong> Conversion Rate (if available and significant for at least one ad in the group).</li>
        <li><strong>Secondary Metric:</strong> Click-Through Rate (CTR) (used if conversion data is insufficient or not primary).</li>
      </ul>
      <p className="mt-2 text-sm text-slate-400">
        <strong>Important:</strong> The script prioritizes ads with higher conversion rates. If an ad has a good conversion rate but a lower CTR, it might still be considered a top performer.
        The script uses a simple statistical model and does not account for all possible external factors. Always review changes made by scripts.
      </p>
    </div>
  );

  if (showResult) {
    return (
      <ToolPageLayout title={pageTitle} description={pageDescriptionString} howThisWorksContent={howThisWorksContent}>
        <div id="scriptDisplaySectionAdPerf" className="mt-8">
          <h3 className="text-2xl font-semibold text-slate-100 mb-6">Generated Script</h3>
          {message && <NotificationMessage type={message.type} message={message.text} />} 
          <ScriptDisplay scriptContent={generatedScript} />
          <StyledButton
            onClick={() => {
              setShowResult(false);
              setMessage(null);
            }}
            variant="outline"
            className="mt-8 w-full sm:w-auto"
            themeColor="slate"
            leftIcon={<PlayCircle className="mr-2 h-5 w-5" />}
          >
            Back to Form
          </StyledButton>
        </div>
      </ToolPageLayout>
    );
  }

  return (
    <ToolPageLayout title={pageTitle} description={pageDescriptionString} howThisWorksContent={howThisWorksContent}>
      {message && <NotificationMessage type={message.type} message={message.text} className="mb-6" />} 

      <form onSubmit={(e) => { e.preventDefault(); handleGenerateScript(); }} className="space-y-8 mt-8">
        <FormSection title="Campaign & Ad Settings" icon={<BarChart2 />} theme="blue">
          <FormItem label="Campaign Name Contains" htmlFor="campaignNameAdPerf" tooltipText="Optional: Only process campaigns whose names contain this text. Leave blank to include all campaigns.">
            <StyledInput type="text" id="campaignNameAdPerf" name="campaignName" value={campaignName} onChange={handleInputChange} placeholder="e.g., My Brand Campaign" />
          </FormItem>
          <FormItem label="Label for Paused Ads" htmlFor="adLabelAdPerf" tooltipText="This label will be applied to ads paused by the script. Useful for tracking." required>
            <StyledInput type="text" id="adLabelAdPerf" name="adLabel" value={adLabel} onChange={handleInputChange} placeholder="Paused by Ad Performance Script" />
          </FormItem>
        </FormSection>

        <FormSection title="Performance Thresholds & Analysis" icon={<Settings />} theme="amber">
          <FormItem label="Confidence Level" htmlFor="confidenceLevelAdPerf" tooltipText="Statistical confidence for determining significance (e.g., 95% means there's a 5% chance the observed difference is random)." required>
            <select id="confidenceLevelAdPerf" name="confidenceLevel" value={confidenceLevel} onChange={handleInputChange} className="w-full p-2.5 border border-slate-600 rounded-md shadow-sm focus:ring-2 focus:ring-sky-500 focus:border-sky-500 text-slate-100 bg-slate-700 hover:bg-slate-600">
              <option value="0.99">99% (Very Confident)</option>
              <option value="0.95">95% (Confident)</option>
              <option value="0.90">90% (Somewhat Confident)</option>
              <option value="0.85">85% (Less Confident)</option>
              <option value="0.80">80% (Minimally Confident)</option>
            </select>
          </FormItem>
          <FormItem label="Min Clicks (per Ad)" htmlFor="minClicksAdPerf" tooltipText="Minimum clicks an ad needs to be considered for analysis." required>
            <StyledInput type="number" id="minClicksAdPerf" name="minClicks" value={minClicks} onChange={handleInputChange} placeholder="100" min="0" />
          </FormItem>
          <FormItem label="Min Impressions (per Ad)" htmlFor="minImpressionsAdPerf" tooltipText="Minimum impressions an ad needs to be considered for analysis." required>
            <StyledInput type="number" id="minImpressionsAdPerf" name="minImpressions" value={minImpressions} onChange={handleInputChange} placeholder="1000" min="0" />
          </FormItem>
          <FormItem label="Date Range" htmlFor="dateRangeAdPerf" tooltipText="Time period for ad performance data analysis." required>
            <select id="dateRangeAdPerf" name="dateRange" value={dateRange} onChange={handleInputChange} className="w-full p-2.5 border border-slate-600 rounded-md shadow-sm focus:ring-2 focus:ring-sky-500 focus:border-sky-500 text-slate-100 bg-slate-700 hover:bg-slate-600">
              <option value="TODAY">Today</option>
              <option value="YESTERDAY">Yesterday</option>
              <option value="LAST_7_DAYS">Last 7 Days</option>
              <option value="LAST_14_DAYS">Last 14 Days</option>
              <option value="LAST_30_DAYS">Last 30 Days</option>
              <option value="THIS_MONTH">This Month</option>
              <option value="LAST_MONTH">Last Month</option>
              <option value="ALL_TIME">All Time</option>
            </select>
          </FormItem>
          <FormItem label="Max CPC Difference (%) for Pausing" htmlFor="maxCpcDifferenceAdPerf" tooltipText="Allow pausing an underperforming ad only if its CPC isn't more than X% lower than the top performer's CPC. Prevents pausing cheap, high-volume ads prematurely. E.g., 20 means an ad can be paused if its CPC is up to 20% cheaper than the winner." required>
            <StyledInput type="number" id="maxCpcDifferenceAdPerf" name="maxCpcDifference" value={maxCpcDifference} onChange={handleInputChange} placeholder="20" min="0" max="100" />
          </FormItem>
        </FormSection>

        <FormSection title="Telegram Notifications" icon={<MessageSquare />} theme="purple" collapsible initiallyCollapsed={!useTelegram && !telegramBotToken && !telegramChatId}>
          <div className="mb-4">
            <label className="flex items-center cursor-pointer group">
              <div className="relative">
                <input
                  type="checkbox"
                  id="useTelegramAdPerf"
                  name="useTelegram"
                  checked={useTelegram}
                  onChange={handleInputChange}
                  className="sr-only"
                />
                <div className={`
                  w-5 h-5 rounded border-2 flex items-center justify-center transition-all duration-200
                  ${useTelegram 
                    ? 'bg-sky-600 border-sky-600' 
                    : 'bg-slate-700/50 border-slate-500 group-hover:border-slate-400'}
                  }`}
                >
                  {useTelegram && (
                    <svg className="w-3 h-3 text-white" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                </div>
              </div>
              <span className="ml-3 text-gray-300 text-sm font-medium">
                Enable Telegram Notifications
              </span>
            </label>
          </div>
          {useTelegram && (
            <>
              <FormItem label="Telegram Bot Token" htmlFor="telegramBotTokenAdPerf" tooltipText="Your Telegram Bot Token." required={useTelegram}>
                <StyledInput type="text" id="telegramBotTokenAdPerf" name="telegramBotToken" value={telegramBotToken} onChange={handleInputChange} placeholder="123456:ABC-DEF1234ghIkl-zyx57W2v1u123ew11" />
              </FormItem>
              <FormItem label="Telegram Chat ID" htmlFor="telegramChatIdAdPerf" tooltipText="Your Telegram Chat ID." required={useTelegram}>
                <StyledInput type="text" id="telegramChatIdAdPerf" name="telegramChatId" value={telegramChatId} onChange={handleInputChange} placeholder="-1001234567890 or @channelname" />
              </FormItem>
            </>
          )}
        </FormSection>

        <div className="flex flex-col sm:flex-row gap-4 pt-4">
          <StyledButton type="submit" variant="primary" size="lg" themeColor="emerald" leftIcon={<PlayCircle className="mr-2 h-5 w-5" />}>
            Generate Script
          </StyledButton>
        </div>
      </form>
    </ToolPageLayout>
  );
};

export default AdPerformance;
