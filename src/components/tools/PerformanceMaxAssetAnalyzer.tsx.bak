import React, { useState } from 'react';
import { Copy, Check, HelpCircle, Info, Bell, Filter, CalendarDays, Mail, BarChartHorizontal, AlertTriangle } from 'lucide-react';

// Simple custom tooltip component
interface SimpleTooltipProps {
  content: string;
  children: React.ReactNode;
}

const SimpleTooltip: React.FC<SimpleTooltipProps> = ({ content, children }) => {
  return (
    <span className="group relative inline-flex">
      {children}
      <span className="absolute z-50 invisible group-hover:visible opacity-0 group-hover:opacity-100 transition-opacity duration-300 bottom-full left-1/2 -translate-x-1/2 mb-2 w-72 max-w-xs bg-neutral-800 text-white text-xs p-2 rounded-md shadow-lg">
        {content}
        <span className="absolute top-full left-1/2 -translate-x-1/2 w-0 h-0 border-l-[5px] border-l-transparent border-r-[5px] border-r-transparent border-t-[5px] border-t-neutral-800"></span>
      </span>
    </span>
  );
};

const GOOGLE_ADS_DATE_RANGES_PMAX = [
  'LAST_7_DAYS',
  'LAST_14_DAYS',
  'LAST_30_DAYS',
  'LAST_90_DAYS',
];

const PerformanceMaxAssetAnalyzer: React.FC = () => {
  // Form inputs
  const [campaignNamePattern, setCampaignNamePattern] = useState('');
  const [dateRange, setDateRange] = useState('LAST_30_DAYS');
  const [emailAddress, setEmailAddress] = useState('');

  // Telegram
  const [useTelegram, setUseTelegram] = useState(false);
  const [telegramBotToken, setTelegramBotToken] = useState('');
  const [telegramChatId, setTelegramChatId] = useState('');

  // UI State
  const [generatedScript, setGeneratedScript] = useState('');
  const [showResult, setShowResult] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);
  const [message, setMessage] = useState<{text: string, type: 'error' | 'success' | 'info'} | null>(null);

  const handleGenerateScript = () => {
    setMessage(null);

    if (!campaignNamePattern.trim()) {
        setMessage({ text: 'Campaign Name Pattern is required for Performance Max campaigns.', type: 'error' });
        return;
    }
    if (!emailAddress.trim() || !/\S+@\S+\.\S+/.test(emailAddress)) {
        setMessage({ text: 'Please enter a valid email address for notifications.', type: 'error' });
        return;
    }
    if (useTelegram && (!telegramBotToken || !telegramChatId)) {
      setMessage({ text: 'Please enter both Telegram Bot Token and Chat ID for Telegram notifications.', type: 'error' });
      return;
    }

    const script = generatePMaxAssetScript(
      campaignNamePattern,
      dateRange,
      emailAddress,
      useTelegram,
      telegramBotToken,
      telegramChatId
    );
    setGeneratedScript(script);
    setShowResult(true);
    setTimeout(() => {
      document.getElementById('resultSectionPMaxAsset')?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  };

  const handleCopyScript = () => {
    setMessage(null);
    navigator.clipboard.writeText(generatedScript).then(() => {
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    }).catch(err => {
      console.error('Could not copy text: ', err);
      setMessage({ text: 'Failed to copy script. Please try copying manually.', type: 'error' });
    });
  };

  const generatePMaxAssetScript = (
    campaignPattern: string, dateRangeVal: string, email: string,
    useTelegramVal: boolean, botToken: string, chatId: string
  ): string => {
    const uniquePrefix = 'pMaxAsset' + Date.now().toString(36) + '_';
    let telegramCode = '';
    if (useTelegramVal) {
      telegramCode = `
  function ${uniquePrefix}sendTelegramNotification(message) {
    var ${uniquePrefix}payload = {
      // eslint-disable-next-line no-useless-escape
      'chat_id': '${chatId.replace(/\\/g, '\\\\').replace(/'/g, "\\'")}',
      'text': message,
      'parse_mode': 'HTML'
    };
    var ${uniquePrefix}options = {
      'method': 'post',
      'contentType': 'application/json',
      'payload': JSON.stringify(${uniquePrefix}payload)
    };
    try {
      // eslint-disable-next-line no-useless-escape
      UrlFetchApp.fetch('https://api.telegram.org/bot${botToken.replace(/\\/g, '\\\\').replace(/'/g, "\\'")}/sendMessage', ${uniquePrefix}options);
      Logger.log('Telegram notification sent.');
    } catch (e) { Logger.log('Telegram Error: ' + e.toString()); }
  }
`;
    }

    const mainScript = `
function main() {
  var ${uniquePrefix}CONFIG = {
    // eslint-disable-next-line no-useless-escape
    CAMPAIGN_NAME_CONTAINS: "${campaignPattern.replace(/\\/g, '\\\\').replace(/"/g, '\\"')}",
    DATE_RANGE: "${dateRangeVal}",
    // eslint-disable-next-line no-useless-escape
    EMAIL_ADDRESS: "${email.replace(/\\/g, '\\\\').replace(/"/g, '\\"')}",
    PERFORMANCE_LABEL_TO_CHECK: 'LOW' // Can be 'LOW', 'GOOD', 'BEST', 'PENDING', 'UNSPECIFIED', 'UNKNOWN'
  };

  var ${uniquePrefix}lowPerformingAssets = [];
  var ${uniquePrefix}accountTimezone = AdsApp.currentAccount().getTimeZone();
  var ${uniquePrefix}formattedTime = Utilities.formatDate(new Date(), ${uniquePrefix}accountTimezone, 'yyyy-MM-dd HH:mm:ss');
  var ${uniquePrefix}emailSubject = 'Performance Max Low-Performing Assets Report - ' + ${uniquePrefix}formattedTime;
  var ${uniquePrefix}emailBody = 'Performance Max Asset Analyzer Script Run at: ' + ${uniquePrefix}formattedTime + '\n\n';
  ${uniquePrefix}emailBody += 'Report for Campaigns containing: "' + ${uniquePrefix}CONFIG.CAMPAIGN_NAME_CONTAINS + '"\n';
  ${uniquePrefix}emailBody += 'Date Range: ' + ${uniquePrefix}CONFIG.DATE_RANGE + '\n';
  ${uniquePrefix}emailBody += 'Finding assets with performance label: ' + ${uniquePrefix}CONFIG.PERFORMANCE_LABEL_TO_CHECK + '\n\n';

  Logger.log(${uniquePrefix}emailBody);

  var ${uniquePrefix}campaignSelector = AdsApp.performanceMaxCampaigns()
    .withCondition("campaign.name CONTAINS_IGNORE_CASE '" + ${uniquePrefix}CONFIG.CAMPAIGN_NAME_CONTAINS + "'")
    .withCondition('campaign.status = ENABLED');

  var ${uniquePrefix}campaignIterator = ${uniquePrefix}campaignSelector.get();

  if (!${uniquePrefix}campaignIterator.hasNext()) {
    ${uniquePrefix}emailBody += 'No active Performance Max campaigns found matching the pattern: "' + ${uniquePrefix}CONFIG.CAMPAIGN_NAME_CONTAINS + '".\n';
    Logger.log('No active Performance Max campaigns found matching the pattern: "' + ${uniquePrefix}CONFIG.CAMPAIGN_NAME_CONTAINS + '".');
  } else {
    ${uniquePrefix}emailBody += '--- Found Low-Performing Assets ---\n';
  }

  while (${uniquePrefix}campaignIterator.hasNext()) {
    var ${uniquePrefix}campaign = ${uniquePrefix}campaignIterator.next();
    var ${uniquePrefix}campaignName = ${uniquePrefix}campaign.getName();
    Logger.log('Processing PMax Campaign: ' + ${uniquePrefix}campaignName);
    ${uniquePrefix}emailBody += '\nCampaign: ' + ${uniquePrefix}campaignName + '\n';

    var ${uniquePrefix}query = "SELECT asset_group_asset.asset, asset_group_asset.performance_label, asset_group.name, asset.name, asset.type, asset.text_asset.text, asset.image_asset.full_size_image_url, asset.youtube_video_asset.youtube_video_id " +
                      "FROM asset_group_asset " +
                      "WHERE asset_group_asset.performance_label = '" + ${uniquePrefix}CONFIG.PERFORMANCE_LABEL_TO_CHECK + "' " +
                      "AND campaign.id = " + ${uniquePrefix}campaign.getId() + " " +
                      "AND segments.date DURING " + ${uniquePrefix}CONFIG.DATE_RANGE;
    
    var ${uniquePrefix}report = AdsApp.search(${uniquePrefix}query);
    var ${uniquePrefix}assetsFoundInCampaign = false;

    while (${uniquePrefix}report.hasNext()) {
      var ${uniquePrefix}row = ${uniquePrefix}report.next();
      ${uniquePrefix}assetsFoundInCampaign = true;

      var ${uniquePrefix}assetResourceName = ${uniquePrefix}row.assetGroupAsset.asset;
      var ${uniquePrefix}performanceLabel = ${uniquePrefix}row.assetGroupAsset.performanceLabel;
      var ${uniquePrefix}assetGroupName = ${uniquePrefix}row.assetGroup.name;
      var ${uniquePrefix}assetName = ${uniquePrefix}row.asset.name ? ${uniquePrefix}row.asset.name : 'N/A (Asset ID: ' + ${uniquePrefix}assetResourceName.split('/').pop() + ')';
      var ${uniquePrefix}assetType = ${uniquePrefix}row.asset.type;
      var ${uniquePrefix}assetContent = '';

      if (${uniquePrefix}assetType === 'TEXT' && ${uniquePrefix}row.asset.textAsset) {
        ${uniquePrefix}assetContent = ${uniquePrefix}row.asset.textAsset.text;
      } else if (${uniquePrefix}assetType === 'IMAGE' && ${uniquePrefix}row.asset.imageAsset) {
        ${uniquePrefix}assetContent = ${uniquePrefix}row.asset.imageAsset.fullSizeImageUrl ? 'Image URL: ' + ${uniquePrefix}row.asset.imageAsset.fullSizeImageUrl : 'Image (no URL)';
      } else if (${uniquePrefix}assetType === 'YOUTUBE_VIDEO' && ${uniquePrefix}row.asset.youtubeVideoAsset) {
        ${uniquePrefix}assetContent = 'YouTube Video ID: ' + ${uniquePrefix}row.asset.youtubeVideoAsset.youtubeVideoId;
      }

      var ${uniquePrefix}assetInfo = '  - Asset Group: ' + ${uniquePrefix}assetGroupName +
                      ' | Asset: ' + ${uniquePrefix}assetName +
                      ' | Type: ' + ${uniquePrefix}assetType +
                      ' | Perf Label: ' + ${uniquePrefix}performanceLabel +
                      (${uniquePrefix}assetContent ? ' | Content: ' + ${uniquePrefix}assetContent.substring(0,100) + (${uniquePrefix}assetContent.length > 100 ? '...' : '') : '');
      
      ${uniquePrefix}lowPerformingAssets.push({
        campaign: ${uniquePrefix}campaignName,
        assetGroup: ${uniquePrefix}assetGroupName,
        assetName: ${uniquePrefix}assetName,
        assetType: ${uniquePrefix}assetType,
        performanceLabel: ${uniquePrefix}performanceLabel,
        content: ${uniquePrefix}assetContent
      });
      ${uniquePrefix}emailBody += ${uniquePrefix}assetInfo + '\n';
      Logger.log('  Found Low Perf Asset: ' + ${uniquePrefix}assetInfo);
    }
    if(!${uniquePrefix}assetsFoundInCampaign){
        ${uniquePrefix}emailBody += '  No assets with performance label "' + ${uniquePrefix}CONFIG.PERFORMANCE_LABEL_TO_CHECK + '" found in this campaign for the selected period.\n';
    }
  }

  if (${uniquePrefix}lowPerformingAssets.length === 0 && ${uniquePrefix}campaignIterator.totalNumEntities() > 0) {
    ${uniquePrefix}emailBody += '\nNo assets with performance label "' + ${uniquePrefix}CONFIG.PERFORMANCE_LABEL_TO_CHECK + '" found across all processed campaigns for the selected period.\n';
  } else if (${uniquePrefix}lowPerformingAssets.length > 0){
    ${uniquePrefix}emailBody += '\n--- End of Report ---';
  }

  Logger.log('\n--- Email Body to be Sent ---');
  Logger.log(${uniquePrefix}emailBody);
  Logger.log('--- End of Email Body ---');

  if (${uniquePrefix}CONFIG.EMAIL_ADDRESS) {
    MailApp.sendEmail(${uniquePrefix}CONFIG.EMAIL_ADDRESS, ${uniquePrefix}emailSubject, ${uniquePrefix}emailBody);
    var ${uniquePrefix}notificationMessage = 'PMax Asset Analyzer: Report sent to ' + ${uniquePrefix}CONFIG.EMAIL_ADDRESS;
    Logger.log(${uniquePrefix}notificationMessage);
    ${useTelegramVal ? `${uniquePrefix}sendTelegramNotification('✅ PMax Asset Analyzer Script executed successfully.\nReport sent to: ${email}.\nCampaigns matching: "' + ${uniquePrefix}CONFIG.CAMPAIGN_NAME_CONTAINS + '".\nDate Range: ${dateRangeVal}.');` : ''}
  } else {
    Logger.log('No email address provided. Skipping email notification.');
    ${useTelegramVal ? `${uniquePrefix}sendTelegramNotification('⚠️ PMax Asset Analyzer Script executed.\nNo email address provided for report.\nCampaigns matching: "' + ${uniquePrefix}CONFIG.CAMPAIGN_NAME_CONTAINS + '".\nDate Range: ${dateRangeVal}.');` : ''}
  }
}

${telegramCode}
`;
    return mainScript;
  };

  return (
    <div className="p-4 md:p-6 bg-neutral-50 min-h-screen">
        <div className="max-w-4xl mx-auto bg-white p-6 md:p-8 rounded-xl shadow-xl">
          <div className="flex items-center mb-6 pb-4 border-b border-neutral-200">
            <BarChartHorizontal className="w-8 h-8 text-blue-600 mr-3" />
            <h1 className="text-2xl font-bold text-neutral-800">Performance Max Asset Analyzer</h1>
          </div>

        {/* Info Box */}
        <div className="mb-6 p-4 bg-sky-50 border border-sky-200 rounded-md text-sky-700">
          <div className="flex items-center">
            <Info className="w-5 h-5 mr-2 flex-shrink-0" />
            <p className="text-sm">
              This script identifies assets with a 'LOW' performance label in your Performance Max campaigns for a selected date range. It then sends a report of these assets to the specified email address and can optionally send a notification to a Telegram chat. This helps you quickly identify underperforming assets that may need to be paused, updated, or replaced.
            </p>
          </div>
        </div>

        {/* Campaign Settings */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-neutral-700 mb-3 flex items-center">
            <Filter className="w-5 h-5 mr-2 text-blue-500" />
            Campaign Settings
          </h2>
          <div className="mb-4">
            <label htmlFor="campaignNamePattern" className="block text-sm font-medium text-neutral-700 mb-1">
              Campaign Name
              <SimpleTooltip content="Enter a full or partial campaign name to target specific Performance Max campaigns. For example, entering 'Brand' will target all Performance Max campaigns with 'Brand' in their name.">
                <HelpCircle className="inline w-4 h-4 ml-1 text-neutral-500 cursor-help" />
              </SimpleTooltip>
            </label>
            <input
              type="text"
              id="campaignNamePattern"
              className="mt-1 block w-full px-3 py-2 bg-white border border-neutral-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors duration-150"
              placeholder="Enter Performance Max campaign name (e.g., PMax_Brand_Q4)"
              value={campaignNamePattern}
              onChange={(e) => setCampaignNamePattern(e.target.value)}
              required
            />
            <p className="mt-1 text-xs text-neutral-500">The script will find Performance Max campaigns whose names contain this text. Case-insensitive.</p>
          </div>
        </div>

        {/* Date Range Settings */}
        <div className="mb-6">
            <h2 className="text-xl font-semibold text-neutral-700 mb-3 flex items-center">
                <CalendarDays className="w-5 h-5 mr-2 text-blue-500" />
                Date Range for Analysis
            </h2>
            <div className="mb-4">
                <label htmlFor="dateRangePMaxAsset" className="block text-sm font-medium text-neutral-700 mb-1">
                    Select Date Range
                    <SimpleTooltip content="The time period used to analyze asset performance. Longer periods provide more data but may include older trends that are no longer relevant. Shorter periods are more current but have less data.">
                        <HelpCircle className="inline w-4 h-4 ml-1 text-neutral-500 cursor-help" />
                    </SimpleTooltip>
                </label>
                <select
                    id="dateRangePMaxAsset"
                    className="mt-1 block w-full px-3 py-2 bg-white border border-neutral-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors duration-150"
                    value={dateRange}
                    onChange={(e) => setDateRange(e.target.value)}
                >
                    {GOOGLE_ADS_DATE_RANGES_PMAX.map(range => (
                        <option key={range} value={range}>{range.replace(/_/g, ' ')}</option>
                    ))}
                </select>
                <p className="mt-1 text-xs text-neutral-500">Time period to analyze for asset performance. 'LOW' label is relative to other assets in the same asset group during this period.</p>
            </div>
        </div>

        {/* Email Notification Settings */}
        <div className="mb-6 pt-4 border-t border-neutral-200">
          <h2 className="text-xl font-semibold text-neutral-700 mb-3 flex items-center">
            <Mail className="w-5 h-5 mr-2 text-blue-500" />
            Email Notification
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className="inline w-4 h-4 ml-1 text-neutral-500 cursor-help" />
              </TooltipTrigger>
              <TooltipContent className="max-w-xs bg-neutral-800 text-white p-2 rounded-md shadow-lg text-xs">
                <p>Receive an email report with a list of low-performing assets. This is useful for regular monitoring and taking quick action.</p>
              </TooltipContent>
            </Tooltip>
          </h2>
          <div className="mb-4">
            <label htmlFor="emailAddressPMax" className="block text-sm font-medium text-neutral-700 mb-1">
                Email Address
                <SimpleTooltip content="Receive email notifications about low-performing assets that should be replaced.">
                    <HelpCircle className="inline w-4 h-4 ml-1 text-neutral-500 cursor-help" />
                </SimpleTooltip>
            </label>
            <input
                type="email"
                id="emailAddressPMax"
                className="mt-1 block w-full px-3 py-2 bg-white border border-neutral-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors duration-150"
                placeholder="Enter your email address (e.g., <EMAIL>)"
                value={emailAddress}
                onChange={(e) => setEmailAddress(e.target.value)}
                required
            />
            <p className="mt-1 text-xs text-neutral-500">The script will send a summary of 'LOW' performing assets to this email. Make sure it's a valid email you have access to.</p>
          </div>
        </div>

        {/* Telegram Notification Settings */}
        <div className="mb-6 pt-4 border-t border-neutral-200">
          <h2 className="text-xl font-semibold text-neutral-700 mb-3 flex items-center">
            <Bell className="w-5 h-5 mr-2 text-blue-500" />
            Telegram Notifications
            <SimpleTooltip content="Get instant alerts on Telegram when the script identifies low-performing assets. Ideal for quick updates and team collaboration.">
                <HelpCircle className="inline w-4 h-4 ml-1 text-neutral-500 cursor-help" />
            </SimpleTooltip>
          </h2>
          <div className="flex items-center mb-3">
            <input
              type="checkbox"
              id="enableTelegramPMaxAsset"
              className="h-4 w-4 text-blue-600 border-neutral-300 rounded focus:ring-blue-500 transition-colors duration-150"
              checked={useTelegram}
              onChange={(e) => setUseTelegram(e.target.checked)}
            />
            <label htmlFor="enableTelegramPMaxAsset" className="ml-2 block text-sm text-neutral-700">
              Enable Telegram notifications
            </label>
          </div>
                  value={telegramBotToken}
                  onChange={(e) => setTelegramBotToken(e.target.value)}
                />
              </div>
              <div>
                <label htmlFor="telegramChatIdPMaxAsset" className="block text-sm font-medium text-neutral-700 mb-1">
                    Telegram Chat ID
                    <SimpleTooltip content="To get your chat ID, message @userinfobot on Telegram. For a group chat ID, add your bot to the group and send a message in the group, then check the bot's update URL.">
                        <HelpCircle className="inline w-4 h-4 ml-1 text-neutral-500 cursor-help" />
                    </SimpleTooltip>
                </label>
                <input
                  type="text"
                  id="telegramChatIdPMaxAsset"
                  className="mt-1 block w-full px-3 py-2 bg-white border border-neutral-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors duration-150"
                  placeholder="Enter Telegram Chat ID (e.g., -1001234567890 or 12345678)"
                  value={telegramChatId}
                  onChange={(e) => setTelegramChatId(e.target.value)}
                />
              </div>
            </div>
          )}
        </div>

        {/* Notification Message Area */}
        {message && (
          <div
            className={`p-4 mb-6 text-sm rounded-lg transition-all duration-300 ease-in-out ${ 
              message.type === 'error' ? 'bg-red-100 border border-red-300 text-red-700' :
              message.type === 'success' ? 'bg-green-100 border border-green-300 text-green-700' :
              'bg-blue-100 border border-blue-300 text-blue-700'
            }`}
            role="alert"
          >
            <span className="font-semibold capitalize">{message.type}: </span> 
            {message.text}
          </div>
        )}

        {/* Submit Button */}
        <div className="mt-8 pt-6 border-t border-neutral-200">
          <button
            onClick={handleGenerateScript}
            className="w-full flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-all duration-150 ease-in-out text-base"
            aria-label="Generate Performance Max Asset Analyzer Script"
          >
            <BarChartHorizontal size={20} className="mr-2" />
            Generate Performance Max Asset Analyzer Script
          </button>
        </div>

        {showResult && (
          <>
            <div id="resultSectionPMaxAsset" className="mt-8 pt-6 border-t border-neutral-200">
              <h2 className="text-xl font-semibold text-neutral-700 mb-3 flex items-center">
                <Check className="w-5 h-5 mr-2 text-green-500" />
                Generated Script
              </h2>
              <div className="relative bg-neutral-800 text-sm text-neutral-100 p-4 rounded-md shadow-md">
                <button 
                  onClick={handleCopyScript}
                  className="absolute top-2 right-2 bg-neutral-700 hover:bg-neutral-600 text-white p-2 rounded-md text-xs transition-colors duration-150 flex items-center"
                  aria-label="Copy script"
                >
                  {copySuccess ? <Check size={14} className="mr-1 text-green-400"/> : <Copy size={14} className="mr-1"/>}
                  {copySuccess ? 'Copied!' : 'Copy'}
                </button>
                <pre className="whitespace-pre-wrap break-all scrollbar-thin scrollbar-thumb-neutral-600 scrollbar-track-neutral-800 max-h-96">{generatedScript}</pre>
              </div>
            </div>

            <div className="mt-8 pt-6 border-t border-neutral-200 p-6 bg-neutral-50 rounded-lg shadow">
              <div className="mb-4 p-3 bg-amber-50 border-l-4 border-amber-400 text-amber-800">
                <div className="flex items-start">
                  <AlertTriangle className="w-5 h-5 mr-2 flex-shrink-0 mt-0.5" />
                  <p className="text-sm">
                    <strong>Important:</strong> This script will analyze your Performance Max assets but won't make any changes to your account. It will only send notifications about low-performing assets.
                  </p>
                </div>
              </div>
              
              <h3 className="text-lg font-semibold text-neutral-700 mb-3">How to Use This Script</h3>
              <ol className="list-decimal list-inside space-y-2 text-sm text-neutral-600">
                <li>Review the script parameters to ensure they are correct for your analysis needs.</li>
                <li>Click "Generate Performance Max Asset Analyzer Script".</li>
                <li>Once the script appears, click "Copy Script".</li>
                <li>In your Google Ads account, navigate to <strong className="font-medium">Tools &amp; settings &gt; BULK ACTIONS &gt; Scripts</strong>.</li>
                <li>Click the blue '+' button to add a new script.</li>
                <li>Give your script a name (e.g., "Performance Max Low Asset Finder").</li>
                <li>Delete any existing code in the editor and paste the copied script.</li>
                <li>Click "Authorize". You'll need to grant permissions for the script to access your Ads data and send emails.</li>
                <li>(Recommended) Click "Preview" to run the script without making changes and check the logs for any errors or unexpected output. You should receive an email/Telegram message if configured.</li>
                <li>If the preview is satisfactory, click "Run" to execute the script.</li>
                <li>To automate this, you can set a schedule (e.g., daily, weekly) for the script to run.</li>
              </ol>

              <h4 className="text-md font-semibold text-neutral-700 mt-6 mb-2">Important Considerations:</h4>
              <ul className="list-disc list-inside space-y-1 text-sm text-neutral-600">
                  <li><strong>Performance Labels:</strong> 'LOW', 'GOOD', 'BEST', etc., are assigned by Google relative to other assets of the <strong className="font-medium">same type within the same asset group</strong>.</li>
                  <li>A 'LOW' label doesn't automatically mean an asset is bad or should be removed. It indicates underperformance compared to its peers. Consider its absolute contribution if any.</li>
                  <li>This script reports on assets with a 'LOW' performance label. You can modify the <code>PERFORMANCE_LABEL_TO_CHECK</code> variable in the script if you wish to check for 'GOOD', 'BEST', etc.</li>
                  <li>Asset performance can take time to stabilize. Avoid making decisions on very new assets or with very little data.</li>
                  <li>Use this report as a starting point for investigation and A/B testing asset variations.</li>
                  <li>The script identifies individual assets, not entire asset groups.</li>
              </ul>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default PerformanceMaxAssetAnalyzer;
