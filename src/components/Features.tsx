import React from 'react';
import { features } from '../data/features';
// import Card from './ui/Card'; // Removed unused import
import { <PERSON><PERSON>hart, Zap, Target, FileText, PiggyBank, LayoutGrid } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useLanguage } from '../contexts/LanguageContext';

const iconMap = {
  'pie-chart': <PERSON><PERSON><PERSON>,
  'zap': Zap,
  'target': Target,
  'file-text': FileText,
  'piggy-bank': PiggyBank,
  'layout-grid': LayoutGrid,
};

const Features: React.FC = () => {
  const { t } = useLanguage();

  return (
    <section className="py-28 bg-gradient-to-b from-gray-800 to-gray-900 relative overflow-hidden">
      <div className="absolute inset-0 bg-grid-white/[0.02] [mask-image:radial-gradient(ellipse_at_center,transparent_10%,black)]"></div>
      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            {t('homepage.features.title')}
          </h2>
          <p className="text-lg text-blue-100/80 max-w-2xl mx-auto">
            {t('homepage.features.subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature) => {
            const Icon = iconMap[feature.icon as keyof typeof iconMap];
            
            const featureContent = (
              <div 
                className="flex flex-col items-start transition-all duration-300 h-full p-8 rounded-xl border border-white/10 hover:border-blue-400/30 bg-white/5 backdrop-blur-sm hover:shadow-lg"
              >
                <div className="mb-4 p-3 bg-blue-600/20 rounded-lg">
                  <Icon className="h-6 w-6 text-blue-400" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-3">{feature.title}</h3>
                <p className="text-blue-100/70 text-sm">{feature.description}</p>
              </div>
            );

            if (feature.id === 7) { 
              return (
                <Link to="/dashboard/telegram-script-generator" key={feature.id} className="block h-full">
                  {featureContent}
                </Link>
              );
            }
            
            return (
              <div key={feature.id} className="h-full">
                {featureContent}
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default Features;