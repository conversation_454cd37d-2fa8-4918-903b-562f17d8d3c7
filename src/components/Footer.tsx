import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Bolt } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

const Footer: React.FC = () => {
  const { t } = useLanguage();
  const location = useLocation();

  // Don't render footer on the telegram script generator page
  if (location.pathname === '/dashboard/telegram-script-generator') {
    return null;
  }
  return (
    <footer className="w-full bg-gray-900 text-white py-12 border-t border-white/10">
      <div className="w-full max-w-7xl mx-auto px-4 md:px-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-12">
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center mb-4">
              <Bolt className="h-8 w-8 text-blue-400" />
              <span className="ml-2 text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-cyan-400">gAds</span>
            </div>
            <p className="text-blue-100/70 mb-6 max-w-md">
              {t('footer.description')}
            </p>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold text-white mb-4">{t('footer.products.title')}</h3>
            <ul className="space-y-3">
              <li><Link to="/features" className="text-blue-100/70 hover:text-white transition-colors">{t('footer.products.features')}</Link></li>
              <li><Link to="/services" className="text-blue-100/70 hover:text-white transition-colors">{t('footer.products.services')}</Link></li>
              <li><Link to="/pricing" className="text-blue-100/70 hover:text-white transition-colors">{t('footer.products.pricing')}</Link></li>
              <li><Link to="/blog" className="text-blue-100/70 hover:text-white transition-colors">{t('footer.products.blog')}</Link></li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold text-white mb-4">{t('footer.company.title')}</h3>
            <ul className="space-y-3 mb-6">
              <li><Link to="/about" className="text-blue-100/70 hover:text-white transition-colors">{t('footer.company.about')}</Link></li>
              <li><Link to="/careers" className="text-blue-100/70 hover:text-white transition-colors">{t('footer.company.careers')}</Link></li>
              <li><Link to="/contact" className="text-blue-100/70 hover:text-white transition-colors">{t('footer.company.contact')}</Link></li>
            </ul>
            <Link
              to="/portfolio"
              className="inline-block font-medium bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white px-5 py-2.5 rounded-md text-sm font-semibold transition-colors shadow-md hover:shadow-lg w-[80%] text-center"
            >
              {t('footer.company.book_service')}
            </Link>
          </div>
        </div>
        
        <div className="mt-16 pt-8 border-t border-white/10">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-blue-100/70"> {new Date().getFullYear()} gAds. {t('footer.copyright')}</p>
            <div className="flex space-x-6 mt-4 md:mt-0" data-component-name="Footer">
              <Link to="/privacy-policy" className="text-sm text-blue-100/70 hover:text-white transition-colors" data-component-name="Footer">
                {t('footer.legal.privacy')}
              </Link>
              <Link to="/terms-of-service" className="text-sm text-blue-100/70 hover:text-white transition-colors" data-component-name="Footer">
                {t('footer.legal.terms')}
              </Link>
              <Link to="/cookies-policy" className="text-sm text-blue-100/70 hover:text-white transition-colors" data-component-name="Footer">
                {t('footer.legal.cookies')}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;