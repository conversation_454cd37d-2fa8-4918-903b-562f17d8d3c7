import React from 'react';
import { useLanguage } from '../contexts/LanguageContext';

const TranslationTest: React.FC = () => {
  const { language, setLanguage, t, isLoading, error } = useLanguage();

  const testKeys = [
    'dashboard.title',
    'auth.login',
    'auth.email',
    'nav.dashboard',
    'nav.login',
    'common.loading'
  ];

  return (
    <div className="fixed bottom-4 right-4 bg-gray-800 text-white p-4 rounded-lg shadow-lg max-w-sm z-50">
      <h3 className="text-lg font-bold mb-2">🌍 Translation Test</h3>
      
      <div className="mb-2">
        <strong>Current Language:</strong> {language}
      </div>
      
      <div className="mb-2">
        <strong>Loading:</strong> {isLoading ? 'Yes' : 'No'}
      </div>
      
      {error && (
        <div className="mb-2 text-red-400">
          <strong>Error:</strong> {error}
        </div>
      )}
      
      <div className="mb-3">
        <button 
          onClick={() => setLanguage('en')}
          className={`mr-2 px-2 py-1 rounded text-xs ${language === 'en' ? 'bg-blue-600' : 'bg-gray-600'}`}
        >
          EN
        </button>
        <button 
          onClick={() => setLanguage('ua')}
          className={`px-2 py-1 rounded text-xs ${language === 'ua' ? 'bg-blue-600' : 'bg-gray-600'}`}
        >
          UA
        </button>
      </div>
      
      <div className="text-xs space-y-1">
        {testKeys.map(key => (
          <div key={key}>
            <strong>{key}:</strong> "{t(key)}"
          </div>
        ))}
      </div>
    </div>
  );
};

export default TranslationTest;
