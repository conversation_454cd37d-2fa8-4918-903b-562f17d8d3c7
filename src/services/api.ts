// API Client for gAds Backend
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
  timestamp?: string;
}

interface ApiError {
  error: string;
  message?: string;
  details?: any;
  timestamp?: string;
}

class ApiClient {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
    this.token = localStorage.getItem('auth_token');
    console.log('API Client initialized with baseURL:', this.baseURL);
  }

  setToken(token: string | null) {
    this.token = token;
    if (token) {
      localStorage.setItem('auth_token', token);
    } else {
      localStorage.removeItem('auth_token');
    }
  }

  getToken(): string | null {
    const token = this.token || localStorage.getItem('auth_token');
    console.log('🔑 ApiClient: Getting token:', !!token);
    return token;
  }

  private async request<T = any>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    const token = this.getToken();

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    try {
      console.log('Making API request to:', url, 'with config:', config);

      // Add timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      const response = await fetch(url, {
        ...config,
        signal: controller.signal
      });
      clearTimeout(timeoutId);

      console.log('Response status:', response.status, response.statusText);
      const data = await response.json();

      if (!response.ok) {
        const error: ApiError = data;
        throw new Error(error.message || error.error || 'API request failed');
      }

      return data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // Authentication methods
  async login(email: string, password: string) {
    console.log('Login attempt for:', email);
    const response = await this.request<{
      token: string;
      user: any;
      message: string;
    }>('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });

    console.log('Login response:', response);
    if (response.token) {
      this.setToken(response.token);
      console.log('Token saved:', response.token.substring(0, 20) + '...');
    }

    return response;
  }

  async logout() {
    try {
      await this.request('/auth/logout', { method: 'POST' });
    } finally {
      this.setToken(null);
    }
  }

  async verifyToken() {
    return this.request<{ valid: boolean; user: any }>('/auth/verify');
  }

  // Content methods
  async getContent(language: string, category?: string) {
    const params = new URLSearchParams();
    if (category) params.append('category', category);
    
    const endpoint = `/content/${language}${params.toString() ? `?${params.toString()}` : ''}`;
    return this.request<{
      language: string;
      category: string;
      content: Record<string, string>;
      count: number;
    }>(endpoint);
  }

  async getTranslations(language: string) {
    return this.request<Record<string, string>>(`/content/translations/${language}`);
  }

  async getContentByCategory(language: string, category: string) {
    return this.request<{
      language: string;
      category: string;
      content: Record<string, string>;
      count: number;
    }>(`/content/${language}/category/${category}`);
  }

  async getCategories() {
    return this.request<{
      categories: Array<{ category: string; key_count: number }>;
    }>('/content/meta/categories');
  }

  // User methods
  async getUserProfile() {
    return this.request<{
      user: any;
      preferences: Record<string, any>;
    }>('/user/profile');
  }

  async updateLanguage(language: string, previousLanguage?: string) {
    return this.request<{ message: string; language: string }>('/user/language', {
      method: 'PUT',
      body: JSON.stringify({ language, previousLanguage }),
    });
  }

  async updatePreferences(preferences: Record<string, any>) {
    return this.request<{ message: string; updatedKeys: string[] }>('/user/preferences', {
      method: 'PUT',
      body: JSON.stringify({ preferences }),
    });
  }

  async getUserSessions() {
    return this.request<{
      sessions: Array<any>;
      count: number;
    }>('/user/sessions');
  }

  async terminateSession(sessionId: string) {
    return this.request<{ message: string }>(`/user/sessions/${sessionId}`, {
      method: 'DELETE',
    });
  }

  // Activity methods
  async getMyActivities(options: {
    limit?: number;
    offset?: number;
    type?: string;
  } = {}) {
    const params = new URLSearchParams();
    if (options.limit) params.append('limit', options.limit.toString());
    if (options.offset) params.append('offset', options.offset.toString());
    if (options.type) params.append('type', options.type);

    const endpoint = `/activity/my${params.toString() ? `?${params.toString()}` : ''}`;
    return this.request<{
      activities: Array<any>;
      pagination: {
        limit: number;
        offset: number;
        total: number;
        hasMore: boolean;
      };
    }>(endpoint);
  }

  async getActivityStats() {
    return this.request<{
      totalActivities: number;
      recentActivity: any;
      activityTypes: Array<any>;
      dailyStats: Array<any>;
    }>('/activity/stats');
  }

  // Admin methods
  async getAllActivities(options: {
    limit?: number;
    offset?: number;
    userId?: string;
    type?: string;
  } = {}) {
    const params = new URLSearchParams();
    if (options.limit) params.append('limit', options.limit.toString());
    if (options.offset) params.append('offset', options.offset.toString());
    if (options.userId) params.append('userId', options.userId);
    if (options.type) params.append('type', options.type);

    const endpoint = `/activity/all${params.toString() ? `?${params.toString()}` : ''}`;
    return this.request<{
      activities: Array<any>;
      pagination: any;
    }>(endpoint);
  }

  async getSystemStats() {
    return this.request<{
      overall: any;
      activityByType: Array<any>;
      dailyActivity: Array<any>;
      mostActiveUsers: Array<any>;
    }>('/activity/system-stats');
  }

  async createContent(data: {
    key_name: string;
    category: string;
    translations: Record<string, string>;
    description?: string;
  }) {
    return this.request<{ message: string }>('/content', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }
}

// Create singleton instance
export const apiClient = new ApiClient();

// Export types
export type { ApiResponse, ApiError };
export default apiClient;
