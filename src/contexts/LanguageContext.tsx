import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { apiClient } from '../services/api';

export type Language = 'en' | 'ua';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
  isLoading: boolean;
  error: string | null;
  refreshContent: () => Promise<void>;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

const STORAGE_KEY = 'gads-language';

interface LanguageProviderProps {
  children: ReactNode;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const [language, setLanguageState] = useState<Language>(() => {
    const saved = localStorage.getItem(STORAGE_KEY);
    return (saved as Language) || 'en';
  });

  const [content, setContent] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fallback translations for when API is not available
  const fallbackTranslations: Record<Language, Record<string, string>> = {
    en: {
      'nav.dashboard': 'Dashboard',
      'nav.login': 'Login',
      'nav.logout': 'Logout',
      'auth.login': 'Login',
      'auth.email': 'Email',
      'auth.password': 'Password',
      'auth.loginButton': 'Sign In',
      'common.loading': 'Loading...',
      'common.error': 'Error',
      'common.success': 'Success',
      'language.english': 'English',
      'language.ukrainian': 'Українська',
    },
    ua: {
      'nav.dashboard': 'Панель керування',
      'nav.login': 'Вхід',
      'nav.logout': 'Вихід',
      'auth.login': 'Вхід',
      'auth.email': 'Електронна пошта',
      'auth.password': 'Пароль',
      'auth.loginButton': 'Увійти',
      'common.loading': 'Завантаження...',
      'common.error': 'Помилка',
      'common.success': 'Успішно',
      'language.english': 'English',
      'language.ukrainian': 'Українська',
    }
  };

  const fetchContent = async (lang: Language) => {
    try {
      setIsLoading(true);
      setError(null);

      console.log('🌍 Fetching content for language:', lang);
      const response = await apiClient.getContent(lang);
      console.log('✅ Content fetched successfully:', Object.keys(response.content).length, 'keys');
      setContent(response.content);
    } catch (err) {
      console.warn('❌ Failed to fetch content from API, using fallback:', err);
      setError('Failed to load translations');
      setContent(fallbackTranslations[lang] || fallbackTranslations.en);
    } finally {
      setIsLoading(false);
    }
  };

  const setLanguage = async (lang: Language) => {
    const previousLanguage = language;
    setLanguageState(lang);
    localStorage.setItem(STORAGE_KEY, lang);

    // Fetch new content
    await fetchContent(lang);

    // Update user preference in backend if authenticated
    try {
      const token = apiClient.getToken();
      if (token) {
        await apiClient.updateLanguage(lang, previousLanguage);
      }
    } catch (err) {
      console.warn('Failed to update language preference:', err);
    }
  };

  const t = (key: string): string => {
    const translation = content[key] || fallbackTranslations[language]?.[key] || fallbackTranslations.en[key] || key;
    if (key === 'dashboard.title' || key === 'auth.login') {
      console.log(`🔤 Translation for "${key}": "${translation}" (lang: ${language}, content keys: ${Object.keys(content).length})`);
    }
    return translation;
  };

  const refreshContent = async () => {
    await fetchContent(language);
  };

  useEffect(() => {
    document.documentElement.lang = language;
    fetchContent(language);
  }, []);

  return (
    <LanguageContext.Provider value={{
      language,
      setLanguage,
      t,
      isLoading,
      error,
      refreshContent
    }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
