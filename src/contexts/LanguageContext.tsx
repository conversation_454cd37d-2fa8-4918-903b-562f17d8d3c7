import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { apiClient } from '../services/api';

export type Language = 'en' | 'ua';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
  isLoading: boolean;
  error: string | null;
  refreshContent: () => Promise<void>;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

const STORAGE_KEY = 'gads-language';

interface LanguageProviderProps {
  children: ReactNode;
}

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const [language, setLanguageState] = useState<Language>(() => {
    const saved = localStorage.getItem(STORAGE_KEY);
    return (saved as Language) || 'en';
  });

  const [content, setContent] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fallback translations for when API is not available
  const fallbackTranslations: Record<Language, Record<string, string>> = {
    en: {
      'nav.dashboard': 'Dashboard',
      'nav.login': 'Login',
      'nav.logout': 'Logout',
      'auth.login': 'Login',
      'auth.email': 'Email',
      'auth.password': 'Password',
      'auth.loginButton': 'Sign In',
      'common.loading': 'Loading...',
      'common.error': 'Error',
      'common.success': 'Success',
      'language.english': 'English',
      'language.ukrainian': 'Українська',
      // Hero section
      'hero.badge': 'Now with AI-powered optimizations',
      'hero.title.line1': 'Supercharge Your',
      'hero.title.line2': 'Advertising Performance',
      'hero.subtitle': 'Advanced tools and automation to optimize your ad campaigns, save time, and maximize ROI across all major advertising platforms.',
      'hero.cta.button': 'Submit a Service Request',
      'hero.features.title': 'Powerful Features for',
      'hero.features.highlight': 'Smart Marketers',
      'hero.features.subtitle': 'Everything you need to optimize your ad campaigns and grow your business',
      'hero.features.analytics.title': 'Advanced Analytics',
      'hero.features.analytics.description': 'Real-time performance metrics and insights to make data-driven decisions.',
      'hero.features.ai.title': 'AI Optimization',
      'hero.features.ai.description': 'Automated bid adjustments and optimizations powered by machine learning.',
      'hero.features.automation.title': 'Time-Saving Automation',
      'hero.features.automation.description': 'Automate repetitive tasks and focus on strategy and growth.',
      'hero.features.security.title': 'Enterprise Security',
      'hero.features.security.description': 'Bank-grade security to keep your data safe and compliant.',
      'hero.contact.link': 'Contact Us',
    },
    ua: {
      'nav.dashboard': 'Панель керування',
      'nav.login': 'Вхід',
      'nav.logout': 'Вихід',
      'auth.login': 'Вхід',
      'auth.email': 'Електронна пошта',
      'auth.password': 'Пароль',
      'auth.loginButton': 'Увійти',
      'common.loading': 'Завантаження...',
      'common.error': 'Помилка',
      'common.success': 'Успішно',
      'language.english': 'English',
      'language.ukrainian': 'Українська',
      // Hero section
      'hero.badge': 'Тепер з AI-оптимізацією',
      'hero.title.line1': 'Підвищте Ефективність',
      'hero.title.line2': 'Вашої Реклами',
      'hero.subtitle': 'Передові інструменти та автоматизація для оптимізації ваших рекламних кампаній, економії часу та максимізації ROI на всіх основних рекламних платформах.',
      'hero.cta.button': 'Подати Запит на Послугу',
      'hero.features.title': 'Потужні Функції для',
      'hero.features.highlight': 'Розумних Маркетологів',
      'hero.features.subtitle': 'Все необхідне для оптимізації ваших рекламних кампаній та розвитку бізнесу',
      'hero.features.analytics.title': 'Розширена Аналітика',
      'hero.features.analytics.description': 'Метрики продуктивності в реальному часі та інсайти для прийняття рішень на основі даних.',
      'hero.features.ai.title': 'AI Оптимізація',
      'hero.features.ai.description': 'Автоматичні коригування ставок та оптимізація на основі машинного навчання.',
      'hero.features.automation.title': 'Автоматизація для Економії Часу',
      'hero.features.automation.description': 'Автоматизуйте рутинні завдання та зосередьтеся на стратегії та зростанні.',
      'hero.features.security.title': 'Корпоративна Безпека',
      'hero.features.security.description': 'Безпека банківського рівня для захисту ваших даних та відповідності вимогам.',
      'hero.contact.link': 'Зв\'яжіться з Нами',
    }
  };

  const fetchContent = async (lang: Language) => {
    try {
      setIsLoading(true);
      setError(null);

      console.log('🌍 Fetching content for language:', lang);
      const response = await apiClient.getContent(lang);
      console.log('✅ Content fetched successfully:', Object.keys(response.content).length, 'keys');
      setContent(response.content);
    } catch (err) {
      console.warn('❌ Failed to fetch content from API, using fallback:', err);
      setError('Failed to load translations');
      setContent(fallbackTranslations[lang] || fallbackTranslations.en);
    } finally {
      setIsLoading(false);
    }
  };

  const setLanguage = async (lang: Language) => {
    const previousLanguage = language;
    setLanguageState(lang);
    localStorage.setItem(STORAGE_KEY, lang);

    // Fetch new content
    await fetchContent(lang);

    // Update user preference in backend if authenticated
    try {
      const token = apiClient.getToken();
      if (token) {
        await apiClient.updateLanguage(lang, previousLanguage);
      }
    } catch (err) {
      console.warn('Failed to update language preference:', err);
    }
  };

  const t = (key: string): string => {
    const translation = content[key] || fallbackTranslations[language]?.[key] || fallbackTranslations.en[key] || key;
    return translation;
  };

  const refreshContent = async () => {
    await fetchContent(language);
  };

  useEffect(() => {
    document.documentElement.lang = language;
    fetchContent(language);
  }, [language]);

  return (
    <LanguageContext.Provider value={{
      language,
      setLanguage,
      t,
      isLoading,
      error,
      refreshContent
    }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
