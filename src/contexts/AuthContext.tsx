import { useState, useEffect, createContext, useContext, ReactNode } from 'react';
import { apiClient } from '../services/api';

interface User {
  id: string;
  email: string;
  role: 'admin' | 'user';
  preferredLanguage: 'en' | 'ua';
}

interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => void;
  error: string | null;
  isLoading: boolean;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if user is already logged in
    const checkAuth = async () => {
      console.log('🔐 AuthContext: Checking authentication...');

      // FORCE CLEAR TOKEN FOR DEBUGGING
      console.log('🧹 AuthContext: FORCE CLEARING TOKEN FOR DEBUGGING');
      apiClient.setToken(null);
      localStorage.removeItem('auth_token');

      const token = apiClient.getToken();
      console.log('🔐 AuthContext: Token found:', !!token);

      if (token) {
        try {
          console.log('🔐 AuthContext: Verifying token...');
          const response = await apiClient.verifyToken();
          console.log('🔐 AuthContext: Token verification response:', response);

          if (response.valid && response.user) {
            console.log('✅ AuthContext: User authenticated:', response.user.email);
            setUser(response.user);
            setIsAuthenticated(true);
          } else {
            console.log('❌ AuthContext: Token invalid, removing...');
            // Token is invalid, remove it
            apiClient.setToken(null);
            setIsAuthenticated(false);
            setUser(null);
          }
        } catch (error) {
          console.error('❌ AuthContext: Token verification failed:', error);
          apiClient.setToken(null);
          setIsAuthenticated(false);
          setUser(null);
        }
      } else {
        console.log('❌ AuthContext: No token found');
        setIsAuthenticated(false);
        setUser(null);
      }

      console.log('🔐 AuthContext: Setting loading to false');
      setIsLoading(false);
    };

    checkAuth();
  }, []);

  const login = async (email: string, password: string): Promise<boolean> => {
    setError(null);
    setIsLoading(true);
    
    try {
      const response = await apiClient.login(email, password);
      
      if (response.token && response.user) {
        setUser(response.user);
        setIsAuthenticated(true);
        return true;
      } else {
        setError('Login failed');
        return false;
      }
    } catch (error) {
      console.error('Login error:', error);
      setError(error instanceof Error ? error.message : 'Login failed');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    setIsLoading(true);
    try {
      await apiClient.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setUser(null);
      setIsAuthenticated(false);
      setError(null);
      setIsLoading(false);
    }
  };

  const refreshUser = async () => {
    if (!isAuthenticated) return;
    
    try {
      const response = await apiClient.getUserProfile();
      if (response.user) {
        setUser(response.user);
      }
    } catch (error) {
      console.error('Failed to refresh user:', error);
      // If refresh fails, user might need to re-login
      if (error instanceof Error && error.message.includes('401')) {
        logout();
      }
    }
  };

  return (
    <AuthContext.Provider value={{
      isAuthenticated,
      user,
      login,
      logout,
      error,
      isLoading,
      refreshUser
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useApiAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useApiAuth must be used within an AuthProvider');
  }
  return context;
};
