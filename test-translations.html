<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Translation Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { margin: 5px; padding: 10px 20px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🌍 gAds Supercharge - Translation Test</h1>
    
    <div>
        <button onclick="testEnglish()">Test English (EN)</button>
        <button onclick="testUkrainian()">Test Ukrainian (UA)</button>
        <button onclick="testBoth()">Test Both Languages</button>
    </div>
    
    <div id="results"></div>

    <script>
        const API_BASE = 'http://localhost:3001/api';
        
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function testLanguage(lang) {
            try {
                addResult(`🔄 Testing ${lang.toUpperCase()} translations...`);
                
                const response = await fetch(`${API_BASE}/content/${lang}`);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                
                addResult(`✅ API Response OK for ${lang.toUpperCase()}`, 'success');
                addResult(`📊 Content keys: ${Object.keys(data.content).length}`, 'info');
                
                // Test specific keys
                const testKeys = [
                    'dashboard.title',
                    'auth.login', 
                    'auth.email',
                    'nav.dashboard',
                    'tools.telegram.title'
                ];
                
                testKeys.forEach(key => {
                    const value = data.content[key];
                    if (value) {
                        addResult(`🔤 ${key}: "${value}"`, 'success');
                    } else {
                        addResult(`❌ Missing key: ${key}`, 'error');
                    }
                });
                
            } catch (error) {
                addResult(`❌ Error testing ${lang.toUpperCase()}: ${error.message}`, 'error');
            }
        }
        
        async function testEnglish() {
            clearResults();
            await testLanguage('en');
        }
        
        async function testUkrainian() {
            clearResults();
            await testLanguage('ua');
        }
        
        async function testBoth() {
            clearResults();
            addResult('🚀 Testing both languages...', 'info');
            await testLanguage('en');
            addResult('---', 'info');
            await testLanguage('ua');
            
            // Compare specific translations
            addResult('🔍 Comparing translations...', 'info');
            try {
                const [enResponse, uaResponse] = await Promise.all([
                    fetch(`${API_BASE}/content/en`),
                    fetch(`${API_BASE}/content/ua`)
                ]);
                
                const enData = await enResponse.json();
                const uaData = await uaResponse.json();
                
                const comparisons = [
                    { key: 'dashboard.title', en: 'Dashboard', ua: 'Панель керування' },
                    { key: 'auth.login', en: 'Login', ua: 'Вхід' },
                    { key: 'auth.email', en: 'Email', ua: 'Електронна пошта' },
                    { key: 'nav.dashboard', en: 'Dashboard', ua: 'Панель керування' }
                ];
                
                comparisons.forEach(comp => {
                    const enValue = enData.content[comp.key];
                    const uaValue = uaData.content[comp.key];
                    
                    if (enValue === comp.en && uaValue === comp.ua) {
                        addResult(`✅ ${comp.key}: EN="${enValue}" | UA="${uaValue}"`, 'success');
                    } else {
                        addResult(`❌ ${comp.key}: Expected EN="${comp.en}" UA="${comp.ua}", Got EN="${enValue}" UA="${uaValue}"`, 'error');
                    }
                });
                
            } catch (error) {
                addResult(`❌ Comparison failed: ${error.message}`, 'error');
            }
        }
        
        // Auto-test on load
        window.onload = () => {
            addResult('🌍 Translation Test Ready!', 'info');
            addResult('Backend should be running on http://localhost:3001', 'info');
            addResult('Frontend should be running on http://localhost:5173', 'info');
        };
    </script>
</body>
</html>
