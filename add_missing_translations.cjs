const { Pool } = require('pg');

const pool = new Pool({
  user: 'gads_user',
  host: 'localhost',
  database: 'gads_db',
  password: 'gads_password',
  port: 5432,
});

const missingTranslations = {
  // Performance Max translations
  'tools.performanceMax.title': 'Аналізатор ресурсів Performance Max',
  'tools.performanceMax.description': 'Генерує скрипт Google Ads для виявлення низькоефективних ресурсів (текст, зображення, відео) у кампаніях Performance Max на основі їх міток ефективності (наприклад, \'LOW\'). Сповіщає електронною поштою та опціонально через Telegram.',
  'tools.performanceMax.fields.campaignPattern': 'Шаблон назви кампанії',
  'tools.performanceMax.fields.dateRange': 'Діапазон дат',
  'tools.performanceMax.fields.emailAddress': 'Адреса електронної пошти',
  'tools.performanceMax.fields.telegramToken': 'Токен Telegram бота',
  'tools.performanceMax.fields.telegramChatId': 'ID чату Telegram',
  'tools.performanceMax.sections.campaign': 'Налаштування кампанії та дат',
  'tools.performanceMax.sections.notifications': 'Сповіщення',
  'tools.performanceMax.generated': 'Згенерований скрипт',
  'tools.performanceMax.howItWorks': 'Як це працює',
  
  // Device Bid Adjuster translations
  'tools.deviceBid.title': 'Коригувач ставок за пристроями Google Ads',
  'tools.deviceBid.description': 'Автоматично коригує ставки за пристроями на основі продуктивності CPC. Зменшує ставки для пристроїв з високим CPC та збільшує для пристроїв з низьким CPC для оптимізації витрат.',
  'tools.deviceBid.fields.campaignPattern': 'Шаблон назви кампанії',
  'tools.deviceBid.fields.mobileThreshold': 'Поріг CPC для мобільних',
  'tools.deviceBid.fields.desktopThreshold': 'Поріг CPC для десктопу',
  'tools.deviceBid.fields.tabletThreshold': 'Поріг CPC для планшетів',
  'tools.deviceBid.fields.adjustmentPercentage': 'Відсоток коригування',
  'tools.deviceBid.fields.dateRange': 'Діапазон дат',
  'tools.deviceBid.fields.telegramToken': 'Токен Telegram бота',
  'tools.deviceBid.fields.telegramChatId': 'ID чату Telegram',
  'tools.deviceBid.sections.campaign': 'Налаштування кампанії',
  'tools.deviceBid.sections.thresholds': 'Пороги CPC за пристроями',
  'tools.deviceBid.sections.notifications': 'Сповіщення Telegram',
  'tools.deviceBid.generated': 'Згенерований скрипт',
  'tools.deviceBid.howItWorks': 'Як це працює',
  
  // Common translations
  'common.generateScript': 'Генерувати скрипт',
  'common.backToForm': 'Назад до форми',
  'common.enableTelegram': 'Увімкнути сповіщення Telegram',
  'common.required': 'Обов\'язкове поле',
  'common.optional': 'Опціонально',
};

async function addTranslations() {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    for (const [key, value] of Object.entries(missingTranslations)) {
      // Check if content key exists
      let keyResult = await client.query(
        'SELECT id FROM content_keys WHERE key_name = $1',
        [key]
      );

      let keyId;
      if (keyResult.rows.length === 0) {
        // Insert new content key
        const insertKeyResult = await client.query(
          'INSERT INTO content_keys (key_name, category, description, created_at, updated_at) VALUES ($1, $2, $3, NOW(), NOW()) RETURNING id',
          [key, 'tools', `Translation for ${key}`]
        );
        keyId = insertKeyResult.rows[0].id;
      } else {
        keyId = keyResult.rows[0].id;
      }

      // Check if translation already exists
      const existingResult = await client.query(
        'SELECT id FROM content_translations WHERE content_key_id = $1 AND language_code = $2',
        [keyId, 'ua']
      );

      if (existingResult.rows.length === 0) {
        // Insert new translation
        await client.query(
          'INSERT INTO content_translations (content_key_id, language_code, translation_text, created_at, updated_at) VALUES ($1, $2, $3, NOW(), NOW())',
          [keyId, 'ua', value]
        );
        console.log(`Added translation: ${key} = ${value}`);
      } else {
        console.log(`Translation already exists: ${key}`);
      }
    }
    
    await client.query('COMMIT');
    console.log('All translations added successfully!');
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error adding translations:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

addTranslations();
