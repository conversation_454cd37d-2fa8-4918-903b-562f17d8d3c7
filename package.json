{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-tooltip": "^1.2.6", "canvas": "^3.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.56.4", "react-router-dom": "^7.6.0", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/node": "^22.15.20", "@types/react": "^18.3.21", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.6.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}